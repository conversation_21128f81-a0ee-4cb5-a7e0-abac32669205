"""
ChunkOperation - 分块相关的数据库操作类

业务聚合模式示例：
- 同时管理 ChunkDB 和 ChunkInfoDB 两张紧密相关的表
- 提供业务级别的操作方法
- 维护数据一致性
- 支持向量搜索功能
- 使用实体类封装数据操作
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import uuid4

from service import get_client, get_config
from .db_wrapper import DatabaseOperationWrapper
from ..entities.base_models import Chunk, ChunkInfo, DocEmbedding

logger = logging.getLogger(__name__)


class ChunkOperation:
    """
    分块业务操作类 - 业务聚合示例
    
    管理三张表：
    - doc_chunks (分块基础信息)
    - doc_chunks_info (分块详细信息)
    - doc_embeddings (向量数据)
    
    这是业务聚合模式的典型例子：
    三张表业务上紧密相关，应该作为一个聚合来操作
    """
    
    def __init__(self, rdb_client=None, vdb_client=None, embedding_client=None, 
                 rdb_config_path=None, vdb_config_path=None, embedding_config_path=None):
        """
        初始化分块操作类
        
        Args:
            rdb_client: 关系型数据库客户端，如果为None则自动从配置获取
            vdb_client: 向量数据库客户端，如果为None则自动从配置获取
            embedding_client: 嵌入模型客户端，如果为None则自动从配置获取
            rdb_config_path: 关系型数据库配置路径，用于覆盖默认配置
            vdb_config_path: 向量数据库配置路径，用于覆盖默认配置
            embedding_config_path: 嵌入模型配置路径，用于覆盖默认配置
        """
        # 客户端存储
        self._rdb_client = rdb_client
        self._vdb_client = vdb_client
        self._embedding_client = embedding_client
        
        # 配置路径存储
        self._rdb_config_path = rdb_config_path
        self._vdb_config_path = vdb_config_path
        self._embedding_config_path = embedding_config_path
        
        # 包装器缓存
        self._rdb_client_wrapper = None
        
        # 表名定义
        self.chunk_table = "doc_chunks"
        self.chunk_info_table = "doc_chunks_info"
        self.chunk_vdb_table = "doc_embeddings"
    
    async def _ensure_rdb_client(self):
        """确保关系型数据库客户端已初始化"""
        if self._rdb_client_wrapper is None:
            if self._rdb_client is None:
                if self._rdb_config_path is None:
                    cfg = await get_config()
                    self._rdb_config_path = cfg.knowledge.doc.rdb
                
                self._rdb_client = await get_client(self._rdb_config_path)
                logger.debug(f"从配置获取RDB客户端: {self._rdb_config_path}")
            
            self._rdb_client_wrapper = DatabaseOperationWrapper(self._rdb_client)
        
        return self._rdb_client_wrapper
    
    async def _ensure_vdb_client(self):
        """确保向量数据库客户端已初始化"""
        if self._vdb_client is None:
            if self._vdb_config_path is None:
                cfg = await get_config()
                self._vdb_config_path = cfg.knowledge.doc.vdb
            
            self._vdb_client = await get_client(self._vdb_config_path)
            logger.debug(f"从配置获取VDB客户端: {self._vdb_config_path}")
        
        return self._vdb_client
    
    async def _ensure_embedding_client(self):
        """确保嵌入模型客户端已初始化"""
        if self._embedding_client is None:
            if self._embedding_config_path is None:
                cfg = await get_config()
                self._embedding_config_path = cfg.knowledge.doc.embedding
            
            self._embedding_client = await get_client(self._embedding_config_path)
            logger.debug(f"从配置获取Embedding客户端: {self._embedding_config_path}")
        
        return self._embedding_client
        
    # ==========================================
    # 业务级别的复合操作 (推荐使用)
    # ==========================================
    
    async def create_chunk_with_info_and_vector(
        self,
        knowledge_id: str,
        doc_id: str,
        chapter_layer: Optional[str] = None,
        parent_id: Optional[str] = None,
        chunk_infos: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        创建分块及其详细信息，并生成向量入库 (完整的业务聚合操作，使用实体类和批量处理)
        
        Args:
            knowledge_id: 知识库ID
            doc_id: 文档ID
            chapter_layer: 章节层级信息
            parent_id: 父分块ID
            chunk_infos: 分块信息列表，格式：[{"info_type": "content", "info_value": "文本内容"}, ...]
            
        Returns:
            str: 创建的分块ID
        """
        try:
            chunk_id = str(uuid4())
            
            # 1. 创建分块实体并存储
            chunk_entity = Chunk(
                chunk_id=chunk_id,
                doc_id=doc_id,
                chapter_layer=chapter_layer,
                parent_id=parent_id,
                created_time=datetime.now(),
                updated_time=None,
                is_active=True
            )
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.chunk_table, [chunk_entity.to_dict()])
            if not success:
                raise Exception("创建分块基础记录失败")
            
            # 2. 批量处理分块信息和向量化
            if chunk_infos:
                chunk_info_entities = []
                vectorizable_infos = []
                
                # 创建ChunkInfo实体
                for info in chunk_infos:
                    chunk_info_entity = ChunkInfo(
                        chunk_info_id=str(uuid4()),
                        chunk_id=chunk_id,
                        info_type=info["info_type"],
                        info_value=info["info_value"],
                        created_time=datetime.now(),
                        updated_time=None,
                        is_active=True
                    )
                    chunk_info_entities.append(chunk_info_entity)
                    
                    # 收集需要向量化的信息 TODO 由用户决定是否需要向量化
                    vectorizable_infos.append(chunk_info_entity)
                
                # 3. 批量插入分块信息
                if chunk_info_entities:
                    info_data_list = [entity.to_dict() for entity in chunk_info_entities]
                    success = rdb_client.insert(self.chunk_info_table, info_data_list)
                    if not success:
                        # 如果分块信息创建失败，需要回滚分块记录
                        await self.delete_chunk(chunk_id)
                        raise Exception("创建分块详细信息失败")
                
                # 4. 批量生成和插入向量
                if vectorizable_infos:
                    vector_entities = await self._batch_generate_vectors(
                        knowledge_id, doc_id, vectorizable_infos
                    )
                    
                    if vector_entities:
                        await self._batch_insert_vectors(vector_entities)
                        logger.info(f"向量入库完成: chunk_id={chunk_id}, 成功={len(vector_entities)}/{len(vectorizable_infos)}")
                
                # 更新chunk实体的关联信息
                chunk_entity.chunk_infos = chunk_info_entities
            
            logger.info(f"分块创建成功: {chunk_id}")
            return chunk_id
            
        except Exception as e:
            logger.error(f"创建分块失败: {e}")
            raise Exception(f"创建分块失败: {str(e)}")
    
    async def _batch_generate_vectors(
        self, 
        knowledge_id: str, 
        doc_id: str, 
        chunk_infos: List[ChunkInfo]
    ) -> List[DocEmbedding]:
        """
        批量生成向量实体，分批处理（每批15个），防止一次embedding太多
        
        Args:
            knowledge_id: 知识库ID
            doc_id: 文档ID
            chunk_infos: 需要向量化的分块信息列表
            
        Returns:
            List[DocEmbedding]: 向量实体列表
        """
        if not chunk_infos:
            return []
        
        try:
            embedding_client = await self._ensure_embedding_client()
            batch_size = 15
            vector_entities = []
            current_time = datetime.now()
            total = len(chunk_infos)
            idx = 0

            while idx < total:
                batch_chunk_infos = chunk_infos[idx:idx+batch_size]
                texts = [info.info_value for info in batch_chunk_infos]
                try:
                    res = await embedding_client.ainvoke(texts=texts)
                except Exception as e:
                    logger.error(f"第{idx//batch_size+1}批向量生成请求失败: {e}")
                    idx += batch_size
                    continue

                if not res.embeddings or len(res.embeddings) != len(texts):
                    logger.error(f"第{idx//batch_size+1}批向量生成失败: 期望{len(texts)}个向量，实际{len(res.embeddings) if res.embeddings else 0}个")
                    idx += batch_size
                    continue

                for i, chunk_info in enumerate(batch_chunk_infos):
                    vector_entity = DocEmbedding(
                        knowledge_id=knowledge_id,
                        doc_id=doc_id,
                        chunk_id=chunk_info.chunk_id,
                        chunk_info_id=chunk_info.chunk_info_id,
                        info_type=chunk_info.info_type,
                        embedding=res.embeddings[i],
                        create_time=current_time,
                        update_time=current_time
                    )
                    if vector_entity.validate():
                        vector_entities.append(vector_entity)
                    else:
                        logger.warning(f"向量实体验证失败: chunk_info_id={chunk_info.chunk_info_id}")
                idx += batch_size

            logger.info(f"批量向量生成完成: {len(vector_entities)}/{len(chunk_infos)}")
            return vector_entities
            
        except Exception as e:
            logger.error(f"批量向量生成失败: {e}")
            return []
            
    async def _batch_insert_vectors(self, vector_entities: List[DocEmbedding]) -> int:
        """
        批量插入向量实体
        
        Args:
            vector_entities: 向量实体列表
            
        Returns:
            int: 成功插入的数量
        """
        if not vector_entities:
            return 0
        
        try:
            vdb_client = await self._ensure_vdb_client()
            vector_data_list = [entity.to_dict() for entity in vector_entities]
            # id为自增，不需要设置
            for vector_data in vector_data_list:
                vector_data.pop("id", None)
            
            # 尝试真正的批量插入
            try:
                await vdb_client.ainsert(self.chunk_vdb_table, vector_data_list)
                logger.info(f"批量向量插入成功: {len(vector_entities)}条")
                return len(vector_entities)
            except Exception as batch_error:
                logger.warning(f"批量插入失败，改为逐条插入: {batch_error}")
                
                # 批量失败时回退到逐条插入
                success_count = 0
                for i, vector_entity in enumerate(vector_entities):
                    try:
                        await vdb_client.ainsert(self.chunk_vdb_table, [vector_entity.to_dict()])
                        success_count += 1
                    except Exception as single_error:
                        logger.error(f"单条向量插入失败: chunk_info_id={vector_entity.chunk_info_id}, error={single_error}")
                
                logger.info(f"逐条向量插入完成: {success_count}/{len(vector_entities)}")
                return success_count
                
        except Exception as e:
            logger.error(f"向量插入失败: {e}")
            return 0
    
    async def create_chunk_with_info(
        self,
        doc_id: str,
        chapter_layer: Optional[str] = None,
        parent_id: Optional[str] = None,
        chunk_infos: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        创建分块及其详细信息 (业务聚合操作)
        
        Args:
            doc_id: 文档ID
            chapter_layer: 章节层级信息
            parent_id: 父分块ID
            chunk_infos: 分块信息列表，格式：[{"info_type": "content", "info_value": "文本内容"}, ...]
            
        Returns:
            str: 创建的分块ID
        """
        try:
            chunk_id = str(uuid4())
            
            # 1. 创建分块基础记录
            chunk_data = {
                "chunk_id": chunk_id,
                "doc_id": doc_id,
                "chapter_layer": chapter_layer,
                "parent_id": parent_id,
                "created_time": datetime.now(),
                "updated_time": None,
                "is_active": True
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.chunk_table, [chunk_data])
            if not success:
                raise Exception("创建分块基础记录失败")
            
            # 2. 创建分块详细信息记录
            if chunk_infos:
                info_data_list = []
                for info in chunk_infos:
                    info_data = {
                        "chunk_info_id": str(uuid4()),
                        "chunk_id": chunk_id,
                        "info_type": info["info_type"],
                        "info_value": info["info_value"],
                        "created_time": datetime.now(),
                        "updated_time": None,
                        "is_active": True
                    }
                    info_data_list.append(info_data)
                
                if info_data_list:
                    success = rdb_client.insert(self.chunk_info_table, info_data_list)
                    if not success:
                        # 如果分块信息创建失败，需要回滚分块记录
                        await self.delete_chunk(chunk_id)
                        raise Exception("创建分块详细信息失败")
            
            logger.info(f"分块创建成功: {chunk_id}")
            return chunk_id
            
        except Exception as e:
            logger.error(f"创建分块失败: {e}")
            raise Exception(f"创建分块失败: {str(e)}")
    
    async def get_chunk_with_info(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """
        获取分块及其完整信息 (业务聚合查询，返回实体数据)
        
        Args:
            chunk_id: 分块ID
            
        Returns:
            Optional[Dict[str, Any]]: 包含分块和信息的完整数据
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            
            # 1. 获取分块基础信息
            chunk_condition: Dict[str, Any] = {"chunk_id": chunk_id, "is_active": True}
            chunk_result = await rdb_client.aselect(
                table=self.chunk_table,
                condition=chunk_condition,
                limit=1
            )
            
            if not chunk_result:
                return None
            
            chunk_data = chunk_result[0]
            
            # 2. 获取分块详细信息
            info_condition: Dict[str, Any] = {"chunk_id": chunk_id, "is_active": True}
            info_result = await rdb_client.aselect(
                table=self.chunk_info_table,
                condition=info_condition,
                order_by=["created_time ASC"]
            )
            
            # 3. 组合数据
            chunk_data["chunk_infos"] = info_result
            
            logger.debug(f"查询到分块完整信息: {chunk_id}")
            return chunk_data
            
        except Exception as e:
            logger.error(f"查询分块完整信息失败: {e}")
            raise Exception(f"查询分块完整信息失败: {str(e)}")
    
    async def get_chunk_entity(self, chunk_id: str) -> Optional[Chunk]:
        """
        获取分块实体及其完整信息 (返回Chunk实体)
        
        Args:
            chunk_id: 分块ID
            
        Returns:
            Optional[Chunk]: 分块实体，包含关联的分块信息实体
        """
        try:
            chunk_data = await self.get_chunk_with_info(chunk_id)
            if not chunk_data:
                return None
            
            # 转换为Chunk实体
            chunk_entity = Chunk.from_dict(chunk_data)
            
            # 转换关联的分块信息为实体
            if chunk_data.get("chunk_infos"):
                chunk_info_entities = [
                    ChunkInfo.from_dict(info_data) 
                    for info_data in chunk_data["chunk_infos"]
                ]
                chunk_entity.chunk_infos = chunk_info_entities
            
            logger.debug(f"查询到分块实体: {chunk_id}")
            return chunk_entity
            
        except Exception as e:
            logger.error(f"查询分块实体失败: {e}")
            raise Exception(f"查询分块实体失败: {str(e)}")
    
    async def get_chunks_by_document(self, doc_id: str, get_chunk_info: bool = True) -> List[Dict[str, Any]]:
        """
        获取文档的所有分块及其信息 (业务聚合查询)
        
        Args:
            doc_id: 文档ID
            get_info: 是否获取分块信息
        Returns:
            List[Dict[str, Any]]: 分块列表，每个分块包含其详细信息
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            
            # 1. 获取文档的所有分块
            chunk_condition: Dict[str, Any] = {"doc_id": doc_id, "is_active": 1}
            chunks = await rdb_client.aselect(
                table=self.chunk_table,
                condition=chunk_condition,
                order_by=["created_time ASC"]
            )
            
            if not chunks:
                return []
            
            if get_chunk_info:
                # 2. 批量获取所有分块的详细信息
                for chunk in chunks:
                    info_condition: Dict[str, Any] = {
                        "chunk_id": chunk["chunk_id"], 
                        "is_active": True
                    }
                    chunk_infos = await rdb_client.aselect(
                        table=self.chunk_info_table,
                        condition=info_condition,
                        order_by=["created_time ASC"]
                    )
                    chunk["chunk_infos"] = chunk_infos
            
            logger.debug(f"查询到文档分块: {len(chunks)} 个 (doc_id: {doc_id})")
            return chunks
            
        except Exception as e:
            logger.error(f"查询文档分块失败: {e}")
            raise Exception(f"查询文档分块失败: {str(e)}")
    
    async def update_chunk_info(
        self,
        chunk_id: str,
        info_type: str,
        new_info_value: str
    ) -> bool:
        """
        更新分块的特定类型信息
        
        Args:
            chunk_id: 分块ID
            info_type: 信息类型
            new_info_value: 新的信息值
            
        Returns:
            bool: 更新是否成功
        """
        try:
            update_data = {
                "info_value": new_info_value,
                "updated_time": datetime.now()
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.update(
                table=self.chunk_info_table,
                filter={"chunk_id": chunk_id, "info_type": info_type, "is_active": True},
                data=update_data
            )
            
            if success:
                logger.info(f"分块信息更新成功: chunk_id={chunk_id}, info_type={info_type}")
            else:
                logger.warning(f"分块信息更新失败，可能不存在: chunk_id={chunk_id}, info_type={info_type}")
                
            return success
            
        except Exception as e:
            logger.error(f"更新分块信息失败: {e}")
            raise Exception(f"更新分块信息失败: {str(e)}")
    
    async def delete_chunk(self, chunk_id: str) -> bool:
        """
        删除分块及其所有信息 (业务聚合删除)
        
        Args:
            chunk_id: 分块ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            
            # 1. 软删除所有分块信息
            info_update_data = {
                "is_active": False,
                "updated_time": datetime.now()
            }
            
            rdb_client.update(
                table=self.chunk_info_table,
                filter={"chunk_id": chunk_id, "is_active": True},
                data=info_update_data
            )
            
            # 2. 软删除分块记录
            chunk_update_data = {
                "is_active": False,
                "updated_time": datetime.now()
            }
            
            success = rdb_client.update(
                table=self.chunk_table,
                filter={"chunk_id": chunk_id, "is_active": True},
                data=chunk_update_data
            )
            
            if success:
                logger.info(f"分块删除成功: {chunk_id}")
            else:
                logger.warning(f"分块删除失败，可能不存在: {chunk_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除分块失败: {e}")
            raise Exception(f"删除分块失败: {str(e)}")
    
    # ==========================================
    # 向量搜索功能
    # ==========================================
    
    async def search_similar_chunks(
        self,
        query_text: str,
        knowledge_id: Optional[str] = None,
        doc_id: Optional[str] = None,
        info_types: Optional[List[str]] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        基于向量相似度搜索分块信息
        
        Args:
            query_text: 待查询文本
            knowledge_id: 知识库ID过滤
            doc_id: 文档ID过滤
            info_types: 信息类型过滤，如 ["content", "summary", "title"]
            top_k: 返回最相似的前K个结果
            similarity_threshold: 相似度阈值
            
        Returns:
            List[Dict[str, Any]]: 相似的分块信息列表，包含相似度分数
        """
        try:
            # 1. 生成查询向量
            embedding_client = await self._ensure_embedding_client()
            res = await embedding_client.ainvoke(texts=[query_text])
            if not res.embeddings or len(res.embeddings) == 0:
                logger.error("查询文本向量生成失败")
                return []
            
            query_vector = res.embeddings[0]
            
            # 2. 执行向量搜索
            vdb_client = await self._ensure_vdb_client()
            # 构建表达式过滤条件
            expr_conditions = []
            if knowledge_id:
                expr_conditions.append(f"knowledge_id = '{knowledge_id}'")
            if doc_id:
                expr_conditions.append(f"doc_id = '{doc_id}'")
            if info_types:
                if len(info_types) == 1:
                    expr_conditions.append(f"info_type = '{info_types[0]}'")
                else:
                    info_type_list = "', '".join(info_types)
                    expr_conditions.append(f"info_type in ('{info_type_list}')")
            
            expr = " AND ".join(expr_conditions) if expr_conditions else None
            
            search_results = await vdb_client.asearch(
                collection_name=self.chunk_vdb_table,
                data=[query_vector],
                anns_field="embedding",
                param={"metric_type": "cosine"},
                limit=top_k,
                expr=expr,
                output_fields=["knowledge_id", "doc_id", "chunk_id", "chunk_info_id", "info_type", "create_time"]
            )
            
            # 3. 获取chunk_info详细信息
            enriched_results = []
            rdb_client = await self._ensure_rdb_client()
            
            # search_results 是 List[List[SearchResult]]，需要取第一个查询向量的结果
            if search_results and len(search_results) > 0:
                vector_results = search_results[0]
                for result in vector_results:
                    # SearchResult 对象有 id, distance, entity 属性
                    chunk_info_id = result.entity.data.get("chunk_info_id")
                    if chunk_info_id:
                        # 获取分块信息详情
                        info_condition = {"chunk_info_id": chunk_info_id, "is_active": True}
                        info_details = rdb_client.select(
                            table=self.chunk_info_table,
                            condition=info_condition,
                            limit=1
                        )
                        
                        if info_details:
                            info_detail = info_details[0]
                            enriched_result = {
                                "chunk_info_id": chunk_info_id,
                                "chunk_id": result.entity.data.get("chunk_id"),
                                "doc_id": result.entity.data.get("doc_id"),
                                "knowledge_id": result.entity.data.get("knowledge_id"),
                                "info_type": result.entity.data.get("info_type"),
                                "info_value": info_detail.get("info_value"),
                                "similarity_score": 1.0 - result.distance,  # 转换距离为相似度
                                "create_time": info_detail.get("created_time"),
                                "vector_create_time": result.entity.data.get("create_time")
                            }
                            # 过滤相似度阈值
                            if enriched_result["similarity_score"] >= similarity_threshold:
                                enriched_results.append(enriched_result)
            
            logger.info(f"向量搜索完成: 查询='{query_text}', 结果数={len(enriched_results)}")
            return enriched_results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            raise Exception(f"向量搜索失败: {str(e)}")
    
    async def search_similar_chunks_by_chunk_info_id(
        self,
        chunk_info_id: str,
        knowledge_id: Optional[str] = None,
        doc_id: Optional[str] = None,
        info_types: Optional[List[str]] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        exclude_self: bool = True
    ) -> List[Dict[str, Any]]:
        """
        基于已有分块信息的向量搜索相似内容
        
        Args:
            chunk_info_id: 源分块信息ID
            knowledge_id: 知识库ID过滤
            doc_id: 文档ID过滤
            info_types: 信息类型过滤
            top_k: 返回最相似的前K个结果
            similarity_threshold: 相似度阈值
            exclude_self: 是否排除自身
            
        Returns:
            List[Dict[str, Any]]: 相似的分块信息列表
        """
        try:
            # 1. 获取源向量 - 修复方法调用
            vdb_client = await self._ensure_vdb_client()
            
            source_vectors = await vdb_client.aquery(
                collection_name=self.chunk_vdb_table,
                expr=f"chunk_info_id = '{chunk_info_id}'",
                output_fields=["embedding"],
                limit=1
            )
            
            if not source_vectors:
                logger.error(f"未找到源分块信息的向量: {chunk_info_id}")
                return []
            
            query_vector = source_vectors[0].data.get("embedding")
            if not query_vector:
                logger.error(f"源向量数据为空: {chunk_info_id}")
                return []
            
            # 处理从PostgreSQL返回的向量格式转换
            if isinstance(query_vector, str):
                try:
                    # PostgreSQL vector类型查询回来是字符串格式，如 "[0.1,0.2,0.3]"
                    # 需要解析成Python列表
                    import json
                    # 移除方括号并解析
                    vector_str = query_vector.strip("[]")
                    query_vector = [float(x.strip()) for x in vector_str.split(",")]
                except (ValueError, json.JSONDecodeError) as e:
                    logger.error(f"向量格式解析失败: {chunk_info_id}, vector={query_vector}, error={e}")
                    return []
            
            # 2. 准备搜索条件
            expr_conditions = []
            if knowledge_id:
                expr_conditions.append(f"knowledge_id = '{knowledge_id}'")
            if doc_id:
                expr_conditions.append(f"doc_id = '{doc_id}'")
            if info_types:
                if len(info_types) == 1:
                    expr_conditions.append(f"info_type = '{info_types[0]}'")
                else:
                    info_type_list = "', '".join(info_types)
                    expr_conditions.append(f"info_type in ('{info_type_list}')")
            
            expr = " AND ".join(expr_conditions) if expr_conditions else None
            
            # 3. 执行向量搜索
            search_results = await vdb_client.asearch(
                collection_name=self.chunk_vdb_table,
                data=[query_vector],
                anns_field="embedding",
                param={"metric_type": "cosine"},
                limit=top_k + (1 if exclude_self else 0),  # 多取一个以便排除自身
                expr=expr,
                output_fields=["knowledge_id", "doc_id", "chunk_id", "chunk_info_id", "info_type", "create_time"]
            )
            
            # 4. 处理搜索结果
            final_results = []
            if search_results and len(search_results) > 0:
                vector_results = search_results[0]
                for result in vector_results:
                    result_chunk_info_id = result.entity.data.get("chunk_info_id")
                    
                    # 排除自身（如果需要）
                    if exclude_self and result_chunk_info_id == chunk_info_id:
                        continue
                    
                    similarity_score = 1.0 - result.distance
                    if similarity_score < similarity_threshold:
                        continue
                        
                    final_results.append(result)
                    if len(final_results) >= top_k:
                        break
            
            # 5. 获取详细信息
            enriched_results = []
            rdb_client = await self._ensure_rdb_client()
            
            for result in final_results:
                # 获取分块信息详情
                result_chunk_info_id = result.entity.data.get("chunk_info_id")
                info_condition = {"chunk_info_id": result_chunk_info_id, "is_active": True}
                info_details = rdb_client.select(
                    table=self.chunk_info_table,
                    condition=info_condition,
                    limit=1
                )
                
                if info_details:
                    info_detail = info_details[0]
                    enriched_result = {
                        "chunk_info_id": result_chunk_info_id,
                        "chunk_id": result.entity.data.get("chunk_id"),
                        "doc_id": result.entity.data.get("doc_id"),
                        "knowledge_id": result.entity.data.get("knowledge_id"),
                        "info_type": result.entity.data.get("info_type"),
                        "info_value": info_detail.get("info_value"),
                        "similarity_score": 1.0 - result.distance,
                        "create_time": info_detail.get("created_time"),
                        "vector_create_time": result.entity.data.get("create_time")
                    }
                    enriched_results.append(enriched_result)
            
            logger.info(f"基于分块向量搜索完成: 源={chunk_info_id}, 结果数={len(enriched_results)}")
            return enriched_results
            
        except Exception as e:
            logger.error(f"基于分块向量搜索失败: {e}")
            raise Exception(f"基于分块向量搜索失败: {str(e)}")
    
    async def add_vector_for_chunk_info(
        self,
        knowledge_id: str,
        chunk_info_id: str,
        force_regenerate: bool = False
    ) -> bool:
        """
        为已存在的分块信息生成并存储向量
        
        Args:
            knowledge_id: 知识库ID
            chunk_info_id: 分块信息ID
            force_regenerate: 是否强制重新生成向量
            
        Returns:
            bool: 是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            
            # 1. 查询分块信息
            info_condition: Dict[str, Any] = {"chunk_info_id": chunk_info_id, "is_active": True}
            info_result = rdb_client.select(
                table=self.chunk_info_table,
                condition=info_condition,
                limit=1
            )
            
            if not info_result:
                logger.error(f"未找到分块信息: {chunk_info_id}")
                return False
            
            info_data = info_result[0]
            chunk_id = info_data["chunk_id"]
            info_type = info_data["info_type"]
            info_value = info_data["info_value"]
            
            # 2. 查询关联的文档ID
            chunk_condition: Dict[str, Any] = {"chunk_id": chunk_id, "is_active": True}
            chunk_result = rdb_client.select(
                table=self.chunk_table,
                condition=chunk_condition,
                limit=1
            )
            
            if not chunk_result:
                logger.error(f"未找到分块: {chunk_id}")
                return False
            
            doc_id = chunk_result[0]["doc_id"]
            
            # 3. 检查是否已存在向量（如果不强制重新生成）
            if not force_regenerate:
                vdb_client = await self._ensure_vdb_client()
                existing_vectors = await vdb_client.aquery(
                    table=self.chunk_vdb_table,
                    condition={"chunk_info_id": chunk_info_id},
                    limit=1
                )
                if existing_vectors:
                    logger.info(f"向量已存在，跳过生成: chunk_info_id={chunk_info_id}")
                    return True
            
            # 4. 生成向量
            if info_type not in ["content", "summary", "title"]:
                logger.info(f"信息类型 {info_type} 不需要向量化")
                return True
            
            embedding_client = await self._ensure_embedding_client()
            res = await embedding_client.ainvoke(texts=[info_value])
            if not res.embeddings or len(res.embeddings) == 0:
                logger.error(f"向量生成失败: chunk_info_id={chunk_info_id}")
                return False
            
            # 5. 存储向量
            vector_data = {
                "knowledge_id": knowledge_id,
                "doc_id": doc_id,
                "chunk_id": chunk_id,
                "chunk_info_id": chunk_info_id,
                "info_type": info_type,
                "embedding": res.embeddings[0],
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }
            
            vdb_client = await self._ensure_vdb_client()
            await vdb_client.ainsert(self.chunk_vdb_table, [vector_data])
            logger.info(f"向量生成并存储成功: chunk_info_id={chunk_info_id}")
            return True
                
        except Exception as e:
            logger.error(f"向量生成和存储失败: chunk_info_id={chunk_info_id}, error={e}")
            return False

    async def batch_generate_vectors_for_document(
        self,
        knowledge_id: str,
        doc_id: str,
        info_types: List[str] = ["content", "summary", "title"]
    ) -> Dict[str, Any]:
        """
        为文档的所有分块批量生成向量 (使用实体类和真正的批量处理)
        
        Args:
            knowledge_id: 知识库ID
            doc_id: 文档ID
            info_types: 需要向量化的信息类型列表
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            # 1. 获取文档的所有分块信息
            chunks = await self.get_chunks_by_document(doc_id)
            
            # 2. 收集需要向量化的ChunkInfo实体
            vectorizable_infos = []
            
            for chunk in chunks:
                chunk_infos = chunk.get("chunk_infos", [])
                for info_data in chunk_infos:
                    if info_data["info_type"] in info_types:
                        chunk_info = ChunkInfo.from_dict(info_data)
                        vectorizable_infos.append(chunk_info)
            
            total_infos = len(vectorizable_infos)
            
            if total_infos == 0:
                return {
                    "doc_id": doc_id,
                    "total_infos": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "vector_count": 0
                }
            
            # 3. 批量生成向量实体
            vector_entities = await self._batch_generate_vectors(
                knowledge_id, doc_id, vectorizable_infos
            )
            
            # 4. 批量插入向量
            success_count = 0
            if vector_entities:
                success_count = await self._batch_insert_vectors(vector_entities)
            
            failed_count = total_infos - success_count
            
            result = {
                "doc_id": doc_id,
                "total_infos": total_infos,
                "success_count": success_count,
                "failed_count": failed_count,
                "vector_count": len(vector_entities)
            }
            
            logger.info(f"文档批量向量化完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"批量向量生成失败: doc_id={doc_id}, error={e}")
            return {
                "doc_id": doc_id,
                "total_infos": 0,
                "success_count": 0,
                "failed_count": 0,
                "vector_count": 0,
                "error": str(e)
            }

    # ==========================================
    # 基础操作方法 (内部使用或特殊场景)
    # ==========================================
    
    async def create_chunk_basic(
        self,
        doc_id: str,
        chapter_layer: Optional[str] = None,
        parent_id: Optional[str] = None
    ) -> str:
        """创建基础分块记录 (不包含详细信息)"""
        try:
            chunk_id = str(uuid4())
            chunk_data = {
                "chunk_id": chunk_id,
                "doc_id": doc_id,
                "chapter_layer": chapter_layer,
                "parent_id": parent_id,
                "created_time": datetime.now(),
                "updated_time": None,
                "is_active": True
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.chunk_table, [chunk_data])
            
            if success:
                logger.info(f"基础分块创建成功: {chunk_id}")
                return chunk_id
            else:
                raise Exception("数据库插入操作失败")
                
        except Exception as e:
            logger.error(f"创建基础分块失败: {e}")
            raise Exception(f"创建基础分块失败: {str(e)}")
    
    async def add_chunk_info(
        self,
        chunk_id: str,
        info_type: str,
        info_value: str
    ) -> str:
        """为已存在的分块添加信息"""
        try:
            chunk_info_id = str(uuid4())
            info_data = {
                "chunk_info_id": chunk_info_id,
                "chunk_id": chunk_id,
                "info_type": info_type,
                "info_value": info_value,
                "created_time": datetime.now(),
                "updated_time": None,
                "is_active": True
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.chunk_info_table, [info_data])
            
            if success:
                logger.info(f"分块信息添加成功: {chunk_info_id}")
                return chunk_info_id
            else:
                raise Exception("数据库插入操作失败")
                
        except Exception as e:
            logger.error(f"添加分块信息失败: {e}")
            raise Exception(f"添加分块信息失败: {str(e)}")


async def main():
    """测试函数 - 重点测试chunk info带embedding的插入和查询"""
    try:
        print("📚 ChunkOperation 配置化使用示例 - 重点测试向量功能")
        print("=" * 60)
        
        # 创建操作实例（自动从配置获取所有客户端）
        chunk_operation = ChunkOperation()
        
        # 测试数据
        knowledge_id = "test-kb-001"
        doc_id = "test-doc-001"
        
        print("\n=== 1. 创建分块及其信息（包含向量化） ===")
        chunk_infos = [
            {"info_type": "content", "info_value": "这是一段关于人工智能的技术文档，详细介绍了机器学习的基本概念和应用场景"},
            {"info_type": "summary", "info_value": "人工智能和机器学习基础概念介绍"},
            {"info_type": "title", "info_value": "AI与机器学习概述"},
            {"info_type": "keywords", "info_value": "人工智能,机器学习,深度学习,神经网络"}
        ]
        
        chunk_id = await chunk_operation.create_chunk_with_info_and_vector(
            knowledge_id=knowledge_id,
            doc_id=doc_id,
            chapter_layer="第一章.第一节",
            chunk_infos=chunk_infos
        )
        print(f"✅ 创建分块成功，ID: {chunk_id}")
        
        print("\n=== 2. 创建第二个分块用于测试搜索 ===")
        chunk_infos_2 = [
            {"info_type": "content", "info_value": "深度学习是机器学习的一个重要分支，它使用多层神经网络来模拟人脑的学习过程"},
            {"info_type": "summary", "info_value": "深度学习和神经网络介绍"},
            {"info_type": "title", "info_value": "深度学习技术详解"},
            {"info_type": "keywords", "info_value": "深度学习,神经网络,反向传播,卷积网络"}
        ]
        
        chunk_id_2 = await chunk_operation.create_chunk_with_info_and_vector(
            knowledge_id=knowledge_id,
            doc_id=doc_id,
            chapter_layer="第一章.第二节",
            chunk_infos=chunk_infos_2
        )
        print(f"✅ 创建第二个分块成功，ID: {chunk_id_2}")
        
        print("\n=== 3. 创建第三个分块（不同主题） ===")
        chunk_infos_3 = [
            {"info_type": "content", "info_value": "云计算是一种通过互联网提供计算资源的技术，包括存储、计算能力和应用服务"},
            {"info_type": "summary", "info_value": "云计算技术和服务模式"},
            {"info_type": "title", "info_value": "云计算服务介绍"},
            {"info_type": "keywords", "info_value": "云计算,分布式计算,虚拟化,SaaS,PaaS,IaaS"}
        ]
        
        chunk_id_3 = await chunk_operation.create_chunk_with_info_and_vector(
            knowledge_id=knowledge_id,
            doc_id=doc_id,
            chapter_layer="第二章.第一节",
            chunk_infos=chunk_infos_3
        )
        print(f"✅ 创建第三个分块成功，ID: {chunk_id_3}")
        
        print("\n=== 4. 查询分块完整信息 ===")
        chunk_data = await chunk_operation.get_chunk_with_info(chunk_id)
        if chunk_data:
            print(f"✅ 分块信息: ID={chunk_data['chunk_id']}, 信息数量={len(chunk_data.get('chunk_infos', []))}")
            for info in chunk_data.get('chunk_infos', []):
                print(f"   - {info['info_type']}: {info['info_value'][:50]}...")
        
        print("\n=== 5. 基于文本的向量搜索 ===")
        query_text = "神经网络和深度学习"
        search_results = await chunk_operation.search_similar_chunks(
            query_text=query_text,
            knowledge_id=knowledge_id,
            info_types=["content", "summary", "title"],
            top_k=3,
            similarity_threshold=0.3
        )
        
        print(f"✅ 查询文本: '{query_text}'")
        print(f"✅ 搜索结果: {len(search_results)} 个")
        for idx, result in enumerate(search_results, 1):
            print(f"   {idx}. 分块ID: {result['chunk_id']}")
            print(f"      信息类型: {result['info_type']}")
            print(f"      相似度: {result['similarity_score']:.4f}")
            print(f"      内容: {result['info_value'][:80]}...")
            print()
        
        print("\n=== 6. 基于已有分块的向量搜索 ===")
        # 获取第一个分块的第一个内容信息ID
        chunk_data = await chunk_operation.get_chunk_with_info(chunk_id)
        if chunk_data and chunk_data.get('chunk_infos'):
            source_chunk_info_id = None
            for info in chunk_data['chunk_infos']:
                if info['info_type'] == 'content':
                    source_chunk_info_id = info['chunk_info_id']
                    break
            
            if source_chunk_info_id:
                similar_results = await chunk_operation.search_similar_chunks_by_chunk_info_id(
                    chunk_info_id=source_chunk_info_id,
                    knowledge_id=knowledge_id,
                    info_types=["content", "summary"],
                    top_k=3,
                    similarity_threshold=0.3,
                    exclude_self=True
                )
                
                print(f"✅ 基于分块信息ID: {source_chunk_info_id}")
                print(f"✅ 相似结果: {len(similar_results)} 个")
                for idx, result in enumerate(similar_results, 1):
                    print(f"   {idx}. 分块ID: {result['chunk_id']}")
                    print(f"      信息类型: {result['info_type']}")
                    print(f"      相似度: {result['similarity_score']:.4f}")
                    print(f"      内容: {result['info_value'][:80]}...")
                    print()
        
        print("\n=== 7. 查询文档的所有分块 ===")
        doc_chunks = await chunk_operation.get_chunks_by_document(doc_id)
        print(f"✅ 文档分块列表: {len(doc_chunks)} 个分块")
        for chunk in doc_chunks:
            print(f"   - 分块ID: {chunk['chunk_id']}")
            print(f"     章节: {chunk.get('chapter_layer', 'N/A')}")
            print(f"     信息数: {len(chunk.get('chunk_infos', []))}")
        
        print("\n=== 8. 测试单独的向量生成 ===")
        # 创建一个不带向量的分块信息
        basic_chunk_id = await chunk_operation.create_chunk_basic(
            doc_id=doc_id,
            chapter_layer="测试章节"
        )
        
        basic_info_id = await chunk_operation.add_chunk_info(
            chunk_id=basic_chunk_id,
            info_type="content",
            info_value="这是一个测试分块，用于验证后补向量生成功能"
        )
        
        # 为这个分块信息生成向量
        vector_success = await chunk_operation.add_vector_for_chunk_info(
            knowledge_id=knowledge_id,
            chunk_info_id=basic_info_id,
            force_regenerate=False
        )
        print(f"✅ 后补向量生成: {'成功' if vector_success else '失败'}")
        
        print("\n=== 9. 批量向量生成测试 ===")
        batch_result = await chunk_operation.batch_generate_vectors_for_document(
            knowledge_id=knowledge_id,
            doc_id=doc_id
        )
        print(f"✅ 批量向量化结果:")
        print(f"   总信息数: {batch_result['total_infos']}")
        print(f"   成功数: {batch_result['success_count']}")
        print(f"   失败数: {batch_result['failed_count']}")
        print(f"   向量数: {batch_result['vector_count']}")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！ChunkOperation 配置化功能正常")
        
        print("\n💡 主要功能验证:")
        print("1. ✅ 内化客户端管理 - 自动获取RDB、VDB、Embedding客户端")
        print("2. ✅ 配置驱动 - 通过 knowledge.doc.* 配置动态获取客户端")
        print("3. ✅ 分块信息创建 - 支持同时创建分块、信息和向量")
        print("4. ✅ 向量搜索 - 基于文本和已有向量的相似度搜索")
        print("5. ✅ 批量处理 - 支持文档级别的批量向量生成")
        print("6. ✅ 后补向量 - 为已有信息后续生成向量")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    asyncio.run(main())
