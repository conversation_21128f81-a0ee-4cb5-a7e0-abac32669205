"""
Metadata系统码值和关联CRUD操作

包含以下实体的CRUD操作：
- 码值集 (md_reference_code_set)
- 码值 (md_reference_code_value)
- 码值关联 (md_reference_code_relation)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudCodes(MetadataCrudBase):
    """Metadata系统码值和关联CRUD操作"""

    # ==================== 码值集操作 ====================

    async def create_code_set(self, code_set_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建码值集

        Args:
            code_set_data: 码值集数据

        Returns:
            (码值集ID, 向量创建结果列表)
        """
        try:
            # 数据验证
            MetadataUtils.validate_code_set_data(code_set_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where={'knowledge_id': code_set_data['knowledge_id'], 'code_set_name': code_set_data['code_set_name']},
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"码值集已存在: {code_set_data['code_set_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(code_set_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                data=[code_set_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"创建码值集失败: {error_msg}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where={'knowledge_id': code_set_data['knowledge_id'], 'code_set_name': code_set_data['code_set_name']},
                limit=1
            )

            code_set_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 码值集本身不创建向量，只有码值才创建向量
            vector_results = []

            logger.info(f"码值集创建成功: {code_set_data['code_set_name']} (ID: {code_set_id})")
            return code_set_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建码值集失败: {e}")
            raise MetadataError(f"创建码值集失败: {e}")

    async def get_code_set(self, code_set_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_code_set(self, code_set_data: Dict[str, Any], code_set_id: int = None, **where_conditions) -> bool:
        """更新码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(code_set_data, is_update=True)

        try:
            updates = [{"data": code_set_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新码值集失败: {e}")
            raise MetadataError(f"更新码值集失败: {e}")

    async def delete_code_set(self, code_set_id: int = None, **where_conditions) -> bool:
        """删除码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他删除条件")

        try:
            # 先删除相关码值的向量
            if self.vdb_client and code_set_id:
                try:
                    # 删除该码值集下所有码值的向量
                    collection_name = MetadataVectorCollections.MD_CODE_EMBEDDINGS
                    delete_expr = f"code_set_id = {code_set_id}"
                    await self.vdb_client.abatch_delete(
                        collection_name=collection_name,
                        exprs=[delete_expr]
                    )
                except Exception as e:
                    logger.warning(f"删除码值集相关向量失败: {e}")

            # 删除码值集记录（MySQL会自动级联删除相关码值和关联记录）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除码值集失败: {e}")
            raise MetadataError(f"删除码值集失败: {e}")

    async def list_code_sets(
        self,
        knowledge_id: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询码值集列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 码值操作 ====================

    async def create_code_value(self, code_value_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建码值"""
        try:
            # 数据验证
            MetadataUtils.validate_code_value_data(code_value_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where={
                    'knowledge_id': code_value_data['knowledge_id'],
                    'code_set_id': code_value_data['code_set_id'],
                    'code_value': code_value_data['code_value']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"码值已存在: {code_value_data['code_value']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(code_value_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                data=[code_value_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"创建码值失败: {error_msg}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where={
                    'knowledge_id': code_value_data['knowledge_id'],
                    'code_set_id': code_value_data['code_set_id'],
                    'code_value': code_value_data['code_value']
                },
                limit=1
            )

            code_value_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 处理向量化（只有码值才创建向量）
            vector_results = []
            if self.vdb_client and self.embedding_client and code_value_id:
                try:
                    vector_results = await self._create_vectors('code_value', code_value_id, code_value_data)
                except Exception as e:
                    logger.warning(f"码值向量化失败: {e}")

            logger.info(f"码值创建成功: {code_value_data['code_value']} (ID: {code_value_id})")
            return code_value_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建码值失败: {e}")
            raise MetadataError(f"创建码值失败: {e}")

    async def get_code_value(self, code_value_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_code_value(self, code_value_data: Dict[str, Any], code_value_id: int = None, **where_conditions) -> bool:
        """更新码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(code_value_data, is_update=True)

        try:
            updates = [{"data": code_value_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and code_value_id:
                    try:
                        await self._update_vectors('code_value', code_value_id, code_value_data)
                    except Exception as e:
                        logger.warning(f"码值向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新码值失败: {e}")
            raise MetadataError(f"更新码值失败: {e}")

    async def delete_code_value(self, code_value_id: int = None, **where_conditions) -> bool:
        """删除码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他删除条件")

        try:
            # 先删除向量
            if self.vdb_client and code_value_id:
                await self._delete_vectors('code_value', code_value_id)

            # 删除码值记录
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除码值失败: {e}")
            raise MetadataError(f"删除码值失败: {e}")

    async def list_code_values(
        self,
        knowledge_id: Optional[str] = None,
        code_set_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询码值列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            code_set_id=code_set_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==========================================
    # 码值关联 (Code Relation) CRUD 操作
    # ==========================================

    async def create_code_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建码值关联

        Args:
            relation_data: 码值关联数据

        Returns:
            Tuple[int, List[Dict[str, Any]]]: (关联ID, 向量化结果列表)
        """
        try:
            # 数据验证
            MetadataUtils.validate_code_relation_data(relation_data)

            # 检查重复
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where={
                    'column_id': relation_data['column_id'],
                    'code_set_id': relation_data['code_set_id']
                },
                limit=1
            )

            if existing:
                raise MetadataConflictError(f"码值关联已存在: 字段ID {relation_data['column_id']} 与码值集ID {relation_data['code_set_id']}")

            # 插入数据
            relation_data['create_time'] = datetime.now()
            relation_data['update_time'] = datetime.now()

            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success or result.affected_rows == 0:
                raise MetadataError("创建码值关联失败: 未知错误")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where={
                    'column_id': relation_data['column_id'],
                    'code_set_id': relation_data['code_set_id']
                },
                limit=1
            )

            relation_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 向量化处理
            vector_results = []
            if self.embedding_client:
                try:
                    vector_result = await self._vectorize_record(
                        table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                        record_id=relation_id,
                        record_data=relation_data,
                        collection_name=MetadataVectorCollections.CODE_RELATION
                    )
                    if vector_result:
                        vector_results.append(vector_result)
                except Exception as e:
                    logger.warning(f"码值关联向量化失败: {e}")

            logger.info(f"码值关联创建成功: 字段ID{relation_data['column_id']} -> 码值集ID{relation_data['code_set_id']} (ID: {relation_id})")
            return relation_id, vector_results

        except Exception as e:
            logger.error(f"创建码值关联失败: {e}")
            raise MetadataError(f"创建码值关联失败: {e}")

    async def get_code_relation(self, relation_id: Optional[int] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        获取码值关联信息

        Args:
            relation_id: 关联ID（主键查询）
            **kwargs: 其他查询条件

        Returns:
            Optional[Dict[str, Any]]: 码值关联信息
        """
        try:
            if relation_id:
                # 主键查询
                records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where={'id': relation_id},
                    limit=1
                )
            else:
                # 条件查询
                where_conditions = {}
                for key, value in kwargs.items():
                    if value is not None:
                        where_conditions[key] = value

                if not where_conditions:
                    return None

                records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where=where_conditions,
                    limit=1
                )

            return records[0] if records else None

        except Exception as e:
            logger.error(f"获取码值关联失败: {e}")
            return None

    async def update_code_relation(self, update_data: Dict[str, Any], relation_id: int) -> bool:
        """
        更新码值关联信息

        Args:
            update_data: 更新数据
            relation_id: 关联ID

        Returns:
            bool: 更新是否成功
        """
        try:
            update_data['update_time'] = datetime.now()

            where = {'id': relation_id}
            updates = [{"data": update_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            success = result.success and result.affected_rows > 0
            if success:
                logger.info(f"码值关联更新成功: ID {relation_id}")

            return success

        except Exception as e:
            logger.error(f"更新码值关联失败: {e}")
            return False

    async def delete_code_relation(self, relation_id: int) -> bool:
        """
        删除码值关联

        Args:
            relation_id: 关联ID

        Returns:
            bool: 删除是否成功
        """
        try:
            where = {'id': relation_id}
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            success = result.success and result.affected_rows > 0
            if success:
                logger.info(f"码值关联删除成功: ID {relation_id}")

            return success

        except Exception as e:
            logger.error(f"删除码值关联失败: {e}")
            return False

    async def list_code_relations(self, knowledge_id: Optional[str] = None,
                                 code_set_id: Optional[int] = None,
                                 column_id: Optional[int] = None,
                                 column_type: Optional[str] = None,
                                 limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        列出码值关联

        Args:
            knowledge_id: 知识库ID
            code_set_id: 码值集ID
            column_id: 字段ID
            column_type: 字段类型
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 码值关联列表
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if code_set_id:
                where_conditions['code_set_id'] = code_set_id
            if column_id:
                where_conditions['column_id'] = column_id
            if column_type:
                where_conditions['column_type'] = column_type

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"列出码值关联失败: {e}")
            return []

    async def batch_create_code_relations(self, relations_data: List[Dict[str, Any]]) -> List[int]:
        """
        批量创建码值关联

        Args:
            relations_data: 码值关联数据列表

        Returns:
            List[int]: 创建的关联ID列表
        """
        try:
            # 数据验证
            for relation_data in relations_data:
                MetadataUtils.validate_code_relation_data(relation_data)

            # 添加时间戳
            for relation_data in relations_data:
                relation_data['create_time'] = datetime.now()
                relation_data['update_time'] = datetime.now()

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                data=relations_data,
                batch_size=len(relations_data),
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError("批量创建码值关联失败")

            # 获取插入的ID（简化处理）
            relation_ids = []
            for relation_data in relations_data:
                inserted_records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where={
                        'column_id': relation_data['column_id'],
                        'code_set_id': relation_data['code_set_id']
                    },
                    limit=1
                )
                relation_id = inserted_records[0].get('id', 0) if inserted_records else 0
                relation_ids.append(relation_id)

            logger.info(f"批量创建码值关联成功: {len(relation_ids)} 个")
            return relation_ids

        except Exception as e:
            logger.error(f"批量创建码值关联失败: {e}")
            raise MetadataError(f"批量创建码值关联失败: {e}")

    async def search_code_relations(self, search_term: str, knowledge_id: Optional[str] = None,
                                   limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值关联

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id

            # 简化搜索：按备注字段搜索
            if search_term:
                where_conditions['comment'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值关联失败: {e}")
            return []

    async def search_code_sets(self, search_term: str, knowledge_id: Optional[str] = None,
                              limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值集

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id

            # 简化搜索：按码值集名称搜索
            if search_term:
                where_conditions['code_set_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值集失败: {e}")
            return []

    async def search_code_values(self, search_term: str, knowledge_id: Optional[str] = None,
                                code_set_id: Optional[int] = None,
                                limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            code_set_id: 码值集ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if code_set_id:
                where_conditions['code_set_id'] = code_set_id

            # 简化搜索：按码值搜索
            if search_term:
                where_conditions['code_value'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值失败: {e}")
            return []

    # ==================== 批量操作方法 ====================

    async def batch_create_code_sets(
        self,
        code_sets_data: List[Dict[str, Any]],
        batch_size: int = 500,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建参考码值集

        Args:
            code_sets_data: 码值集数据列表
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数
            timeout_per_batch: 每批次超时时间

        Returns:
            (码值集ID列表, 向量创建结果列表)
        """
        if not code_sets_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for code_set_data in code_sets_data:
                MetadataUtils.validate_code_set_data(code_set_data)
                MetadataUtils.add_timestamps(code_set_data)
                processed_data.append(code_set_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                data=processed_data,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建参考码值集失败: {error_msg}")

            # 获取插入的ID
            code_set_ids = []
            if processed_data:
                # 构建批量查询条件
                where_conditions = []
                for code_set_data in processed_data:
                    where_conditions.append({
                        'knowledge_id': code_set_data['knowledge_id'],
                        'code_set_name': code_set_data['code_set_name']
                    })

                # 批量查询获取ID
                batch_results = await self.rdb_client.abatch_query(
                    table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                    queries=[{
                        "table": MetadataTableNames.MD_REFERENCE_CODE_SET,
                        "data": ["id"],
                        "filters": condition,
                        "limit": 1
                    } for condition in where_conditions],
                    batch_size=batch_size,
                    max_concurrency=max_concurrency,
                    timeout_per_batch=timeout_per_batch
                )

                for query_response in batch_results:
                    if hasattr(query_response, 'data') and query_response.data:
                        code_set_ids.append(query_response.data[0]['id'])
                    else:
                        code_set_ids.append(0)

            # 码值集不需要向量化，只有码值需要
            vector_results = [[] for _ in code_set_ids]

            logger.info(f"批量创建参考码值集成功: {len(code_set_ids)} 个码值集")
            return code_set_ids, vector_results

        except Exception as e:
            logger.error(f"批量创建参考码值集失败: {e}")
            raise MetadataError(f"批量创建参考码值集失败: {e}")

    async def batch_create_code_values(
        self,
        code_values_data: List[Dict[str, Any]],
        batch_size: int = 500,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建参考码值

        Args:
            code_values_data: 码值数据列表
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数
            timeout_per_batch: 每批次超时时间

        Returns:
            (码值ID列表, 向量创建结果列表)
        """
        if not code_values_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for code_value_data in code_values_data:
                MetadataUtils.validate_code_value_data(code_value_data)
                MetadataUtils.add_timestamps(code_value_data)
                processed_data.append(code_value_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                data=processed_data,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建参考码值失败: {error_msg}")

            # 获取插入的ID
            code_value_ids = []
            all_vector_results = []

            if processed_data:
                # 构建批量查询条件
                where_conditions = []
                for code_value_data in processed_data:
                    where_conditions.append({
                        'knowledge_id': code_value_data['knowledge_id'],
                        'code_set_id': code_value_data['code_set_id'],
                        'code_value': code_value_data['code_value']
                    })

                # 批量查询获取ID
                batch_results = await self.rdb_client.abatch_query(
                    table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                    queries=[{
                        "table": MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                        "data": ["id"],
                        "filters": condition,
                        "limit": 1
                    } for condition in where_conditions],
                    batch_size=batch_size,
                    max_concurrency=max_concurrency,
                    timeout_per_batch=timeout_per_batch
                )

                for i, query_response in enumerate(batch_results):
                    if hasattr(query_response, 'data') and query_response.data:
                        code_value_id = query_response.data[0]['id']
                        code_value_ids.append(code_value_id)

                        # 为码值创建向量
                        try:
                            vector_results = await self._create_vectors('code_value', code_value_id, processed_data[i])
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"码值向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        code_value_ids.append(0)
                        all_vector_results.append([])

            logger.info(f"批量创建参考码值成功: {len(code_value_ids)} 个码值")
            return code_value_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建参考码值失败: {e}")
            raise MetadataError(f"批量创建参考码值失败: {e}")

    # ==================== 统一的批量操作辅助方法 ====================

    async def _unified_update(
        self,
        table: str,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0,
        source_type: str = None
    ) -> bool:
        """统一的更新方法 - 支持向量数据处理"""
        try:
            # 标准化输入参数
            if isinstance(updates, dict) and conditions is not None:
                if isinstance(conditions, dict):
                    update_list = [{"data": updates, "filters": conditions}]
                elif isinstance(conditions, list):
                    update_list = [{"data": updates, "filters": cond} for cond in conditions]
                else:
                    raise MetadataValidationError("conditions 必须是字典或字典列表")

                if len(update_list) == 1:
                    batch_size = 1
                    max_concurrency = 1
                    timeout_per_batch = 60.0

            elif isinstance(updates, list):
                update_list = []
                for update in updates:
                    if isinstance(update, dict) and 'data' in update and 'filters' in update:
                        update_list.append({"data": update['data'], "filters": update['filters']})
                    else:
                        raise MetadataValidationError("批量更新格式错误，应为 [{'data': {...}, 'filters': {...}}, ...]")
            else:
                raise MetadataValidationError("updates 参数格式错误")

            # 执行批量更新
            result = await self.rdb_client.abatch_update(
                table=table,
                updates=update_list,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            success = result.success and result.affected_rows > 0

            # TODO: 向量数据更新处理（如果需要）
            # 码值更新时可能需要更新向量数据

            return success

        except Exception as e:
            logger.error(f"统一更新操作失败: table={table}, error={e}")
            raise MetadataError(f"统一更新操作失败: {e}")

    async def _unified_delete(
        self,
        table: str,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0,
        source_type: str = None
    ) -> bool:
        """统一的删除方法 - 支持向量数据处理"""
        try:
            if isinstance(conditions, dict):
                condition_list = [conditions]
                batch_size = 1
                max_concurrency = 1
                timeout_per_batch = 60.0
            elif isinstance(conditions, list):
                condition_list = conditions
            else:
                raise MetadataValidationError("conditions 必须是字典或字典列表")

            if not condition_list:
                raise MetadataValidationError("删除条件不能为空")

            # 在删除前，需要先查询要删除的记录，以便处理向量数据
            records_to_delete = []
            if source_type and self.vdb_client:
                try:
                    for condition in condition_list:
                        query_result = await self.rdb_client.aquery({
                            "table": table,
                            "data": ["*"],
                            "filters": condition,
                            "limit": 10000
                        })
                        if query_result and query_result.data:
                            records_to_delete.extend(query_result.data)
                except Exception as e:
                    logger.warning(f"查询待删除记录失败，跳过向量处理: {e}")

            # 执行批量删除
            result = await self.rdb_client.abatch_delete(
                table=table,
                conditions=condition_list,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            success = result.success and result.affected_rows > 0

            # 删除对应的向量数据
            if success and source_type and self.vdb_client and records_to_delete:
                try:
                    await self._delete_vectors_for_records(table, records_to_delete, source_type)
                except Exception as e:
                    logger.warning(f"删除向量数据失败: {e}")

            return success

        except Exception as e:
            logger.error(f"统一删除操作失败: table={table}, error={e}")
            raise MetadataError(f"统一删除操作失败: {e}")

    async def _unified_query(
        self,
        table: str,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """统一的查询方法 - 高性能批量查询"""
        if not conditions_list:
            return []

        try:
            queries = []
            for condition in conditions_list:
                query = {
                    "table": table,
                    "data": ["*"],
                    "filters": condition,
                    "limit": 1000
                }
                queries.append(query)

            logger.info(f"开始批量查询: table={table}, 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            batch_results = await self.rdb_client.abatch_query(
                table=table,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    else:
                        logger.debug(f"查询 {i+1} 无结果: 条件={conditions_list[i]}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 失败: {e}")

            logger.info(f"批量查询完成: table={table}, 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"统一查询操作失败: table={table}, error={e}")
            raise MetadataError(f"统一查询操作失败: {e}")

    async def _delete_vectors_for_records(
        self,
        table: str,
        records: List[Dict[str, Any]],
        source_type: str
    ) -> None:
        """为删除的记录删除对应的向量数据"""
        if not records or not self.vdb_client:
            return

        try:
            if table == MetadataTableNames.MD_REFERENCE_CODE_VALUE:
                # 码值删除：删除码值向量
                for record in records:
                    knowledge_id = record.get('knowledge_id')
                    code_value_id = record.get('id')

                    if knowledge_id and code_value_id:
                        await self.vdb_client.adelete(
                            collection_name='md_code_embeddings',
                            expr=f"knowledge_id = '{knowledge_id}' and code_value_id = {code_value_id}"
                        )
                        logger.debug(f"删除码值向量数据成功: knowledge_id={knowledge_id}, code_value_id={code_value_id}")

        except Exception as e:
            logger.error(f"删除向量数据失败: table={table}, source_type={source_type}, error={e}")
            raise

    # ==================== 码值集批量操作 ====================

    async def batch_update_code_sets(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量更新参考码值集"""
        return await self._unified_update(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch,
            source_type=None  # 码值集不需要向量处理
        )

    async def batch_delete_code_sets(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量删除参考码值集"""
        return await self._unified_delete(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch,
            source_type=None  # 码值集不需要向量处理
        )

    async def batch_query_code_sets(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """批量查询参考码值集"""
        return await self._unified_query(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            conditions_list=conditions_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    # ==================== 码值批量操作 ====================

    async def batch_update_code_values(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量更新参考码值"""
        return await self._unified_update(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch,
            source_type='CODE'  # 码值需要向量处理
        )

    async def batch_delete_code_values(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量删除参考码值"""
        return await self._unified_delete(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch,
            source_type='CODE'  # 码值需要向量处理
        )

    async def batch_query_code_values(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """批量查询参考码值"""
        return await self._unified_query(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            conditions_list=conditions_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )
