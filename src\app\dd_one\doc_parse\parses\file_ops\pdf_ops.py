#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：pdf_ops.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 10:55 
@Desc    ： pdf相关的操作
"""
import logging
import re
import os
from pathlib import Path
from typing import List, Tuple, Dict

from PyPDF2 import PdfReader, PdfWriter

from app.dd_one.doc_parse.parses.simple_utils.utils.utils import save_dict_to_txt, print_dict, print_list


# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def split_pdf(input_path, output_prefix, page_ranges, o_path=None):
    """
    将 PDF 按指定页码范围拆分为多个文件

    参数:
    input_path (str): 输入 PDF 文件路径
    output_prefix (str): 输出文件前缀
    page_ranges (list): 页码范围列表，每个元素为 (start_page, end_page) 元组
    o_path (str): 输出路径
    """
    split_part_path = []
    pdf_reader: PdfReader = PdfReader(input_path)
    last_page = len(pdf_reader.pages)

    for i, (start, end) in enumerate(page_ranges):
        pdf_writer = PdfWriter()
        # 注意：页码从 0 开始，所以要减 1
        if end == -1:
            end = last_page
        for page_num in range(start - 1, end):
            pdf_writer.add_page(pdf_reader.pages[page_num])

        if o_path and os.path.exists(o_path):
            output_path = f"{o_path}/{output_prefix}_part_{i + 1}.pdf"
        else:
            output_path = f"{output_prefix}_part_{i + 1}.pdf"

        with open(output_path, 'wb') as output_file:
            pdf_writer.write(output_file)
        split_part_path.append(output_path)
        logger.info(f"已生成 {output_path}，包含第 {start} 到 {end} 页")

    return split_part_path

def get_pdf_dir(input_path):
    pdf_reader: PdfReader = PdfReader(input_path)
    dir_page_s = 0
    dir_page_e = 0
    try:
        for idx, page in enumerate(pdf_reader.pages):
            text = page.extract_text()
            if not text:
                continue

            if "目   录" in text:
                dir_page_s = idx
                continue

            if "— 1 —" in text:
                dir_page_e = idx
                break
    except Exception as e:
        raise Exception(f"目标文件未找到目录, 目标文件路径：{input_path}")

    return {
                "dir_page_s": dir_page_s + 1,
                "dir_page_e": dir_page_e
            }

def extract_content_text(input_path):
    """
    解析正文内容

    参数:
    input_path (str): 输入 PDF 文件路径
    """
    pdf_reader = PdfReader(input_path)
    text = ""

    for page in pdf_reader.pages:
        c_text = page.extract_text()

        lines = c_text.split('\n')
        # page_height = len(lines)
        # # 去除页眉行
        # header_line = 1
        # footer_line = page_height - 1
        # body_lines = lines[header_line:footer_line-1]
        text += "\n".join(lines[3:])

    input_file = Path(input_path)
    output_path = os.path.join(
        input_file.parent,
        f"{input_file.stem}.txt"
    )
    with open(output_path, 'w', encoding='utf-8') as output_file:
        output_file.write(text)
    logger.info(f"已提取文本到 {output_path}")

    return text


def extract_form_numbers(text):
    # 步骤1：预处理——合并以顿号结尾的换行内容
    lines = text.split('\n')
    merged_lines = []
    temp = ""
    for line in lines:
        line = line.strip()
        if not line:
            continue
        # 如果当前临时内容不为空，先拼接
        if temp:
            temp += line
            # 检查拼接后是否还有顿号结尾（可能多换行）
            if not temp.endswith('、'):
                merged_lines.append(temp)
                temp = ""
        else:
            # 如果当前行以顿号结尾，暂存等待下一行
            if line.endswith('、'):
                temp = line
            else:
                merged_lines.append(line)
    # 处理最后剩余的临时内容
    if temp:
        merged_lines.append(temp)
    # 合并为完整文本
    processed_text = ' '.join(merged_lines)

    # 步骤2：提取表单号
    # 匹配"表单号："后的所有内容，支持顿号分隔和字母数字组合
    pattern = r'表?单号[:：]\s*([A-Za-z0-9、]+)'
    matches = re.findall(pattern, processed_text)

    # 拆分顿号，去重并整理
    form_numbers = []
    for match in matches:
        # 处理可能的连续顿号（如"A1、、A2" → 过滤空值）
        parts = [p for p in match.split('、') if p]
        form_numbers.extend(parts)

    return form_numbers

def task_extract_form_number(file_parts: list):
    extract_results = []
    for idx, file_part in enumerate(file_parts):
        extract_result = extract_content_text(file_part)
        table_names = extract_form_numbers(extract_result)
        logger.info(f"file path:{file_part}\ttable_names:{table_names}")
        extract_results.append((file_part, table_names))

    return extract_results

def extract_sections_and_titles(text):
    """
    按“一、xxx”层级切分文本，提取每个部分的标题和内容
    :param text: 原始文本内容
    :return: 列表，每个元素是字典：{"title": 标题, "content": 内容}
    """
    # 正则匹配标题：“一、xxx” “二、xxx”... 支持中文数字（一、二、三...）
    # 匹配规则：换行符后 + 中文数字 + 、 + 标题内容 + 换行符
    pattern = re.compile(r'(\n+([一二三四五六七八九十]+、[^\n]+))')

    # 用特殊标记分割文本（方便后续处理）
    split_text = re.split(pattern, text)

    # 整理结果：第一个元素是标题前的内容（通常无用），后续按 (分隔符, 标题, 内容) 分组
    sections = []
    for i in range(1, len(split_text), 3):
        title = split_text[i].strip().replace("\n", "")  # 标题（如“一、指标解释”）
        content = split_text[i + 2].strip()  # 标题对应的内容
        sections.append({"title": title, "content": content})

    return sections


def merge_lines_by_period(text):
    """
    将文本中末尾不是句号的行与下一行合并，直到遇到句号为止

    Args:
        text: 原始文本（包含多行）

    Returns:
        合并后的文本（按句号分割段落）
    """
    lines = [line.strip() for line in text.split('\n') if line.strip()]  # 去除空行
    merged_lines = []
    current_line = ""

    for line in lines:
        current_line += line  # 累加当前行
        # 如果当前行末尾是句号，结束当前段落
        if current_line.endswith('。'):
            merged_lines.append(current_line)
            current_line = ""
    # 处理最后一段（可能没有句号）
    if current_line:
        merged_lines.append(current_line)

    return '\n'.join(merged_lines)  # 用换行分隔段落



# def extract_term_definitions_colon(text, remove_footnotes=True, strip_newlines=True):
#     """
#     仅通过冒号（:或：）匹配名词和解释，支持完整提取跨行的长解释
#
#     Args:
#         text: 原始文本
#         remove_footnotes: 是否移除数字开头的脚注
#         strip_newlines: 是否清理解释中的换行符
#
#     Returns:
#         列表，每个元素为 (名词, 解释) 元组
#     """
#     # 预处理：移除脚注
#     if remove_footnotes:
#         text = re.sub(r'^\s*\d+\.?\s+.*?$', '', text, flags=re.MULTILINE)
#     # 核心正则：匹配"名词:解释"结构，支持解释跨越多行
#     # 正则说明：
#     # ^\s*([^:：\n]+?)  - 匹配名词（行首开始，非贪婪，直到冒号前）
#     # [:：]             - 匹配中文或英文冒号
#     # \s*               - 匹配冒号后的空格
#     # (.*?)             - 匹配解释内容（非贪婪）
#     # (?=^\s*[^:：\n]+?[:：]|\Z) - 结束条件：下一个"名词:解释"或文本结束（\Z表示全文结束）
#     pattern = re.compile(
#         r'^\s*([^:：\n]+?)[:：]\s*(.*?)(?=^\s*[^:：\n]+?[:：]|\Z)',
#         re.MULTILINE | re.DOTALL  # MULTILINE支持多行匹配，DOTALL让.匹配换行符
#     )
#
#     # 提取所有匹配项
#     matches = pattern.findall(text)
#
#     # 清理结果（去空格、换行）
#     result = []
#     for noun, explanation in matches:
#         # 清理名词
#         clean_noun = noun.strip().replace('\n', ' ')
#         # 清理解释（保留必要的换行，移除多余的空行）
#         clean_explanation = explanation.strip()
#         if strip_newlines:
#             # 合并多余的空行为单个空格
#             clean_explanation = re.sub(r'\n\s*\n', '\n', clean_explanation)
#             clean_explanation = clean_explanation.replace('\n', ' ')
#
#         # 过滤空结果
#         if clean_noun and clean_explanation:
#             result.append((clean_noun, clean_explanation))
#
#     return result


# def extract_term_definitions_colon(block_text, remove_footnotes=True, strip_newlines=True):
#     """
#     提取"名词:解释"对（解决连续结构和名词截断问题）
#     """
#     # 预处理：移除脚注
#     if remove_footnotes:
#         block_text = re.sub(r'^\s*\d+\.?\s+.*?$', '', block_text, flags=re.MULTILINE)
#
#     # 核心正则：允许名词包含括号，通过“下一个名词的明确特征”判断结束
#     # 下一个名词的特征：行首+（可能带括号的名词）+冒号
#     pattern = re.compile(
#         r'^\s*([^:：\n()]+(?:\([^)]*\))*[^:：\n()]*?)[:：]\s*(.*?)(?=^\s*[^:：\n()]+(?:\([^)]*\))*[^:：\n()]*?[:：]|\Z)',
#         re.MULTILINE | re.DOTALL
#     )
#
#     matches = pattern.findall(block_text)
#
#     # 清理结果：合并被截断的名词和解释
#     cleaned_matches = []
#     for i in range(len(matches)):
#         noun, exp = matches[i]
#
#         # 过滤明显不完整的名词（如长度过短或解释过短）
#         if len(noun) < 4 or len(exp) < 10:
#             # 尝试与下一个结果合并（如果存在）
#             if i + 1 < len(matches):
#                 next_noun, next_exp = matches[i + 1]
#                 # 判断是否为截断：当前解释以“其中”“项下”等结尾，且下一个名词更像解释的延续
#                 if exp.strip().endswith(('其中', '项下', '项')) and not next_noun.endswith(('存款', '资金', '贷款')):
#                     merged_noun = noun
#                     merged_exp = exp + next_noun + '：' + next_exp
#                     cleaned_matches.append((merged_noun, merged_exp))
#                     continue  # 跳过下一个结果
#         # 正常添加
#         cleaned_matches.append((noun, exp))
#
#     # 最终清理（去空格、换行）
#     result = []
#     for noun, explanation in cleaned_matches:
#         clean_noun = noun.strip().replace('\n', ' ')
#         clean_explanation = explanation.strip().replace('\n', ' ')
#         if clean_noun and clean_explanation:
#             result.append((clean_noun, clean_explanation))
#
#     return result

def remove_labels(text: str) -> str:
    """
    移除文本中的标号（如"（五）"和"1."）及其后续冒号，保留文本内容

    Args:
        text: 待处理的文本

    Returns:
        处理后的文本
    """
    # 定义匹配标号的正则表达式
    # 匹配"（五）"类型的标号行及其后续内容（直到空行）
    pattern1 = r'^[ \t]*（[一二三四五六七八九十百千]+）.*?$[\s\S]*?^(?:\s*$|（)'

    # 匹配"1."类型的标号行及其后续内容（直到空行）
    pattern2 = r'^[ \t]*\d+\..*?$[\s\S]*?^(?:\s*$|（|\d+\.|\d+\）)'

    # 替换匹配的内容
    text = re.sub(pattern1, '', text, flags=re.MULTILINE)
    text = re.sub(pattern2, '', text, flags=re.MULTILINE)

    return text


def extract_term_definitions_colon(block_text, remove_footnotes=True, strip_newlines=True):
    """
    提取"名词:解释"对，自动删除两个名词之间的冗余内容
    """
    # 预处理：移除脚注
    if remove_footnotes:
        block_text = re.sub(r'^\s*\d+\.?\s+.*?$', '', block_text, flags=re.MULTILINE)

    block_text = remove_labels(block_text)
    block_text = merge_lines_by_period(block_text)

    # 第一步：定位所有名词的位置（通过"名词:"结构）
    # 匹配所有可能的名词（带冒号）
    noun_pattern = re.compile(
        r'^\s*([^:：\n()]+(?:\([^)]*\))*[^:：\n()]*?)[:：]',
        re.MULTILINE
    )
    noun_matches = list(noun_pattern.finditer(block_text))
    if not noun_matches:
        return []  # 无名词时直接返回

    # 第二步：按名词位置分割文本，提取每个名词对应的解释
    terms = []
    for i in range(len(noun_matches)):
        # 当前名词的匹配信息
        current_match = noun_matches[i]
        noun_start, noun_end = current_match.span()
        noun = current_match.group(1).strip().replace('\n', ' ')

        # 确定解释的结束位置（下一个名词的开始，或文本结束）
        if i < len(noun_matches) - 1:
            next_noun_start = noun_matches[i + 1].start()
            explanation_end = next_noun_start
        else:
            explanation_end = len(block_text)

        # 提取解释内容（当前名词结束到下一个名词开始）
        explanation = block_text[noun_end:explanation_end].strip()

        # 清理结果
        if strip_newlines:
            explanation = explanation.replace('\n', ' ')
        if noun and explanation:
            terms.append({noun: explanation})

    return terms


def split_records(text: str) -> List[str]:
    """
    根据分号(;)将文本分割成独立的记录
    """
    # 处理跨行的记录，确保分号是记录结束的标志
    records = []
    current_record = ""

    # 处理每一行
    for line in text.split('\n'):
        # 移除行尾的空白字符
        line = line.rstrip()

        # 如果行尾有分号，说明这是记录的结束
        if line.endswith(';') or line.endswith('；'):
            current_record += line
            records.append(current_record)
            current_record = ""
        elif line.endswith('。') or line.endswith('.') :
            continue
        else:
            # 否则，这是记录的一部分，继续添加
            current_record += line + " "

    # 添加最后一个记录（如果没有以分号结束）
    if current_record.strip():
        records.append(current_record)

    return records


def extract_main_content(pdf_path, header_threshold=0.1, footer_threshold=0.99):
    """提取PDF正文内容，排除页眉、页脚和页码"""
    reader = PdfReader(pdf_path)
    main_content = []

    for idx, page in enumerate(reader.pages):
        # 提取带位置信息的文本块（简化版）
        text = page.extract_text()
        if not text:
            continue

        if "— 1 —" in text:
            break

        # 简单的页眉页脚过滤（基于页面比例）
        lines = text.split('\n')
        page_height = len(lines)
        # 去除页眉行
        header_lines = int(page_height * header_threshold)

        # 保留正文行
        body_lines = lines[header_lines:]
        for body_line in body_lines:
            # 处理换行情况
            if "…" in body_line or "." in body_line:
                main_content.append(body_line)

    return main_content


def extract_toc_items(toc_text):
    lines = toc_text
    merged_lines = []
    current_line = ""

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 如果行以数字+点开头（如"5.", "6."），视为新目录项
        if re.match(r'^\d+\.', line):
            # 如果当前有累积的行，先添加到结果
            if current_line:
                merged_lines.append(current_line)
            current_line = line
        # 否则视为上一行的延续，合并到当前行
        else:
            current_line += line

    # 添加最后一个累积的行
    if current_line:
        merged_lines.append(current_line)

    # 提取标题和页码
    toc_items = []
    pattern = re.compile(r'^\s*(.*?)\s*[…\s]+(\d+)\s*$')

    for line in merged_lines:
        match = pattern.search(line)
        if match:
            title = match.group(1).strip()
            page = int(match.group(2))
            toc_items.append((title, page))
        else:
            continue

    return toc_items


def extract_check_rule(lines: List[str]) -> Dict[str, List[str]]:
    """
    提取每行前五个字符作为键，整行内容作为值存入字典

    Args:
        lines: 包含文本行的列表

    Returns:
        字典，键为每行前五个字符，值为包含对应行的列表
    """
    result = {}
    for line in lines:
        line = line.strip()  # 去除首尾空白字符
        if not line:  # 跳过空行
            continue
        key = line[:5]  # 提取前五个字符作为键
        result.setdefault(key, []).append(line)  # 将行添加到对应键的值列表中
    return result


# TODO优化kv提取
def process_text_to_sentences(text: str) -> list[str]:
    """
    处理文本为一行一句话格式：
    1. 删除以序号开头的行（如 （一）、1.、(1)、A. 等）
    2. 合并末尾无句号的行（除非符合冒号特殊规则）
    3. 按句号分割成独立句子
    """
    # 正则表达式：匹配需删除的序号前缀
    NUMBERING_PATTERN = re.compile(r'^'
                                   r'('
                                   r'[一二三四五六七八九十百千]+[、.)]\s*|'  # 中文数字+标点 如"一、"
                                   r'\([一二三四五六七八九十百千]+\)\s*|'  # 括号+中文数字 如"(一)"
                                   r'（[一二三四五六七八九十百千]+）\s*|'  # 中文括号+中文数字 如"（一）"
                                   r'\d+[、.)]\s*|'  # 数字+标点 如"1、"
                                   r'\(\d+\)\s*|'  # 括号+数字 如"(1)"
                                   r'（\d+）\s*|'  # 中文括号+数字 如"（1）"
                                   r'[a-zA-Z]+[.).]\s*'  # 字母+标点 如"A."
                                   r')')

    lines = text.split('\n')
    merged_lines = []
    current_line = ""
    prev_ended_with_period = True  # 跟踪上一行是否以句号结尾

    for line in lines:
        stripped_line = line.strip()
        if not stripped_line:
            continue  # 跳过空行

        # 步骤1：删除正则匹配到的序号行（整行移除）
        if NUMBERING_PATTERN.match(stripped_line):
            continue  # 直接跳过序号行

        # 步骤2：判断冒号特殊规则
        # 条件：当前行有冒号且冒号前无句号，且上一行未以句号结尾 → 不合并
        has_cn_colon = '：' in stripped_line
        has_en_colon = ':' in stripped_line
        has_colon = has_cn_colon or has_en_colon

        if has_colon:
            # 找到最后出现的冒号位置
            cn_pos = stripped_line.find('：') if has_cn_colon else -1
            en_pos = stripped_line.find(':') if has_en_colon else -1
            colon_pos = max(cn_pos, en_pos)

            # 检查冒号前是否有句号
            period_pos = stripped_line.find('。')
            colon_before_period = (period_pos == -1) or (colon_pos < period_pos)
        else:
            colon_before_period = False

        # 应用冒号特殊规则
        if has_colon and colon_before_period and not prev_ended_with_period:
            if current_line:  # 若当前有累积内容，先保存
                merged_lines.append(current_line)
            current_line = stripped_line  # 新行作为独立内容
        else:
            # 普通行：合并到当前累积内容
            current_line += stripped_line

        # 步骤3：判断是否为完整句子（以句号结尾）
        prev_ended_with_period = current_line.endswith('。')
        if prev_ended_with_period:
            merged_lines.append(current_line)
            current_line = ""  # 重置累积内容

    # 处理最后剩余的内容（若未以句号结尾）
    if current_line:
        merged_lines.append(current_line)

    # 步骤4：按句号分割，确保每行一句话
    sentences = []
    for line in merged_lines:
        # 分割后过滤空字符串，补全句号
        parts = [p.strip() for p in line.split('。') if p.strip()]
        sentences.extend([f"{p}。" for p in parts])

    return sentences

def merge_lines_without_colon(lines: list[str]) -> list[str]:
    """
    处理列表中的行，将没有冒号（:或：）的行合并到上一行
    """
    merged = []
    for line in lines:
        line = line.strip()  # 去除行首尾空白
        if not line:  # 跳过空行
            continue
        if not merged or '：' in line or ':' in line:
            merged.append(line)  # 有冒号或第一行，直接添加
        else:
            merged[-1] += line  # 无冒号，合并到上一行
    return merged

def extract_key_value_from_list(lines: list[str]) -> dict:
    """
    从列表中提取键值对：
    1. 遍历列表中的每一行
    2. 若行中存在 : 或 ：，则分割为 key: value
    3. 忽略不含冒号的行
    """
    key_value = {}
    for line in lines:
        line = line.strip()  # 去除行首尾空白
        if not line:  # 跳过空行
            continue

        # 查找中文冒号和英文冒号的位置
        cn_colon_pos = line.find('：')
        en_colon_pos = line.find(':')

        # 确定是否存在有效冒号
        has_cn_colon = cn_colon_pos != -1
        has_en_colon = en_colon_pos != -1

        if has_cn_colon or has_en_colon:
            # 取第一个出现的冒号作为分隔符
            if has_cn_colon and has_en_colon:
                split_pos = min(cn_colon_pos, en_colon_pos)
            elif has_cn_colon:
                split_pos = cn_colon_pos
            else:
                split_pos = en_colon_pos

            # 分割键和值（去除两端空白）
            key = line[:split_pos].strip()
            value = line[split_pos + 1:].strip()

            # 存入字典（重复键会覆盖，保留最后一次出现的值）
            key_value[key] = value

    return key_value

def extract_doc_info(doc_path):
    # 解析需求文件内容
    extract_result = extract_content_text(doc_path)
    sections = extract_sections_and_titles(extract_result) # 将需求文件根据 一、 二、 三等切分
    table_names = extract_form_numbers(extract_result) # 提取需求文件关联的需求表（返回报送基础信息submit_basic_info的时候要用）
    # 待返回的信息
    term_pair_dict = {} # kv_信息
    check_rule = [] # 核查规则
    submit_basic_info = {} #

    input_file = Path(doc_path)
    kv_output_path = os.path.join(
        input_file.parent,
        f"{input_file.stem}_kv.txt"
    )
    doc_info_output_path = os.path.join(
        input_file.parent,
        f"{input_file.stem}_doc_info.txt"
    )


    for sec in sections:
        if "指标解释" in sec['title'] or "填报说明" in sec['title'] or "统计指标口径" in sec['title']:
            text = re.sub(" ", "", sec['content'])

            # TODO优化KV提取
            merge_t2s = process_text_to_sentences(text)
            # print_list(merge_t2s)
            merge_s2kv = merge_lines_without_colon(merge_t2s)
            print_list(merge_s2kv)
            term_pair_dict = extract_key_value_from_list(merge_s2kv)
            save_dict_to_txt(term_pair_dict, kv_output_path)

            # results = extract_term_definitions_colon(text)
            # with open(kv_output_path, 'w', encoding='utf-8') as output_file:
            #     for idx, result in enumerate(results):
            #         term_pair_dict.update(result)
            #         output_file.write(str(result))
            #         output_file.write("\n")

        if "注意事项" in sec['title']:
            if str(sec['content']).count('=') > 3:
                split_records_result = split_records(sec['content'])
                for item in split_records_result:
                    check_rule.append(item)

        if "数据报送要求" in sec['title']:
            submit_basic_content = sec['content']
            for table_name in table_names:
                submit_basic_info[table_name] = submit_basic_content

    doc_info = {
        "term_pair_dict": term_pair_dict,
        "check_rule_dict": extract_check_rule(check_rule),
        "submit_basic_info": submit_basic_info
    }

    # print_dict(term_pair_dict)

    with open(doc_info_output_path, 'w', encoding='utf-8') as output_file:
        output_file.write(str(doc_info))
        output_file.write("\n")

    return doc_info

if __name__ == '__main__':
    txt_path = 'D:/project/work/hsbc/文件解析/code/src/file/gc/doc/金融统计制度汇编（2023版）印刷_temp/output_part_1.pdf'
    extract_doc_info(txt_path)