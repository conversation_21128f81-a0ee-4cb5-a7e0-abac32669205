"""
DD提交相关的响应模型定义

基于新业务逻辑设计的响应数据结构
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field


class StandardResponse(BaseModel):
    """标准响应模型"""
    code: str = Field(..., description="响应码：0表示成功，400表示失败")
    msg: str = Field(..., description="响应信息")


class BusinessSubmissionItem(BaseModel):
    """业务报送结果项"""
    entry_id: str = Field(..., description="条目ID")
    entry_type: str = Field(..., description="条目类型：ITEM或TABLE")
    DR22: List[str] = Field(..., description="部门ID数组")
    BDR01: List[str] = Field(..., description="部门ID数组")
    BDR03: str = Field(..., description="描述字符串")


class BusinessSubmissionResponse(BaseModel):
    """业务报送响应模型"""
    code: str = Field(..., description="响应码：0表示成功，400表示失败")
    msg: str = Field(..., description="响应信息")
    data: Optional[List[BusinessSubmissionItem]] = Field(None, description="处理结果数据")


class DataBackfillResponse(BaseModel):
    """数据回填响应模型"""
    code: str = Field(..., description="响应码：0表示成功，400表示失败")
    msg: str = Field(..., description="响应信息")
    statistics: Optional[Dict[str, Any]] = Field(None, description="处理统计信息")


class ProcessingStatistics(BaseModel):
    """处理统计信息"""
    total_count: int = Field(..., description="总处理数量")
    updated_count: int = Field(..., description="成功更新数量")
    error_count: int = Field(..., description="错误数量")
    processing_time_ms: Optional[float] = Field(None, description="处理时间（毫秒）")
    engine_type: Optional[str] = Field(None, description="使用的引擎类型")


class FieldFillDetail(BaseModel):
    """字段填充详情"""
    field_name: str = Field(..., description="字段名称")
    original_value: Optional[str] = Field(None, description="原始值")
    filled_value: str = Field(..., description="填充后的值")
    fill_reason: str = Field(..., description="填充原因")
    status: str = Field(..., description="填充状态")


class DDBEnhancedProcessData(BaseModel):
    """DD-B增强处理结果数据"""
    total_records_found: int = Field(..., description="查询到的记录总数")
    records_processed: int = Field(..., description="实际处理的记录数")
    records_with_complete_main_fields: int = Field(..., description="主要字段完整的记录数")
    records_requiring_fill: int = Field(..., description="需要填充的记录数")
    total_fields_filled: int = Field(..., description="填充的字段总数")
    processing_time_ms: float = Field(..., description="处理耗时（毫秒）")
    status: str = Field(..., description="处理状态")
    fill_details: Optional[List[FieldFillDetail]] = Field(None, description="字段填充详情")
    processing_notes: Optional[List[str]] = Field(None, description="处理说明")
    performance_stats: Optional[Dict[str, Any]] = Field(None, description="性能统计")


class DDBEnhancedProcessResponse(BaseModel):
    """DD-B增强处理响应模型（立即响应）"""
    code: str = Field(..., description="响应码：0表示成功，400表示失败")
    msg: str = Field(..., description="响应信息")


class DDBCallbackItem(BaseModel):
    """DD-B回调数据项 - 包含BDR01-BDR17字段"""
    entry_id: str = Field(..., description="submission_id")
    entry_type: str = Field(..., description="TABLE(范围项)或ITEM(填报项)")
    # BDR01-BDR04字段（从原始records读取）
    BDR01: Optional[str] = Field(None, description="BDR01字段")
    BDR02: Optional[str] = Field(None, description="BDR02字段")
    BDR03: Optional[str] = Field(None, description="BDR03字段")
    BDR04: Optional[str] = Field(None, description="BDR04字段")
    # BDR05-BDR17字段（Pipeline处理生成）
    BDR05: Optional[str] = Field(None, description="BDR05字段")
    BDR06: Optional[str] = Field(None, description="BDR06字段")
    BDR07: Optional[str] = Field(None, description="BDR07字段")
    BDR08: Optional[str] = Field(None, description="BDR08字段")
    BDR09: Optional[str] = Field(None, description="BDR09字段")
    BDR10: Optional[str] = Field(None, description="BDR10字段")
    BDR11: Optional[str] = Field(None, description="BDR11字段")
    BDR12: Optional[str] = Field(None, description="BDR12字段")
    BDR13: Optional[str] = Field(None, description="BDR13字段")
    BDR14: Optional[str] = Field(None, description="BDR14字段")
    BDR15: Optional[str] = Field(None, description="BDR15字段")
    BDR16: Optional[str] = Field(None, description="BDR16字段")
    BDR17: Optional[str] = Field(None, description="BDR17字段")


class DDBCallbackRequest(BaseModel):
    """DD-B回调请求模型"""
    report_code: str = Field(..., description="报表代码")
    dept_id: str = Field(..., description="部门ID")
    item: List[DDBCallbackItem] = Field(..., description="处理结果项列表")


class DDBCallbackResponse(BaseModel):
    """DD-B回调响应模型"""
    code: int = Field(..., description="响应码：200表示成功")
    msg: str = Field(..., description="响应信息")
    data: bool = Field(..., description="处理结果")


class DDBUpdateDataItem(BaseModel):
    """DD-B更新数据项（前端格式）"""
    entry_id: str = Field(..., description="条目ID")
    entry_type: str = Field(..., description="条目类型：范围项/填报项")

    # 动态BDR字段，根据实际更新的字段添加
    class Config:
        extra = "allow"  # 允许额外字段


class DDBUpdateResponse(BaseModel):
    """DD-B数据更新响应模型（前端兼容格式）"""
    report_code: str = Field(..., description="报表代码")
    dept_id: str = Field(..., description="部门ID")
    data: List[DDBUpdateDataItem] = Field(default_factory=list, description="更新后的数据列表")


# 向后兼容的别名
DutyDistributionResponse = BusinessSubmissionResponse
AssignmentItem = BusinessSubmissionItem
DutyDistributionResult = BusinessSubmissionResponse
