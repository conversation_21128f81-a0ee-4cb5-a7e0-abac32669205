#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：ier_chatbot_api.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/21 17:26 
@Desc    ：外规内化chatbot相关的api接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger

from app.ier.config.config import (RDB_CLIENT_CONFIG,
                                   VDB_CLIENT_CONFIG,
                                   LLM_CLIENT_CONFIG,
                                   EMBEDDING_CLIENT_CONFIG)
from app.ier.services.ier.model.ier_chatbot_model import (ChatbotQueryModel,
                                                          ChatbotSaveModel,
                                                          ChatbotHistoryQueryModel)
from app.ier.services.ier.chatbot_logic import ChatBotLogic
from service import get_client
# 定义路由
router = APIRouter(tags=["外规内化-聊天机器人"])

async def get_chatbot_logic() -> ChatBotLogic:
    """获取法规文件处理实例"""
    try:
        rdb_client = await get_client(RDB_CLIENT_CONFIG)
        vdb_client = await get_client(VDB_CLIENT_CONFIG)
        llm_client = await get_client(LLM_CLIENT_CONFIG)
        embed_client = await get_client(EMBEDDING_CLIENT_CONFIG)
        return ChatBotLogic(rdb_client, vdb_client, llm_client, embed_client)
    except Exception as e:
        logger.error(f"创建部门职责分配逻辑实例失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务初始化失败: {e}"
        )

def register_ier_chatbot_routers():
    @router.post("/chatbot", response_model=dict, summary="聊天机器人接口")
    async def chatbot_query(
            request: ChatbotQueryModel,
            logic: ChatBotLogic = Depends(get_chatbot_logic)
    ):
        return StreamingResponse(logic.query(request), media_type="text/event-stream")

    @router.post("/chatbot_save_history", response_model=dict, summary="保存聊天记录接口")
    async def chatbot_query(
            request: ChatbotSaveModel,
            logic: ChatBotLogic = Depends(get_chatbot_logic)
    ):
        await logic.save_history(request)
        return {"status": "ok"}

    @router.post("/chatbot_get_history", response_model=dict, summary="获得聊天记录接口")
    async def chatbot_query(
            request: ChatbotHistoryQueryModel,
            logic: ChatBotLogic = Depends(get_chatbot_logic)
    ):
        result = await logic.get_chatbot_history(request)
        return result

    return router