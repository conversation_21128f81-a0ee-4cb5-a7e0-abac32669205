#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：gc_parse.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/9 10:42 
@Desc    ：大集中相关excel及文档解析
"""
import logging
import json
import re
import os

from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import pandas as pd

from config.config import style_json_path
from app.dd_one.doc_parse.base_parse.base_document_parse import BaseDocumentParser
from app.dd_one.doc_parse.parses.file_ops.pdf_ops import extract_doc_info
from app.dd_one.doc_parse.parses.file_ops.excel_ops import read_gc_tb, get_sheet_names
from app.dd_one.doc_parse.parses.workflow_djz.gc_pdf_split_workflow import GCPdfSplitWorkflow
from app.dd_one.doc_parse.parses.simple_utils.utils.utils import save_dict_to_txt

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def clean_subject_name(name: str) -> str:
    """
    清理科目名称中的各种前缀格式
    测试用例
    test_cases = [
        "（一）现金",  # 中文括号+中文数字
        "(1) 存放中央银行存款",  # 英文括号+阿拉伯数字
        "1. 财政性存款",  # 阿拉伯数字+点号
        "A. 准备金存款",  # 字母+点号
        "银行存款:",  # 中文冒号
        "备注: 其他科目",  # 英文冒号+空格
        "一、法定准备金存款",  # 中文数字+顿号
        "1）超额准备金",  # 阿拉伯数字+右括号
        "A) 存放同业款项",  # 字母+右括号
        "(备注) 特殊科目",  # 括号包围的文字
        "1: 一级科目",  # 数字+冒号
        "A: 二级科目",  # 字母+冒号
    ]
    """
    # 修正后的正则表达式，确保括号正确闭合
    pattern = r'^(' \
              r'[一二三四五六七八九十百千]+[、.)]\s*|' \
              r'\([一二三四五六七八九十百千]+\)\s*|' \
              r'（[一二三四五六七八九十百千]+）\s*|' \
              r'\d+[、.)]\s*|' \
              r'\(\d+\)\s*|' \
              r'（\d+）\s*|' \
              r'[0-9a-zA-Z]+[.).]\s*|' \
              r'[（(].*?[）)]\s*|' \
              r'[0-9a-zA-Z\d]+[:：]\s*' \
              r')'
    s = re.sub(pattern, '', name).strip()
    s = s.strip(':').strip('：').strip(':')
    match = re.search(r'^.*?[:：](.*)$', s)
    return match.group(1).strip() if match else s


def int_to_excel_column(n):
    if n <= 0:
        raise ValueError("输入的数字必须是正整数")

    result = ''
    while n > 0:
        # 计算当前位对应的字母（A对应1，Z对应26）
        remainder = (n - 1) % 26
        result = chr(65 + remainder) + result
        # 更新数值，处理更高位
        n = (n - 1) // 26

    return result


def execute_processing_logic(csv_dir: str, file_path: str, sheet_names_input: str) -> str:
    out_dict = {}
    output = []  # 输出的数据
    output_dir = os.path.dirname(file_path)
    # requirement_file
    requirement_file_base_name = os.path.basename(file_path)
    requirement_file_name, rf_ext = os.path.splitext(requirement_file_base_name)
    # table_file
    table_file_base_name = os.path.basename(csv_dir)
    table_file_name, tf_ext = os.path.splitext(table_file_base_name)
    # output_file
    output_file_name = f"{requirement_file_name}_{table_file_name}_process_output.xlsx"
    excel_path = os.path.join(output_dir, output_file_name)
    try:
        # 加载样式 JSON
        json_path = style_json_path
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"未找到样式文件: {json_path}")
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                style_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"样式文件 {json_path} 中的 JSON 格式无效: {str(e)}")

        # 提取基本信息
        sheet_names = get_sheet_names(csv_dir)

        # 提取文档中的 kv信息 和 规则核对信息
        term_pair_dict = {}  # kv信息 （key:专业名词 value:专业名词解释）
        check_rule_dict = {}  # 核对规则信息 （key:科目代码 value:科目代码对应的核对规则）
        submit_basic_info_dict = {}  # 数据报送基础信息（key:报送表 value:报送基础信息）

        workflow = GCPdfSplitWorkflow(
            input_path=file_path,
            output_dir=None
        )
        # 执行工作流
        workflow_exec_results = workflow.execute()
        # 文档解析结果
        # [('output_part_1.pdf', ['A1411', 'A2411']), ('output_part_2.pdf', ['A3702'])]
        split_result = workflow_exec_results['task_extract_form_number']
        # 合并解析数据
        for file_path, tb_names in split_result:
            part_doc_info = extract_doc_info(file_path)
            part_term_pair_dict = part_doc_info['term_pair_dict']
            part_check_rule_dict = part_doc_info['check_rule_dict']
            part_submit_basic_info = part_doc_info['submit_basic_info']
            term_pair_dict.update(part_term_pair_dict)
            check_rule_dict.update(part_check_rule_dict)
            submit_basic_info_dict.update(part_submit_basic_info)

        term_pair_dict_path = os.path.join(output_dir, 'term_pair_dict.txt')
        save_dict_to_txt(term_pair_dict, term_pair_dict_path)

        # 提取table的信息
        records, gc_tb_indicator_names, table_name = read_gc_tb(csv_dir)

        # 构建输出（保持不变）
        for gc_tb_indicator_name in gc_tb_indicator_names:
            for item in records:
                output.append({
                    "DR01": "ADS",
                    "DR02": requirement_file_name,
                    "DR03": "不适用",
                    "DR04": "不适用",
                    "DR05": "不适用",
                    "DR06": table_name,
                    "DR07": table_file_name,
                    "DR08": "不适用",
                    "DR09": f"{item['科目名称']}_{gc_tb_indicator_name}",
                    "DR10": f"{item['科目代码']}_{gc_tb_indicator_name}",
                    "DR11": "不适用",
                    "DR12": "不适用",
                    "DR13": "不适用",
                    "DR14": "不适用",
                    "DR15": "不适用",
                    "DR16": submit_basic_info_dict.get(table_file_name, f"{table_file_name}数据报送需求暂无"),
                    "DR17": '维度描述：' + term_pair_dict.get(clean_subject_name(item['科目名称']),
                                                             item['科目名称']) + '\n指标描述：' + term_pair_dict.get(
                        gc_tb_indicator_name, item['科目名称']),
                    "DR18": "不适用",
                    "DR19": "月报",
                    "DR20": "\n".join(check_rule_dict.get(item['科目代码'])) if check_rule_dict.get(item['科目代码'],
                                                                                                    []) else "不适用",
                    "DR21": "不脱敏",
                    "DR22": ""
                })
        output.insert(0, {
            "DR01": "范围",
            "DR02": requirement_file_name,
            "DR03": "不适用",
            "DR04": "不适用",
            "DR05": "不适用",
            "DR06": table_name,
            "DR07": table_file_name,
            "DR08": "不适用",
            "DR09": "报送范围",
            "DR10": "不适用",
            "DR11": "不适用",
            "DR12": "不适用",
            "DR13": "不适用",
            "DR14": "不适用",
            "DR15": "不适用",
            "DR16": "不适用",
            "DR17": "",
            "DR18": "不适用",
            "DR19": "月报",
            "DR20": "不适用",
            "DR21": "不脱敏",
            "DR22": ""
        })

        table_name = sheet_names[0]
        out_dict[table_name] = output

        wb = Workbook()
        wb.remove(wb.active)

        for i, sheet_name in enumerate(sheet_names):
            sheet_name = sheet_name
            data = out_dict.get(sheet_name, [])
            if not data:
                continue

            ws = wb.create_sheet(title=sheet_name)

            # 恢复样式（保持不变）
            for row_idx, row_data in enumerate(style_data["rows"], start=1):
                for col_idx, cell_data in enumerate(row_data, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=cell_data["value"])

                    # 设置字体
                    font_info = cell_data.get("font", {})
                    cell.font = Font(
                        name=font_info.get("name", "Calibri"),
                        size=int(font_info.get("size", 11)),
                        bold=font_info.get("bold", False),
                        italic=font_info.get("italic", False),
                        color=font_info.get("color", "FF000000")
                    )

                    # 设置背景色
                    fill_info = cell_data.get("fill", {})
                    bg_color = fill_info.get("bgColor", "FFFFFFFF")
                    cell.fill = PatternFill(patternType="solid", fgColor=bg_color)

                    # 设置对齐
                    align_info = cell_data.get("alignment", {})
                    h_align = align_info.get("horizontal", "left")
                    v_align = align_info.get("vertical", "top")
                    wrap_text = align_info.get("wrap_text", False)
                    cell.alignment = Alignment(horizontal=h_align, vertical=v_align, wrap_text=wrap_text)

                    # -----------------------------
                    # ✅ 第二步：设置列宽
                    # -----------------------------
                for col_letter, width in style_data.get("column_widths", {}).items():
                    ws.column_dimensions[col_letter].width = float(width)

                    # -----------------------------
                    # ✅ 第三步：设置行高
                    # -----------------------------
                for row_number, height in style_data.get("row_heights", {}).items():
                    ws.row_dimensions[int(row_number)].height = float(height)

                    # -----------------------------
                    # ✅ 第四步：恢复合并单元格
                    # -----------------------------
                for merged_range in style_data.get("merged_cells", []):
                    ws.merge_cells(merged_range)
                    # 字体、背景、对齐等设置（保持不变）

            # 写入数据
            df = pd.DataFrame(data)
            for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), start=5):
                for c_idx, value in enumerate(row, start=1):
                    ws.cell(row=r_idx, column=c_idx, value=value)

        wb.save(excel_path)
        print(f"输出文件sheet {table_name} 已保存")

    except (IOError, ValueError, FileNotFoundError) as e:
        logger.error(f"处理失败: {str(e)}")
        return ''

    return excel_path


def extraction_report(csv_dir: str, file_path: str, sheet_names_input: str) -> dict:
    out_dict = {}
    output = []  # 输出的数据
    output_dir = os.path.dirname(file_path)
    # requirement_file
    requirement_file_base_name = os.path.basename(file_path)
    requirement_file_name, rf_ext = os.path.splitext(requirement_file_base_name)
    # table_file
    table_file_base_name = os.path.basename(csv_dir)
    table_file_name, tf_ext = os.path.splitext(table_file_base_name)
    # output_file
    output_file_name = f"{requirement_file_name}_{table_file_name}_process_output.xlsx"
    excel_path = os.path.join(output_dir, output_file_name)
    try:
        # 加载样式 JSON
        json_path = style_json_path
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"未找到样式文件: {json_path}")
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                style_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"样式文件 {json_path} 中的 JSON 格式无效: {str(e)}")

        # 提取基本信息
        sheet_names = get_sheet_names(csv_dir)

        # 提取文档中的 kv信息 和 规则核对信息
        term_pair_dict = {}  # kv信息 （key:专业名词 value:专业名词解释）
        check_rule_dict = {}  # 核对规则信息 （key:科目代码 value:科目代码对应的核对规则）
        submit_basic_info_dict = {}  # 数据报送基础信息（key:报送表 value:报送基础信息）

        workflow = GCPdfSplitWorkflow(
            input_path=file_path,
            output_dir=None
        )
        # 执行工作流
        workflow_exec_results = workflow.execute()
        # 文档解析结果
        # [('output_part_1.pdf', ['A1411', 'A2411']), ('output_part_2.pdf', ['A3702'])]
        split_result = workflow_exec_results['task_extract_form_number']
        # 合并解析数据
        for file_path, tb_names in split_result:
            part_doc_info = extract_doc_info(file_path)
            part_term_pair_dict = part_doc_info['term_pair_dict']
            part_check_rule_dict = part_doc_info['check_rule_dict']
            part_submit_basic_info = part_doc_info['submit_basic_info']
            term_pair_dict.update(part_term_pair_dict)
            check_rule_dict.update(part_check_rule_dict)
            submit_basic_info_dict.update(part_submit_basic_info)

        term_pair_dict_path = os.path.join(output_dir, 'term_pair_dict.txt')
        save_dict_to_txt(term_pair_dict, term_pair_dict_path)

        # 提取table的信息
        records, gc_tb_indicator_names, table_name = read_gc_tb(csv_dir)
        # 构建输出（保持不变）
        for idx_y, gc_tb_indicator_name in enumerate(gc_tb_indicator_names):
            for idx_x, item in enumerate(records):
                output.append({
                    "entry_type": "ITEM",
                    "DR01": "ADS",
                    "DR02": requirement_file_name,
                    "DR03": "不适用",
                    "DR04": "不适用",
                    "DR05": "不适用",
                    "DR06": table_name,
                    "DR07": table_file_name,
                    "DR08": "不适用",
                    "DR09": f"{item['科目名称']}_{gc_tb_indicator_name}",
                    "DR10": f"{item['科目代码']}_{gc_tb_indicator_name}",
                    "DR11": "不适用",
                    "DR12": "不适用",
                    "DR13": "不适用",
                    "DR14": "不适用",
                    "DR15": "不适用",
                    "DR16": submit_basic_info_dict.get(table_file_name, f"{table_file_name}数据报送需求暂无"),
                    "DR17": '维度描述：' + term_pair_dict.get(clean_subject_name(item['科目名称']),
                                                             item['科目名称']) + '\n指标描述：' + term_pair_dict.get(
                        gc_tb_indicator_name, item['科目名称']),
                    "DR18": "不适用",
                    "DR19": "月报",
                    "DR20": "\n".join(check_rule_dict.get(item['科目代码'])) if check_rule_dict.get(item['科目代码'],
                                                                                                    []) else "不适用",
                    "DR21": "不脱敏",
                    "DR22": "",
                    "mark": {
                        "markSheet": sheet_names[0],
                        "markCellType": "report",
                        "markCellPosition": f"{int_to_excel_column(int(idx_y) + 3)}{int(idx_x) + 3}",
                        "markCellReportRegionX": f"{int(idx_x) + 3}",
                        "markCellReportRegionY": f"{int(idx_y + 3)}"
                    }
                })

        output.insert(0, {
            "entry_type": "TABLE",
            "DR01": "范围",
            "DR02": requirement_file_name,
            "DR03": "不适用",
            "DR04": "不适用",
            "DR05": "不适用",
            "DR06": table_name,
            "DR07": table_file_name,
            "DR08": "不适用",
            "DR09": "报送范围",
            "DR10": "不适用",
            "DR11": "不适用",
            "DR12": "不适用",
            "DR13": "不适用",
            "DR14": "不适用",
            "DR15": "不适用",
            "DR16": "不适用",
            "DR17": "",
            "DR18": "不适用",
            "DR19": "月报",
            "DR20": "不适用",
            "DR21": "不脱敏",
            "DR22": "",
            "mark": {
                "markSheet": "",
                "markCellType": "report",
                "markCellPosition": "",
                "markCellReportRegionX": "",
                "markCellReportRegionY": ""
            },
        })

        table_name = sheet_names[0]
        out_dict[table_name] = output

    except (IOError, ValueError, FileNotFoundError) as e:
        logger.error(f"处理失败: {str(e)}")
        return {}

    return out_dict


def get_djz_custom_indicator(input_data: dict):
    file_path = input_data['report_file_path']
    csv_dir = input_data['excel_file_path']
    indicator_data = input_data['indicator_data']
    indicator_data_mark_l = [item['mark'] for item in indicator_data]

    out_dict = {}
    output = []  # 输出的数据
    output_dir = os.path.dirname(file_path)
    # requirement_file
    requirement_file_base_name = os.path.basename(file_path)
    requirement_file_name, rf_ext = os.path.splitext(requirement_file_base_name)
    # table_file
    table_file_base_name = os.path.basename(csv_dir)
    table_file_name, tf_ext = os.path.splitext(table_file_base_name)
    # output_file
    output_file_name = f"{requirement_file_name}_{table_file_name}_process_output.xlsx"
    excel_path = os.path.join(output_dir, output_file_name)
    try:
        # 加载样式 JSON
        json_path = style_json_path
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"未找到样式文件: {json_path}")
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                style_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"样式文件 {json_path} 中的 JSON 格式无效: {str(e)}")

        # 提取基本信息
        sheet_names = get_sheet_names(csv_dir)

        # 提取文档中的 kv信息 和 规则核对信息
        term_pair_dict = {}  # kv信息 （key:专业名词 value:专业名词解释）
        check_rule_dict = {}  # 核对规则信息 （key:科目代码 value:科目代码对应的核对规则）
        submit_basic_info_dict = {}  # 数据报送基础信息（key:报送表 value:报送基础信息）

        workflow = GCPdfSplitWorkflow(
            input_path=file_path,
            output_dir=None
        )
        # 执行工作流
        workflow_exec_results = workflow.execute()
        # 文档解析结果
        # [('output_part_1.pdf', ['A1411', 'A2411']), ('output_part_2.pdf', ['A3702'])]
        split_result = workflow_exec_results['task_extract_form_number']
        # 合并解析数据
        for file_path, tb_names in split_result:
            part_doc_info = extract_doc_info(file_path)
            part_term_pair_dict = part_doc_info['term_pair_dict']
            part_check_rule_dict = part_doc_info['check_rule_dict']
            part_submit_basic_info = part_doc_info['submit_basic_info']
            term_pair_dict.update(part_term_pair_dict)
            check_rule_dict.update(part_check_rule_dict)
            submit_basic_info_dict.update(part_submit_basic_info)

        term_pair_dict_path = os.path.join(output_dir, 'term_pair_dict.txt')
        save_dict_to_txt(term_pair_dict, term_pair_dict_path)

        # 提取table的信息
        records, gc_tb_indicator_names, table_name = read_gc_tb(csv_dir)
        # 构建输出（保持不变）
        for idx_y, gc_tb_indicator_name in enumerate(gc_tb_indicator_names):
            for idx_x, item in enumerate(records):
                indicator_data_mark = {"x": f"{int(idx_x) + 3}", "y": f"{int(idx_y) + 3}"}
                if indicator_data_mark not in indicator_data_mark_l:
                    continue
                output.append({
                    "entry_type": "ITEM",
                    "DR01": "ADS",
                    "DR02": requirement_file_name,
                    "DR03": "不适用",
                    "DR04": "不适用",
                    "DR05": "不适用",
                    "DR06": table_name,
                    "DR07": table_file_name,
                    "DR08": "不适用",
                    "DR09": f"{item['科目名称']}_{gc_tb_indicator_name}",
                    "DR10": f"{item['科目代码']}_{gc_tb_indicator_name}",
                    "DR11": "不适用",
                    "DR12": "不适用",
                    "DR13": "不适用",
                    "DR14": "不适用",
                    "DR15": "不适用",
                    "DR16": submit_basic_info_dict.get(table_file_name, f"{table_file_name}数据报送需求暂无"),
                    "DR17": '维度描述：' + term_pair_dict.get(clean_subject_name(item['科目名称']),
                                                             item['科目名称']) + '\n指标描述：' + term_pair_dict.get(
                        gc_tb_indicator_name, item['科目名称']),
                    "DR18": "不适用",
                    "DR19": "月报",
                    "DR20": "\n".join(check_rule_dict.get(item['科目代码'])) if check_rule_dict.get(item['科目代码'],
                                                                                                    []) else "不适用",
                    "DR21": "不脱敏",
                    "DR22": "",
                    "mark": {
                        "markSheet": sheet_names[0],
                        "markCellType": "report",
                        "markCellPosition": f"{int_to_excel_column(int(idx_y) + 3)}{int(idx_x) + 3}",
                        "markCellReportRegionX": f"{int(idx_x) + 3}",
                        "markCellReportRegionY": f"{int(idx_y) + 3}"
                    }
                })

        table_name = sheet_names[0]
        out_dict[table_name] = output

    except (IOError, ValueError, FileNotFoundError) as e:
        logger.error(f"处理失败: {str(e)}")
        return {}

    return out_dict


class GCExcelParser(BaseDocumentParser):
    def document_unparsing(self, metrics_file: str, description_file: str, sheet_names: str) -> str:
        output = execute_processing_logic(metrics_file, description_file, None)
        return output

    def extraction_report(self, metrics_file: str, description_file: str, sheet_names: str) -> dict:
        output = extraction_report(metrics_file, description_file, sheet_names)
        print(output)
        return output
