"""
Knowledge V2 API 数据模型

统一的API数据模型，基于现有数据库表结构设计
支持完整的CRUD操作和批量处理
"""

from .request_models import *
from .response_models import *

__all__ = [
    # 枚举类型
    "DataLayerEnum",
    "ColumnTypeEnum",
    "IndexTypeEnum",
    "RelationTypeEnum",

    # 实体创建模型
    "SourceDatabaseCreate",
    "IndexDatabaseCreate",
    "SourceTableCreate",
    "IndexTableCreate",
    "SourceColumnCreate",
    "IndexColumnCreate",
    "CodeSetCreate",
    "CodeRelationCreate",
    "CodeValueCreate",

    # 实体更新模型
    "SourceDatabaseUpdate",
    "IndexDatabaseUpdate",
    "SourceTableUpdate",
    "IndexTableUpdate",
    "SourceColumnUpdate",
    "IndexColumnUpdate",
    "CodeSetUpdate",
    "CodeValueUpdate",

    # 统一请求模型
    "UnifiedGetRequest",
    "UnifiedListRequest",
    "BatchCreateRequest",
    "BatchUpdateRequest",
    "BatchDeleteRequest",
    "MetadataSearchRequest",
    "BatchOperationConfig",

    # 实体响应模型
    "SourceDatabaseResponse",
    "IndexDatabaseResponse",
    "SourceTableResponse",
    "IndexTableResponse",
    "SourceColumnResponse",
    "IndexColumnResponse",
    "CodeSetResponse",
    "CodeRelationResponse",
    "CodeValueResponse",

    # 统一响应模型
    "UnifiedResponse",
    "UnifiedListResponse",
    "BatchOperationResponse",
    "MetadataSearchResponse",
    "SystemStatusResponse",
    "ErrorResponse",
    "BatchOperationStats",
    "PaginationInfo"
]
