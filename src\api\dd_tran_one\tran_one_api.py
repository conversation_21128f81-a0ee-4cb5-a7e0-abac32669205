import json
from modules.knowledge.dd import DDCrud
from pydantic import BaseModel
from typing import List, Dict
from service import get_client
from fastapi import APIRouter, HTTPException, Depends
from app.dd_one.get_pri_file import recognize_files
from app.dd_one.getreportlist import getreportlist
from app.dd_one.extraction_report import get_report_data_list, get_report_data
from app.dd_one.custom_project import custom_project_output, get_custom_indicat
import httpx
from typing import List, Dict, Any
import asyncio
from loguru import logger

# 创建路由器
router = APIRouter(tags=["DD0-1文件解析"], prefix="/onetran")


class FilePathInput(BaseModel):
    files_path: List[str]


class FileRecognitionOutput(BaseModel):
    doc_set: str
    time: str
    doc_title: str
    doc_num: str


@router.post("/get_pri_file", response_model=FileRecognitionOutput, summary="主文件提取")
async def get_pri_api_file(file_path: FilePathInput):
    output = recognize_files(file_path.files_path)
    # logger.info('火锅还是得')
    # logger.info(output)
    return output


class ReportListInput(BaseModel):
    files_info: List[dict]
    release_id: str
    report_department: str = ""
    version: str = "V0"  # version信息，默认是V0
    version_scene: str  # "RELEASE/BETA"
    level: str  # ADS还是其他库


class CallbackResponse(BaseModel):
    msg: str
    code: int = 0  # 添加默认code字段


@router.post("/get_report_list", response_model=CallbackResponse, summary="提取出报告列表")
async def get_report_list(file_path_data: ReportListInput):
    """
    提取报告列表并异步触发回调处理。
    立即返回处理中的响应，耗时操作在后台异步执行。
    """
    try:
        # 立即返回响应
        response = CallbackResponse(msg="报告列表处理中", code=0)

        # 异步执行耗时任务
        asyncio.create_task(process_report_list(file_path_data))

        return response

    except Exception as e:
        logger.error(f"初始化报告列表处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail="初始化报告列表处理失败")


async def process_report_list(file_path_data: ReportListInput) -> None:
    """
    异步处理报告列表的耗时操作并调用回调接口。
    """
    try:
        # 步骤 1：获取报告列表
        logger.info("获取报告列表中...")
        if file_path_data.version == "":
            file_path_data.version = "V0"
        outputs_list = await asyncio.to_thread(getreportlist, file_path_data.files_info, file_path_data.version,
                                               file_path_data.version_scene, file_path_data.level)

        # 步骤 2：为输出添加 release_id 和 report_department
        enriched_outputs = [
            {**output, "release_id": file_path_data.release_id, "report_department": file_path_data.report_department}
            for output in outputs_list
        ]
        # enriched_outputs = json.dumps(enriched_outputs, indent=2, ensure_ascii=False)

        headers = {
            "Content-Type": "application/json;charset=UTF-8"
        }

        # 步骤 3：调用回调接口
        callback_url = "http://218.78.129.173:30134/regulatory/release/ai/createReport"
        async with httpx.AsyncClient(timeout=30.0) as client:
            callback_response = await client.post(
                callback_url,
                json=enriched_outputs,
                headers=headers
            )

            if callback_response.status_code != 200:
                logger.error(f"回调接口调用失败，状态码: {callback_response.status_code}")
                return

            callback_data = callback_response.json()
            print(callback_data)
            if not callback_data.get("data", False):
                logger.error(f"回调失败: {callback_data.get('msg', '未知错误')}")

    except Exception as e:
        logger.error(f"异步处理报告列表失败: {str(e)}")


class FileInfoTwo(BaseModel):
    type: str
    file_id: str
    file_path: str


# 根据文件抽取dd返回
class ExtractionReportInput(BaseModel):
    report_code: str
    report_type: str  # 报送类型
    doc_set: str  # 套系类型，1104或者djz，根据这个选择不同的解析方式
    files_info: List[FileInfoTwo]  # 文件信息


def entry_type_tran(entry_type):
    if entry_type == "ITEM":
        return "SUBMISSION"
    elif entry_type == "TABLE":
        return "RANGE"


def report_type_tran(report_type):
    if report_type == "list":
        return "detail"
    elif report_type == "index":
        return "index"


def get_report_detail(report_code):
    report_detail = report_code.split("_")
    report_id, level, version_scene, version = report_detail[0], report_detail[1], report_detail[2], report_detail[
        3]
    return report_id, level, version_scene, version


@router.post("/extraction_report", response_model=CallbackResponse, summary="根据文件提取dd")
async def extraction_report(input_data: ExtractionReportInput):
    """
    根据文件提取dd数据并异步触发回调处理。
    立即返回处理中的响应，耗时操作在后台异步执行。
    """
    try:
        logger.info(f"收到请求: input_data={input_data.dict()}")
        # 立即返回响应
        response = CallbackResponse(msg="报告数据提取中", code=0)

        # 异步执行耗时任务
        asyncio.create_task(process_extraction_report(input_data))

        return response

    except Exception as e:
        logger.error(f"初始化报告提取失败: {str(e)}, input_data={input_data.dict()}")
        raise HTTPException(status_code=500, detail="初始化报告提取失败")


async def process_extraction_report(input_data: ExtractionReportInput) -> None:
    """
    异步处理报告提取的耗时操作并调用回调接口。
    """

    try:
        # 验证 files_info
        if not input_data.files_info:
            logger.error("files_info 为空")
            return

        # report_id, level, version_scene, version = get_report_detail(input_data.report_code)

        # 获取文件路径
        doc_path, csv_path = None, None
        file_dict = {info.type: info.file_path for info in input_data.files_info}
        doc_path = file_dict.get("explain", "")
        csv_path = file_dict.get("example")

        if not csv_path:
            logger.error(f"缺少必要的文件路径: doc_path={doc_path}, csv_path={csv_path}")
            return

        # 步骤 1：获取报告数据
        def get_report_data_wrapper():
            return get_report_data(csv_path, input_data.doc_set, doc_path)

        report_data = await asyncio.to_thread(get_report_data_wrapper)

        # 步骤 2：转换数据
        def get_report_data_list_wrapper():
            return get_report_data_list(report_data)

        output_list_data = await asyncio.to_thread(get_report_data_list_wrapper)

        # 步骤 3：构造输出
        output = {
            "report_code": input_data.report_code,
            "data": output_list_data
        }
        # logger.info(input_data.report_code)
        # for output_data in output_list_data:
        #     logger.info(f"entry_id: {output_data["entry_id"]}, report_code:{input_data.report_code})")
        # report_code = "hsada"
        # print('-' * 100)
        # print(output_list_data)
        # print('-' * 100)
        logger.info(output)

        rdb_client = await get_client("database.rdbs.mysql")

        # 执行插入前先将历史数据给删除掉
        delete_filter = [{"version": input_data.report_code}]
        delete_label = await rdb_client.abatch_delete("biz_dd_pre_distribution", delete_filter)

        # print('----------------')
        # print('删除数据', delete_label)

        # 调用前构造python的表数据，然后插入数据
        insert_data = [{"submission_id": str(output_data["entry_id"]),
                        "submission_type": entry_type_tran(output_data["entry_type"]),
                        "report_type": input_data.report_type,
                        "set": input_data.doc_set,
                        "version": input_data.report_code,
                        "dr01": output_data["DR01"],
                        "dr02": output_data["DR02"],
                        "dr03": output_data["DR03"],
                        "dr04": output_data["DR04"],
                        "dr05": output_data["DR05"],
                        "dr06": output_data["DR06"],
                        "dr07": output_data["DR07"],
                        "dr08": output_data["DR08"],
                        "dr09": output_data["DR09"],
                        "dr10": output_data["DR10"],
                        "dr11": output_data["DR11"],
                        "dr12": output_data["DR12"],
                        "dr13": output_data["DR13"],
                        "dr14": output_data["DR14"],
                        "dr15": output_data["DR15"],
                        "dr16": output_data["DR16"],
                        "dr17": output_data["DR17"],
                        "dr18": output_data["DR18"],
                        "dr19": output_data["DR19"],
                        "dr20": output_data["DR20"],
                        "dr21": output_data["DR21"]
                        } for output_data in output_list_data]

        insert_type = await rdb_client.abatch_insert(table="biz_dd_pre_distribution", data=insert_data, batch_size=10)
        headers = {
            "Content-Type": "application/json;charset=UTF-8"
        }

        if not insert_type.success:
            logger.info(insert_type)
            logger.info(f"全量提取失败，失败的report_code是{input_data.report_code}")
            errors = insert_type.operation_parameters.get("errors", "")
            logger.info(f"失败的原因是{errors}")

        # 步骤 4：调用回调接口
        callback_url = "http://218.78.129.173:30134/regulatory/release/ai/createMainDd"  # 建议移到配置
        async with httpx.AsyncClient(timeout=30.0) as client:
            callback_response = await client.post(
                callback_url,
                json=output,
                headers=headers
            )

            if callback_response.status_code != 200:
                logger.error(
                    f"回调接口调用失败，状态码: {callback_response.status_code}, 响应: {callback_response.text}")
                return

            try:
                callback_data = callback_response.json()
                print(callback_data)
                if not callback_data.get("data", False):
                    logger.error(f"回调失败: {callback_data.get('message', '未知错误')}")
            except ValueError as e:
                logger.error(f"回调响应解析失败: {str(e)}, 响应: {callback_response.text}")

    except Exception as e:
        logger.error(f"异步处理报告提取失败: {str(e)}, input_data={input_data.dict()}")


class IndicatorInfo(BaseModel):
    sheet_name: str  # 对应的sheet——name
    sheet_sort: int  # 当前sheet所属表样的顺序
    col: list  # 当前选取行的所有内容的list [1.1资产方]
    lines: list  # 当前选取列的所有内容的list [A,title1,title2]
    mark: dict  # 当前选取的坐标
    entry_id: str = ""  # 当前ITEM的id，如果没有则为""
    markCellReportRegionX: str = ""
    markCellReportRegionY: str = ""


class CustomProjectInput(BaseModel):
    report_code: str  # 报送code
    report_file_path: str = ""  # 填报文件路径
    excel_file_path: str  # 表样文件路径
    report_type: str  # 报送类型
    doc_set: str  # 套系类型，1104或者djz，根据这个选择不同的解析方式
    indicator_data: List[IndicatorInfo]  # 选定的数据


class CustomProjectOutput(BaseModel):
    report_code: str  # 报送code
    data: List[Dict[str, Any]]  # 自定义项目数据


@router.post("/custom_project", response_model=CustomProjectOutput, summary="根据文件提取自定义项目数据")
async def custom_project(input_data: CustomProjectInput):
    """
    根据文件提取自定义项目数据并异步触发回调处理。
    立即返回处理中的响应，耗时操作在后台异步执行。
    """
    try:
        # logger.info(f"收到请求: input_data={input_data.dict()}")
        # 立即返回响应
        # response = CallbackResponse(msg="自定义项目数据处理中", code=0)

        # 异步执行耗时任务
        output_data = await process_custom_project(input_data)

        return output_data

    except Exception as e:
        logger.error(f"初始化自定义项目处理失败: {str(e)}, input_data={input_data.dict()}")
        raise HTTPException(status_code=500, detail="初始化自定义项目处理失败")


async def process_custom_project(input_data: CustomProjectInput):
    """
    异步处理自定义项目数据的耗时操作并调用回调接口。
    """
    try:
        # 验证 indicator_data
        if not input_data.indicator_data:
            logger.error("indicator_data 为空")
            return

        # report_file_name, report_version = get_report_detail(input_data.report_code)
        old_entry_ids = [file_data.entry_id for file_data in input_data.indicator_data if
                         file_data.entry_id != ""]
        # 验证 doc_set
        valid_doc_sets = {"1104", "djz"}
        if input_data.doc_set not in valid_doc_sets:
            logger.error(f"无效的doc_set: {input_data.doc_set}")
            return

        input_x_y = {
            item['mark']['x'] + ',' + item['mark']['y']: item.get('markCellReportRegionX', '') + '<split>' + item.get(
                'markCellReportRegionY', '')
            for item in input_data.dict()["indicator_data"]}
        rdb_client = await get_client("database.rdbs.mysql")

        # 步骤 1：处理自定义项目数据
        def custom_project_output_wrapper():
            return custom_project_output(input_data.dict())

        custom_project_data = await asyncio.to_thread(custom_project_output_wrapper)
        # 对值做一个转换处理，获取传递进来的参数
        for project_data in custom_project_data["data"]:
            markCellReportRegionX, markCellReportRegionY = project_data['mark'].get('markCellReportRegionX', ''), \
                project_data['mark'].get('markCellReportRegionY', '')
            x, y = markCellReportRegionX, markCellReportRegionY

            # Get entry_id based on x,y coordinates
            markCellReportRegionX, markCellReportRegionY = input_x_y.get(x + ',' + y, '').split('<split>')[0], \
                input_x_y.get(x + ',' + y, '').split('<split>')[1]
            project_data['mark']['markCellReportRegionX'], project_data['mark'][
                'markCellReportRegionY'] = markCellReportRegionX, markCellReportRegionY

        # print(custom_project_data["data"])
        update_data = [{"submission_id": str(output_data["entry_id"]),
                        "submission_type": entry_type_tran(output_data["entry_type"]),
                        "report_type": input_data.report_type,
                        "set": input_data.doc_set,
                        "version": input_data.report_code,
                        "dr01": output_data["DR01"],
                        "dr02": output_data["DR02"],
                        "dr03": output_data["DR03"],
                        "dr04": output_data["DR04"],
                        "dr05": output_data["DR05"],
                        "dr06": output_data["DR06"],
                        "dr07": output_data["DR07"],
                        "dr08": output_data["DR08"],
                        "dr09": output_data["DR09"],
                        "dr10": output_data["DR10"],
                        "dr11": output_data["DR11"],
                        "dr12": output_data["DR12"],
                        "dr13": output_data["DR13"],
                        "dr14": output_data["DR14"],
                        "dr15": output_data["DR15"],
                        "dr16": output_data["DR16"],
                        "dr17": output_data["DR17"],
                        "dr18": output_data["DR18"],
                        "dr19": output_data["DR19"],
                        "dr20": output_data["DR20"],
                        "dr21": output_data["DR21"]
                        } for output_data in custom_project_data["data"]]
        update_dd = []
        insert_dd = []
        for custom_data in update_data:
            if custom_data['submission_id'] in old_entry_ids:
                filter_data = {"submission_id": custom_data["submission_id"], "version": custom_data["version"]}
                update_dd.append({"data": custom_data, "filters": filter_data})
            else:
                insert_dd.append(custom_data)
        if update_dd:
            update_result = await rdb_client.abatch_update("biz_dd_pre_distribution", update_dd)
        if insert_dd:
            insert_result = await rdb_client.abatch_insert(table="biz_dd_pre_distribution", data=insert_dd)
        print(custom_project_data)
        return custom_project_data

    except Exception as e:
        logger.error(f"异步处理自定义项目失败: {str(e)}, input_data={input_data.dict()}")


class GetRuleInput(BaseModel):
    report_code: str  # 报送code
    dept_id: str = ''  # 部门id


class GetRuleOutput(BaseModel):
    report_code: str
    report_rule: list
    entry_rule: list


@router.post("/get_rule", response_model=GetRuleOutput, summary="根据文件提取自定义项目数据")
async def get_rule_project(input_data: GetRuleInput):
    rdb_client = await get_client("database.rdbs.mysql")
    result = await rdb_client.aquery({
        "table": "biz_dd_pre_distribution",
        "columns": ["dr20"],
        "filters": {"version": input_data.report_code}
    })
    check_data = result.data
    output = set()
    for item in check_data:
        output.update(item["dr20"].replace('不适用', '').split('\n'))
    filtered_output = {x for x in output if x.strip() != ''}
    out_data = {"report_code": input_data.report_code,
                "report_rule": list(filtered_output),
                "entry_rule": []
                }
    return out_data
