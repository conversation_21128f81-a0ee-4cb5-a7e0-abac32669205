# DD提交数据最终确定模块 (Finalization)

## 概述

该模块负责处理前端最终确定的内容，直接按照前端提供的数据修改数据库。主要用于各个解读阶段（报表解读、义务解读、业务解读、IT解读）的数据最终落库。

## 核心功能

### 支持的解读阶段

- **EXTRACTION** (报表解读): 已实现
- **DUTY** (义务解读): 待实现
- **BIZ** (业务解读): 待实现  
- **TECH** (IT解读): 待实现

### 支持的处理策略

1. **删除重建策略** (`delete_insert`)
   - 根据 `report_code` (version) 删除所有匹配的记录
   - 然后批量插入新的数据
   - 适用于数据量较大，需要完全替换的场景

2. **批量更新策略** (`batch_update`)
   - 根据 `submission_id` 和 `version` 定位唯一记录
   - 批量更新现有记录
   - 适用于数据量适中，需要精确更新的场景

## 使用方法

### 基本用法

```python
from modules.dd_submission.finalization import create_finalization_api

# 创建API实例
api = create_finalization_api(dd_crud)

# 前端数据格式
frontend_data = {
    "report_code": "G0107_beta_v1.0",
    "dept_id": "DEPT001",  # 报表解读阶段不存在
    "step": "EXTRACTION",
    "data": [
        {
            "entry_id": "SUB001",
            "entry_type": "范围项",
            "DR22": ["DEPT001", "DEPT002"],
            "BDR01": ["DEPT001"],
            "BDR03": "范围描述内容"
        }
    ]
}

# 执行数据最终确定
result = await api.finalize_data(
    report_code=frontend_data["report_code"],
    step=frontend_data["step"],
    data=frontend_data["data"],
    strategy="delete_insert"  # 或 "batch_update"
)

print(result)
# {
#     "success": True,
#     "step": "EXTRACTION",
#     "strategy": "delete_insert",
#     "affected_records": 1,
#     "message": "成功删除重建 1 条记录"
# }
```

### 字段映射

前端字段到数据库字段的映射关系：

| 前端字段 | 数据库字段 | 说明 |
|----------|------------|------|
| `entry_id` | `submission_id` | 提交ID |
| `entry_type` | `submission_type` | 提交类型 |
| `report_code` | `version` | 报表版本 |
| `DR22` | `dr22` | 部门ID列表（转为逗号分隔字符串） |
| `BDR01` | `bdr01` | 部门ID列表（转为逗号分隔字符串） |
| `BDR03` | `bdr03` | 描述内容 |

### 策略选择建议

#### 删除重建策略 (delete_insert)
- ✅ 适用场景：数据量大，需要完全替换
- ✅ 优点：简单可靠，不会有残留数据
- ❌ 缺点：会删除所有相关数据，操作不可逆

#### 批量更新策略 (batch_update)  
- ✅ 适用场景：数据量适中，需要精确更新
- ✅ 优点：只更新指定记录，保留其他数据
- ❌ 缺点：需要确保记录存在，逻辑相对复杂

## 文件结构

```
finalization/
├── __init__.py          # 模块初始化
├── service.py           # 核心业务逻辑
├── api.py              # API接口层
├── examples.py         # 使用示例
└── README.md           # 说明文档
```

## 核心类说明

### FinalizationService
核心业务逻辑类，负责具体的数据处理操作。

主要方法：
- `finalize_extraction_data()`: 处理报表解读数据
- `finalize_duty_data()`: 处理义务解读数据（待实现）
- `finalize_biz_data()`: 处理业务解读数据（待实现）
- `finalize_tech_data()`: 处理IT解读数据（待实现）

### FinalizationAPI
API接口类，提供统一的调用入口。

主要方法：
- `finalize_data()`: 统一的数据最终确定接口

## 性能优化

### 批量操作参数建议

```python
# 高性能配置（服务器资源充足）
batch_size = 1000
max_concurrency = 5

# 保守配置（资源有限）
batch_size = 500
max_concurrency = 3
```

### 监控和日志

模块提供详细的日志记录：
- 操作开始和结束日志
- 数据量和策略信息
- 错误详情和堆栈跟踪
- 性能统计信息

## 错误处理

所有方法都包含完整的错误处理：

```python
{
    "success": False,
    "step": "EXTRACTION",
    "strategy": "delete_insert",
    "affected_records": 0,
    "message": "操作失败: 具体错误信息"
}
```

## 扩展开发

### 添加新的解读阶段

1. 在 `FinalizationService` 中添加对应的方法
2. 在 `FinalizationAPI.finalize_data()` 中添加新的分支
3. 根据需要调整字段映射逻辑

### 添加新的处理策略

1. 在 `FinalizationService` 中添加新的策略方法
2. 更新 `strategy` 参数的类型定义
3. 在相应的处理方法中添加新策略的分支

## 注意事项

1. **数据安全**: 删除重建策略会删除所有匹配的数据，请谨慎使用
2. **事务处理**: 建议在事务中执行操作，确保数据一致性
3. **性能考虑**: 大量数据操作时，建议使用适当的批次大小和并发数
4. **字段映射**: 新增字段时需要更新 `_map_frontend_to_db_fields` 方法

## 待实现功能

- [ ] 义务解读阶段数据处理
- [ ] 业务解读阶段数据处理  
- [ ] IT解读阶段数据处理
- [ ] 更完善的字段映射配置
- [ ] 数据验证和校验
- [ ] 操作审计日志
