"""
DD-B Pipeline集成器

集成nl2sql_pipeline.py，实现具体的Pipeline调用：
- 调用参数：table_ids, user_question(dr09), hint(dr17), column_limit=5, source_type="mysql", is_final=False
- 处理Pipeline输出结果
- 字段映射和转换
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field

from pipeline.nl2sql_pipeline import CompletePipelineExecutor
from modules.knowledge.metadata.crud import MetadataCrud
from modules.dd_submission.dd_b.infrastructure.models import DDBRecord
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBProcessingError,
    handle_async_ddb_errors,
    create_processing_error
)

logger = logging.getLogger(__name__)


@dataclass
class PipelineRequest:
    """Pipeline请求"""
    
    record_id: Optional[int]                    # 记录ID
    table_ids: List[str]                        # 表ID列表
    user_question: str                          # 用户问题（dr09）
    hint: str                                   # 提示信息（dr17）
    
    # Pipeline配置
    column_limit: int = 5                       # 列限制
    source_type: str = "source"                  # 数据源类型 需要日后做判断
    is_final: bool = False                      # 是否最终
    
    # 超时配置
    timeout_seconds: float = 3600.0              # 超时时间 (1小时，很宽泛)


@dataclass
class PipelineResult:
    """Pipeline结果"""
    
    request: PipelineRequest                    # 原始请求
    
    # Pipeline输出
    business_logic: Dict[str, Any] = field(default_factory=dict)    # 业务逻辑
    parser_info: Dict[str, Any] = field(default_factory=dict)       # 解析信息
    sql_candidates: List[str] = field(default_factory=list)         # SQL候选

    # Candidate字段
    candidate_tables: List[str] = field(default_factory=list)       # 候选表
    candidate_columns: Dict[str, Any] = field(default_factory=dict) # 候选字段
    candidate_column_ids: Dict[str, Any] = field(default_factory=dict) # 候选字段ID
    
    # 执行信息
    execution_success: bool = False             # 执行是否成功
    execution_time_ms: float = 0.0              # 执行耗时
    error_message: str = ""                     # 错误消息
    
    # 执行说明
    execution_notes: List[str] = field(default_factory=list)        # 执行说明


@dataclass
class FieldMappingResult:
    """字段映射结果"""
    
    # BDR字段
    BDR09: str = ""                             # 表名列表
    BDR10: str = ""                             # 表中文名
    BDR11: str = ""                             # 列信息
    BDR16: str = ""                             # 业务逻辑字符串
    
    # SDR字段
    SDR01: str = ""                             # 从DR01获取
    SDR02: str = "1"                            # 默认值
    SDR03: str = "CRD"                          # 默认值
    SDR04: str = "不适用"                        # 默认值
    SDR05: str = ""                             # 表名列表（与BDR09相同）
    SDR06: str = ""                             # 表中文名（与BDR10相同）
    SDR07: str = "不适用"                        # 默认值
    SDR08: str = ""                             # 列信息（与BDR11相同）
    SDR09: str = ""                             # 列中文名
    SDR10: str = ""                             # SQL候选
    SDR11: str = "不适用"                        # 默认值
    SDR12: str = ""                             # join信息
    SDR13: str = "不适用"                        # 默认值
    SDR14: str = "不适用"                        # 默认值
    SDR15: str = ""                             # 默认值
    
    # 映射信息
    mapping_success: bool = False               # 映射是否成功
    mapping_notes: List[str] = field(default_factory=list)          # 映射说明


class PipelineIntegrator:
    """Pipeline集成器"""
    
    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化Pipeline集成器
        
        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        
        # 初始化Pipeline执行器
        self.pipeline_executor = CompletePipelineExecutor(default_db_type="mysql")
        
        # 初始化Metadata CRUD
        self.metadata_crud = MetadataCrud(rdb_client, vdb_client, embedding_client)
        
        logger.info("Pipeline集成器初始化完成")
    
    @handle_async_ddb_errors
    async def execute_pipeline(self, request: PipelineRequest) -> PipelineResult:
        """
        执行Pipeline
        
        Args:
            request: Pipeline请求
            
        Returns:
            Pipeline结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始执行Pipeline: record_id={request.record_id}, "
                       f"table_ids={request.table_ids}, question='{request.user_question}'")
            
            # 构建schema_generation_params
            schema_generation_params = {
                "table_ids": request.table_ids,
                "column_limit": request.column_limit,
                "source_type": request.source_type,
                "is_final": request.is_final
            }
            
            # 执行Pipeline
            pipeline_start_time = time.time()
            logger.info(f"🚀 Pipeline启动: record_id={request.record_id}, 时间戳={pipeline_start_time:.3f}")

            pipeline_context = await asyncio.wait_for(
                self.pipeline_executor.execute_with_context(
                    schema_generation_params=schema_generation_params,
                    user_question=request.user_question,
                    hint=request.hint,
                    db_type="mysql"
                ),
                timeout=request.timeout_seconds
            )

            pipeline_end_time = time.time()
            pipeline_duration = pipeline_end_time - pipeline_start_time
            logger.info(f"✅ Pipeline完成: record_id={request.record_id}, 时间戳={pipeline_end_time:.3f}, 耗时={pipeline_duration:.2f}s")
            
            # 检查是否有context属性，如果有则使用context作为实际数据源
            actual_context = pipeline_context
            if 'context' in pipeline_context:
                logger.info(f"  发现context字段，使用pipeline_context['context']作为实际数据源")
                actual_context = pipeline_context['context']

            # 从实际context中提取结果
            business_logic = actual_context.get("business_logic", {})
            parser_info = actual_context.get("parser_info", {})
            sql_candidates = actual_context.get("sql_candidates", [])

            # 从实际context中提取candidate字段
            candidate_tables = actual_context.get("candidate_tables", [])
            candidate_columns = actual_context.get("candidate_columns", {})
            candidate_column_ids = actual_context.get("candidate_column_ids", {})

            # 详细记录Pipeline context内容
            logger.info(f"🔍 Pipeline Context详情 (record_id={request.record_id}):")
            logger.info(f"  context类型: {type(pipeline_context)}")

            # 如果是字典，打印所有键值对
            if isinstance(pipeline_context, dict):
                logger.info(f"  字典所有键: {list(pipeline_context.keys())}")

                # 重点检查candidate相关字段
                logger.info(f"🔍 Candidate字段检查:")
                logger.info(f"  'candidate_tables' in keys: {'candidate_tables' in pipeline_context}")
                logger.info(f"  'candidate_columns' in keys: {'candidate_columns' in pipeline_context}")
                logger.info(f"  'candidate_column_ids' in keys: {'candidate_column_ids' in pipeline_context}")

                # 🚨 断点标记：Pipeline Context检查
                logger.error(f"🚨 BREAKPOINT: Pipeline Context原始数据")
                logger.error(f"🚨 pipeline_context类型: {type(pipeline_context)}")
                logger.error(f"🚨 pipeline_context keys: {list(pipeline_context.keys())}")

                # 检查context字段
                if 'context' in pipeline_context:
                    context_obj = pipeline_context['context']
                    logger.error(f"🚨 context对象类型: {type(context_obj)}")
                    if hasattr(context_obj, '__dict__'):
                        logger.error(f"🚨 context对象属性: {list(context_obj.__dict__.keys())}")
                        logger.error(f"🚨 context.candidate_columns: {getattr(context_obj, 'candidate_columns', 'NOT_FOUND')}")
                        logger.error(f"🚨 context.candidate_column_ids: {getattr(context_obj, 'candidate_column_ids', 'NOT_FOUND')}")
                    elif hasattr(context_obj, 'get'):
                        logger.error(f"🚨 context字典keys: {list(context_obj.keys())}")
                        logger.error(f"🚨 context.candidate_columns: {context_obj.get('candidate_columns', 'NOT_FOUND')}")
                        logger.error(f"🚨 context.candidate_column_ids: {context_obj.get('candidate_column_ids', 'NOT_FOUND')}")
                # 🚨 断点标记结束

                for key, value in pipeline_context.items():
                    if 'candidate' in key.lower():
                        logger.info(f"  🎯 {key}: {value}")
                    else:
                        logger.info(f"  {key}: {value}")



            logger.info(f"  最终business_logic: {business_logic}")
            logger.info(f"  最终parser_info: {parser_info}")
            logger.info(f"  最终sql_candidates: {sql_candidates}")
            logger.info(f"  candidate_tables: {candidate_tables}")
            logger.info(f"  candidate_columns: {candidate_columns}")
            logger.info(f"  candidate_column_ids: {candidate_column_ids}")

            # 🚨 断点标记：Pipeline集成器数据检查
            logger.error(f"🚨 BREAKPOINT: Pipeline集成器数据检查")
            logger.error(f"🚨 actual_context类型: {type(actual_context)}")
            logger.error(f"🚨 actual_context keys: {list(actual_context.keys()) if hasattr(actual_context, 'keys') else 'No keys'}")
            logger.error(f"🚨 candidate_columns = {candidate_columns}")
            logger.error(f"🚨 candidate_column_ids = {candidate_column_ids}")
            logger.error(f"🚨 business_logic = {business_logic}")
            logger.error(f"🚨 parser_info = {parser_info}")
            # 🚨 断点标记结束

            # 检查context中是否有其他相关数据
            important_keys = ['candidate_tables', 'candidate_columns', 'candidate_column_ids', 'db_schema', 'parsed_tables', 'parsed_columns']
            for key in important_keys:
                if hasattr(pipeline_context, 'get'):
                    value = pipeline_context.get(key)
                elif isinstance(pipeline_context, dict):
                    value = pipeline_context.get(key)
                else:
                    value = getattr(pipeline_context, key, None)
                if value:
                    logger.info(f"  {key}: {value}")

            # 如果主要结果为空，尝试从其他字段获取
            if not business_logic and not parser_info and not sql_candidates:
                logger.warning(f"⚠️ 主要结果为空，检查备用字段 (record_id={request.record_id})")

                # 尝试从其他可能的字段获取数据
                alt_sql = pipeline_context.get("sql_query", "") or pipeline_context.get("generated_sql", "")
                if alt_sql:
                    sql_candidates = [alt_sql]
                    logger.info(f"  从备用字段获取SQL: {alt_sql}")

                # 尝试获取表信息
                alt_tables = pipeline_context.get("parsed_tables", []) or pipeline_context.get("candidate_tables", [])
                alt_columns = pipeline_context.get("parsed_columns", []) or pipeline_context.get("candidate_columns", {})

                if alt_tables or alt_columns:
                    parser_info = {
                        "tables": alt_tables,
                        "columns": alt_columns
                    }
                    logger.info(f"  从备用字段获取解析信息: tables={alt_tables}, columns={alt_columns}")
            
            execution_time = (time.time() - start_time) * 1000

            result = PipelineResult(
                request=request,
                business_logic=business_logic,
                parser_info=parser_info,
                sql_candidates=sql_candidates,
                candidate_tables=candidate_tables,
                candidate_columns=candidate_columns,
                candidate_column_ids=candidate_column_ids,
                execution_success=True,
                execution_time_ms=execution_time,
                execution_notes=[
                    f"Pipeline执行成功",
                    f"业务逻辑: {'已生成' if business_logic else '未生成'}",
                    f"解析信息: {'已生成' if parser_info else '未生成'}",
                    f"SQL候选数: {len(sql_candidates)}",
                    f"执行耗时: {execution_time:.2f}ms"
                ]
            )
            
            logger.info(f"Pipeline执行完成: record_id={request.record_id}, "
                       f"成功={result.execution_success}, 耗时={execution_time:.2f}ms")
            
            return result
            
        except asyncio.TimeoutError:
            execution_time = (time.time() - start_time) * 1000
            error_msg = f"Pipeline执行超时: {request.timeout_seconds}s"
            logger.error(f"Pipeline执行超时: record_id={request.record_id}, "
                        f"耗时={execution_time:.2f}ms")
            
            return PipelineResult(
                request=request,
                execution_success=False,
                execution_time_ms=execution_time,
                error_message=error_msg,
                execution_notes=[f"Pipeline执行超时: {request.timeout_seconds}s"]
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            error_msg = f"Pipeline执行失败: {str(e)}"
            logger.error(f"Pipeline执行失败: record_id={request.record_id}, "
                        f"error={e}, 耗时={execution_time:.2f}ms")
            
            return PipelineResult(
                request=request,
                execution_success=False,
                execution_time_ms=execution_time,
                error_message=error_msg,
                execution_notes=[f"Pipeline执行异常: {str(e)}"]
            )
    
    @handle_async_ddb_errors
    async def map_pipeline_result_to_fields(
        self, 
        pipeline_result: PipelineResult,
        original_record: DDBRecord
    ) -> FieldMappingResult:
        """
        将Pipeline结果映射到字段
        
        Args:
            pipeline_result: Pipeline结果
            original_record: 原始记录
            
        Returns:
            字段映射结果
        """
        try:
            logger.info(f"开始字段映射: record_id={original_record.id}")
            
            mapping_result = FieldMappingResult()
            mapping_notes = []
            
            if not pipeline_result.execution_success:
                mapping_result.mapping_success = False
                mapping_result.mapping_notes = [f"Pipeline执行失败: {pipeline_result.error_message}"]
                return mapping_result
            
            # 提取Pipeline输出
            business_logic = pipeline_result.business_logic
            parser_info = pipeline_result.parser_info
            sql_candidates = pipeline_result.sql_candidates

            # 详细记录Pipeline结果内容
            logger.info(f"🔍 Pipeline结果详情 (record_id={original_record.id}):")
            logger.info(f"  business_logic类型: {type(business_logic)}, 内容: {business_logic}")
            logger.info(f"  parser_info类型: {type(parser_info)}, 内容: {parser_info}")
            logger.info(f"  sql_candidates类型: {type(sql_candidates)}, 内容: {sql_candidates}")

            # 检查Pipeline结果是否为空
            if not business_logic and not parser_info and not sql_candidates:
                logger.warning(f"⚠️ Pipeline结果全部为空! (record_id={original_record.id})")
            else:
                logger.info(f"✅ Pipeline结果非空，开始字段映射 (record_id={original_record.id})")
            
            # 1. 处理BDR09: parser_info.tables (应该是list格式)
            if parser_info.get("tables"):
                tables = parser_info["tables"]
                if isinstance(tables, list):
                    mapping_result.BDR09 = str(tables)  # 存储为字符串包裹的list
                    mapping_notes.append(f"BDR09映射成功: {len(tables)}个表")
                else:
                    mapping_result.BDR09 = str([tables])  # 单个表也转为list格式
                    mapping_notes.append("BDR09映射成功: 单个表")
            
            # 2. 处理BDR10: 查询表中文名 (应该是list格式)
            if mapping_result.BDR09:
                # 从BDR09的字符串list中提取表名
                import ast
                try:
                    table_names = ast.literal_eval(mapping_result.BDR09)
                    if not isinstance(table_names, list):
                        table_names = [table_names]
                except:
                    table_names = [mapping_result.BDR09]

                table_cn_names = []

                for table_name in table_names:
                    try:
                        # 使用source_table方法查询表中文名
                        table_info = await self.metadata_crud.get_source_table(table_name=table_name.strip())
                        if table_info and table_info.get("table_name_cn"):
                            table_cn_names.append(table_info["table_name_cn"])
                        else:
                            table_cn_names.append(table_name.strip())  # 使用原名作为备选
                    except Exception as e:
                        logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                        table_cn_names.append(table_name.strip())

                mapping_result.BDR10 = str(table_cn_names)  # 存储为字符串包裹的list
                mapping_notes.append(f"BDR10映射成功: {len(table_cn_names)}个表中文名")
            
            # 3. 处理BDR11: parser_info.columns (应该是dict格式)
            if parser_info.get("columns"):
                columns = parser_info["columns"]
                if isinstance(columns, dict):
                    # 保持dict格式，存储为字符串包裹的dict
                    mapping_result.BDR11 = str(columns)
                elif isinstance(columns, list):
                    # 将list转换为dict格式 {column: ""}
                    column_dict = {col: "" for col in columns}
                    mapping_result.BDR11 = str(column_dict)
                else:
                    mapping_result.BDR11 = str({"unknown": str(columns)})
                mapping_notes.append("BDR11映射成功: 列信息")
            
            # 4. 处理BDR16: business_logic拼接字符串
            if business_logic:
                bdr16_parts = []
                
                if business_logic.get("表范围"):
                    tables = business_logic["表范围"]
                    if isinstance(tables, list):
                        bdr16_parts.append(f"表范围：【{','.join(tables)}】")
                    else:
                        bdr16_parts.append(f"表范围：【{tables}】")
                
                if business_logic.get("计算逻辑"):
                    bdr16_parts.append(f"计算逻辑：{business_logic['计算逻辑']}")
                
                if business_logic.get("条件"):
                    bdr16_parts.append(f"条件：{business_logic['条件']}")
                
                if business_logic.get("维度"):
                    bdr16_parts.append(f"维度：{business_logic['维度']}")
                
                if business_logic.get("整体逻辑描述"):
                    bdr16_parts.append(f"整体逻辑描述：{business_logic['整体逻辑描述']}")
                
                mapping_result.BDR16 = "\n".join(bdr16_parts)
                mapping_notes.append("BDR16映射成功: 业务逻辑字符串")
            
            # 5. 处理SDR字段
            # SDR01: 从原始记录获取DR01
            if hasattr(original_record, 'dr01') and original_record.dr01:
                mapping_result.SDR01 = original_record.dr01
            
            # SDR05: 与BDR09相同
            mapping_result.SDR05 = mapping_result.BDR09
            
            # SDR06: 与BDR10相同
            mapping_result.SDR06 = mapping_result.BDR10
            
            # SDR08: 与BDR11相同
            mapping_result.SDR08 = mapping_result.BDR11
            
            # SDR09: 查询列中文名 (应该是dict格式: {column_name: column_name_cn})
            if mapping_result.SDR08:
                # 从SDR08(BDR11)中提取列名，查询中文名
                try:
                    import ast
                    # 解析SDR08中的字典
                    columns_dict = ast.literal_eval(mapping_result.SDR08)
                    if isinstance(columns_dict, dict):
                        column_cn_dict = {}

                        for column_name in columns_dict.keys():
                            try:
                                # 使用source_column方法查询字段中文名
                                column_info = await self.metadata_crud.get_source_column(column_name=column_name.strip())
                                if column_info and column_info.get("column_name_cn"):
                                    column_cn_dict[column_name] = column_info["column_name_cn"]
                                else:
                                    column_cn_dict[column_name] = column_name.strip()  # 使用原名作为备选
                            except Exception as e:
                                logger.warning(f"查询字段中文名失败: {column_name}, error={e}")
                                column_cn_dict[column_name] = column_name.strip()

                        mapping_result.SDR09 = str(column_cn_dict)  # 存储为字符串包裹的dict
                        mapping_notes.append(f"SDR09映射成功: {len(column_cn_dict)}个字段中文名")
                    else:
                        mapping_result.SDR09 = mapping_result.SDR08
                        mapping_notes.append("SDR09映射成功: 使用原始列信息")
                except Exception as e:
                    logger.warning(f"查询列中文名失败: {e}")
                    mapping_result.SDR09 = mapping_result.SDR08
            
            # SDR10: SQL候选
            if sql_candidates:
                mapping_result.SDR10 = sql_candidates[0] if sql_candidates else ""
                mapping_notes.append("SDR10映射成功: SQL候选")
            
            # SDR12: parser_info.join
            if parser_info.get("join"):
                join_info = parser_info["join"]
                if isinstance(join_info, list):
                    mapping_result.SDR12 = ",".join(join_info)
                else:
                    mapping_result.SDR12 = str(join_info)
                mapping_notes.append("SDR12映射成功: join信息")
            
            mapping_result.mapping_success = True
            mapping_result.mapping_notes = mapping_notes
            
            # 详细记录字段映射结果
            logger.info(f"字段映射完成: record_id={original_record.id}, "
                       f"成功={mapping_result.mapping_success}")
            logger.info(f"📋 字段映射详情 (record_id={original_record.id}):")

            # 记录每个字段的映射结果
            field_mappings = {
                'BDR09': mapping_result.BDR09,
                'BDR10': mapping_result.BDR10,
                'BDR11': mapping_result.BDR11,
                'BDR16': mapping_result.BDR16
            }
            for field_name, field_value in field_mappings.items():
                if field_value:  # 只记录非空字段
                    # 截断过长的值用于日志显示
                    display_value = str(field_value)[:100] + "..." if len(str(field_value)) > 100 else str(field_value)
                    logger.info(f"  ✅ {field_name}: '{display_value}'")
                else:
                    logger.info(f"  ⚠️ {field_name}: 空值")

            logger.info(f"📊 映射统计: 总字段={len(field_mappings)}, 非空字段={len([v for v in field_mappings.values() if v])}")
            
            return mapping_result
            
        except Exception as e:
            logger.error(f"字段映射失败: record_id={original_record.id}, error={e}")
            
            return FieldMappingResult(
                mapping_success=False,
                mapping_notes=[f"字段映射异常: {str(e)}"]
            )
    
    @handle_async_ddb_errors
    async def execute_pipeline_and_map_fields(
        self,
        record: DDBRecord,
        table_ids: List[str],
        dr09: str,
        dr17: str
    ) -> Tuple[PipelineResult, FieldMappingResult]:
        """
        执行Pipeline并映射字段的便捷方法
        
        Args:
            record: 原始记录
            table_ids: 表ID列表
            dr09: 数据项名称
            dr17: 需求口径
            
        Returns:
            (Pipeline结果, 字段映射结果)
        """
        # 创建Pipeline请求
        pipeline_request = PipelineRequest(
            record_id=record.id,
            table_ids=table_ids,
            user_question=dr09,
            hint=dr17
        )
        
        # 执行Pipeline
        pipeline_result = await self.execute_pipeline(pipeline_request)
        
        # 映射字段
        field_mapping = await self.map_pipeline_result_to_fields(pipeline_result, record)
        
        return pipeline_result, field_mapping


# 便捷函数
def create_pipeline_integrator(
    rdb_client: Any, 
    vdb_client: Any = None, 
    embedding_client: Any = None
) -> PipelineIntegrator:
    """创建Pipeline集成器实例"""
    return PipelineIntegrator(rdb_client, vdb_client, embedding_client)


async def execute_pipeline_for_record(
    rdb_client: Any,
    record: DDBRecord,
    table_ids: List[str],
    dr09: str,
    dr17: str,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> Tuple[PipelineResult, FieldMappingResult]:
    """为记录执行Pipeline的便捷函数"""
    integrator = create_pipeline_integrator(rdb_client, vdb_client, embedding_client)
    return await integrator.execute_pipeline_and_map_fields(record, table_ids, dr09, dr17)
