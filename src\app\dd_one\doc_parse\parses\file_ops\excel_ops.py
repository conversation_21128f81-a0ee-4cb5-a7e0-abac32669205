#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：excel_ops.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/10 13:43 
@Desc    ：大集中excel文件解析方法
"""
import os
from typing import List

import pandas as pd


def read_gc_tb(metrics_file:str) -> [dict,list]:
    records = []
    df = pd.read_excel(metrics_file, header=None)
    # 获取第一行(index=0)作为列名
    table_name = df.iloc[0, 0]
    key_names = list(df.iloc[1])
    gc_tb_id = key_names[0]  # 大集中表中的id
    gc_tb_name = key_names[1]  # 大集中表中对应id的名称
    gc_tb_indicator_names = key_names[2:]  # 大集中表的维度名称
    gc_tb_data = df[2:]
    for index, row in gc_tb_data.iterrows():
        if pd.isna(row[0]) or pd.isna(row[1]):
            continue
        records.append({
            gc_tb_id: row[0],
            gc_tb_name: str(row[1]).strip('\u3000').strip().replace('\u3000', '')
        })

    return records, gc_tb_indicator_names, table_name

def find_files_by_names(directory: str, names: List[str]) -> List[str]:
    """
    在指定目录中查找特定名称的文件，优先返回 .xls 文件，其次是 .xlsx 文件，否则返回空列表。

    Args:
        directory: 要搜索的目录路径
        names: 要查找的文件名列表（不含扩展名）

    Returns:
        包含匹配文件的绝对路径的列表，按 names 中的顺序排列
    """
    result = []

    # 检查目录是否存在
    if not os.path.exists(directory):
        return result

    # 获取目录中所有文件的基本名称（不含扩展名）和扩展名
    files = {}
    for entry in os.scandir(directory):
        if entry.is_file():
            base_name, ext = os.path.splitext(entry.name)
            files.setdefault(base_name, []).append(ext.lower())

    # 遍历要查找的每个名称
    for name in names:
        file_exts = files.get(name, [])

        # 优先查找 .xls 文件
        if '.xls' in file_exts:
            result.append(os.path.join(directory, f"{name}.xls"))
        # 其次查找 .xlsx 文件
        elif '.xlsx' in file_exts:
            result.append(os.path.join(directory, f"{name}.xlsx"))
        # 都不存在则添加空字符串
        else:
            continue

    return result

def get_sheet_names(file_path: str) -> list[str]:
    """获取 Excel 文件中所有工作表的名称"""
    try:
        # 实例化 ExcelFile 对象
        excel_file = pd.ExcelFile(file_path)
        # 获取所有表名
        sheet_names = excel_file.sheet_names
        return sheet_names
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []

if __name__ == '__main__':
    # csv_dir = 'D:/project/work/hsbc/文件解析/code/src/doc_parse/parses/file_ops/A1411.xlsx'
    csv_dir = 'D:/project/work/hsbc/文件解析/code/src/file/gc/table/A1411.xls'
    records, gc_tb_indicator_names, table_name = read_gc_tb(csv_dir)
    for record in records[:5]:
        key_n = record['科目名称']
        print(key_n)
        print(record)
    # print(gc_tb_indicator_names)
    #
    # directory = r"D:/project/work/hsbc/文件解析/code/src/file/gc/table"
    # names = ['A1411', 'A2411', 'A1231231231']
    #
    # file_paths = find_files_by_names(directory, names)
    #
    # print("查找结果:")
    # for name, path in zip(names, file_paths):
    #     print(f"{name}: {path if path else '未找到'}")