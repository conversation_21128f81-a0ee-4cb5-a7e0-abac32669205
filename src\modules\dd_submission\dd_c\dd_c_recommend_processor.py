"""
DD-C推荐处理器

基于dd_b_recommend_processor的DD-C版本，专门用于推荐功能：
1. 输入：entry_id (submission_id)、report_code (version)
2. 处理：查询数据 → 向量搜索（limit=1，无阈值）
3. 输出：sdr01-sdr15字段 + entry_id + entry_type 的dict格式

核心流程：
查询submission数据 → 提取entry信息和查询文本 → 向量搜索推荐 → 格式化输出
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class RecommendResult:
    """推荐处理结果"""
    success: bool = False
    entry_id: str = ""
    entry_type: str = "ITEM"
    processing_time: float = 0.0
    
    # 推荐的SDR字段数据
    recommended_data: Dict[str, str] = field(default_factory=dict)
    
    # 调试信息
    debug_info: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)


class DDCRecommendProcessor:
    """DD-C推荐处理器"""
    
    def __init__(
        self,
        rdb_client: Any,
        vdb_client: Any = None,
        embedding_client: Any = None
    ):
        """
        初始化推荐处理器
        
        Args:
            rdb_client: RDB客户端 (get_client('database.rdbs.mysql'))
            vdb_client: VDB客户端 (get_client('database.vdbs.pgvector'))
            embedding_client: 嵌入客户端 (get_client('model.embeddings.moka-m3e-base'))
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        logger.info(f"DD-C推荐处理器初始化完成")

    async def recommend_logic(
        self,
        entry_id: str,
        report_code: str
    ) -> RecommendResult:
        """
        执行推荐逻辑
        
        Args:
            entry_id: 条目ID（submission_id）
            report_code: 报告代码（version）
            
        Returns:
            RecommendResult: 推荐处理结果
        """
        start_time = time.time()
        result = RecommendResult(entry_id=entry_id)

        try:
            logger.info(f"🚀 开始DD-C推荐处理: entry_id={entry_id}, report_code={report_code}")

            # 1. 查询指定submission的数据
            logger.info("1️⃣ 查询submission数据...")
            query_start_time = time.time()
            submission_data = await self._query_submission_data(entry_id, report_code)
            query_end_time = time.time()
            logger.info(f"1️⃣ 查询submission数据完成: 耗时={query_end_time-query_start_time:.2f}s")

            result.debug_info['submission_data_found'] = bool(submission_data)
            
            if not submission_data:
                logger.warning("未找到指定的submission记录")
                result.success = False
                return result
            
            # 2. 提取entry_type和查询文本
            logger.info("2️⃣ 提取entry信息和查询文本...")
            entry_type = self._extract_entry_type(submission_data)
            dr09, dr17 = submission_data.get('dr09', ''), submission_data.get('dr17', '')
            
            result.entry_type = entry_type
            result.debug_info['dr09'] = dr09
            result.debug_info['dr17'] = dr17
            
            logger.info(f"提取信息: entry_type={entry_type}, dr09='{dr09}', dr17='{dr17}'")
            
            if not dr09 and not dr17:
                logger.warning("dr09和dr17都为空，无法进行向量搜索")
                result.success = False
                return result
            
            # 3. 向量搜索推荐
            logger.info("3️⃣ 向量搜索推荐...")
            vector_start_time = time.time()
            recommend_data = await self._vector_search_recommend_by_text(dr09, dr17)
            vector_end_time = time.time()
            logger.info(f"3️⃣ 向量搜索推荐完成: 耗时={vector_end_time-vector_start_time:.2f}s")
            
            result.debug_info['vector_search_completed'] = True
            
            if not recommend_data:
                logger.warning("向量搜索未找到推荐数据")
                result.success = False
                return result
            
            # 4. 格式化输出
            logger.info("4️⃣ 格式化输出...")
            formatted_data = await self._format_recommend_output(recommend_data, entry_id, entry_type)
            
            # 设置结果
            result.recommended_data = formatted_data
            result.success = True
            
            logger.info(f"✅ DD-C推荐处理完成: entry_id={entry_id}, entry_type={entry_type}")
            
        except Exception as e:
            error_msg = f"推荐处理失败: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            result.success = False
        
        result.processing_time = time.time() - start_time
        return result
    
    async def _query_submission_data(
        self,
        entry_id: str,
        report_code: str
    ) -> Dict[str, Any]:
        """查询指定submission的数据"""
        try:
            # 使用DD CRUD查询
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)
            
            # 1. 根据submission_id和version查询post_distribution表中的唯一记录
            conditions = [{"submission_id": entry_id, "version": report_code}]
            records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions,
                batch_size=1,
                max_concurrency=1
            )

            logger.info(f"查询post_distribution: submission_id={entry_id}, version={report_code}")
            logger.info(f"查询到 {len(records)} 条post_distribution记录")

            if not records:
                logger.warning(f"未找到submission_id={entry_id}, version={report_code}的记录")
                return {}

            # 应该只有一条记录
            post_record = records[0]
            pre_distribution_id = post_record.get('pre_distribution_id')
            submission_type = post_record.get('submission_type', '')
            
            logger.info(f"找到记录: pre_distribution_id={pre_distribution_id}, submission_type={submission_type}")

            if not pre_distribution_id:
                logger.warning("pre_distribution_id为空")
                return {
                    'submission_id': entry_id,
                    'version': report_code,
                    'submission_type': submission_type,
                    'dr09': '',
                    'dr17': ''
                }

            # 2. 根据pre_distribution_id查询pre_distribution表获取dr09和dr17
            pre_conditions = [{"id": pre_distribution_id}]
            pre_records = await dd_crud.batch_query_pre_distributions(
                conditions_list=pre_conditions,
                batch_size=1,
                max_concurrency=1
            )

            logger.info(f"查询pre_distribution: pre_distribution_id={pre_distribution_id}")
            logger.info(f"查询到 {len(pre_records)} 条pre_distribution记录")

            dr09, dr17 = '', ''
            if pre_records:
                pre_record = pre_records[0]
                dr09 = pre_record.get('dr09', '')
                dr17 = pre_record.get('dr17', '')
                logger.info(f"获取到dr字段: dr09='{dr09}', dr17='{dr17}'")
            else:
                logger.warning(f"未找到pre_distribution_id={pre_distribution_id}的记录")

            # 3. 组装返回数据
            result_data = {
                'submission_id': entry_id,
                'version': report_code,
                'submission_type': submission_type,
                'pre_distribution_id': pre_distribution_id,
                'dr09': dr09,
                'dr17': dr17
            }

            logger.info(f"submission数据查询完成: {result_data}")
            return result_data
            
        except Exception as e:
            logger.error(f"submission数据查询失败: {e}")
            return {}
    
    def _extract_entry_type(self, submission_data: Dict[str, Any]) -> str:
        """
        从submission数据中提取entry_type
        
        Args:
            submission_data: submission数据
            
        Returns:
            str: entry_type
        """
        try:
            submission_type = submission_data.get('submission_type', '')
            
            if submission_type == 'RANGE':
                entry_type = 'TABLE'
            elif submission_type == 'SUBMISSION':
                entry_type = 'ITEM'
            else:
                # 默认值
                entry_type = 'ITEM'
                logger.warning(f"未知的submission_type: {submission_type}, 使用默认值ITEM")
            
            logger.info(f"Entry类型转换: submission_type={submission_type} → entry_type={entry_type}")
            
            return entry_type
            
        except Exception as e:
            logger.error(f"提取entry_type失败: {e}")
            return 'ITEM'

    async def _vector_search_recommend_by_text(
        self,
        dr09: str,
        dr17: str
    ) -> Dict[str, Any]:
        """根据dr09和dr17文本进行向量搜索（与核心处理器完全一致的方法）"""
        if not self.vdb_client or not self.embedding_client:
            logger.warning("向量搜索客户端未配置，跳过向量搜索")
            return {}

        try:
            # 提取查询文本（完全参考核心处理器的方法）
            search_queries = []
            query_text = f"{dr09} {dr17}".strip()
            if query_text:
                search_queries.append({
                    'record_id': 'recommend_query',
                    'dr09': dr09,
                    'dr17': dr17,
                    'query_text': query_text
                })

            if not search_queries:
                logger.warning("没有有效的查询文本")
                return {}

            logger.info(f"准备批量向量搜索: {len(search_queries)} 个查询")

            # 使用现有的向量搜索仓库（与核心处理器完全相同）
            from modules.knowledge.dd.vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)

            # 构建批量搜索请求（与核心处理器完全相同）
            search_requests = []
            for query in search_queries:
                if query['query_text']:
                    search_requests.append({
                        'query_text': query['query_text'],
                        'field_code': 'dr09',  # 主要搜索字段
                        'limit': 1,
                        'min_score': 0
                    })

            # 执行批量搜索（与核心处理器完全相同）
            batch_results = await vector_repo.batch_search_similar_vectors(search_requests)

            logger.info(f"批量向量搜索完成: {len(batch_results)} 个结果集")

            if batch_results and len(batch_results) > 0 and len(batch_results[0]) > 0:
                # 取第一个结果集的第一个结果（最高相似度）
                vector_result = batch_results[0][0]
                logger.info(f"向量搜索推荐完成: 找到最佳匹配，相似度={vector_result.get('score', 0)}")
                logger.info(f"向量搜索结果包含字段: {list(vector_result.keys())}")

                # 获取data_row_id用于查询完整数据
                data_row_id = vector_result.get('data_row_id')
                if not data_row_id:
                    logger.warning("向量搜索结果缺少data_row_id")
                    return {}

                logger.info(f"使用data_row_id={data_row_id}查询dd_submission_data获取完整数据")

                # 通过data_row_id查询dd_submission_data表获取完整数据（包含sdr01-sdr15）
                full_data = await self._get_full_submission_data(data_row_id)

                if full_data:
                    # 合并向量搜索信息和完整数据
                    complete_result = {
                        **full_data,
                        'vector_score': vector_result.get('score', 0),
                        'vector_similarity': vector_result.get('similarity', 0),
                        'data_row_id': data_row_id
                    }

                    # 验证是否包含sdr字段
                    sdr_fields = [f"sdr{i:02d}" for i in range(1, 16)]
                    available_sdr_fields = [field for field in sdr_fields if field in complete_result]
                    logger.info(f"完整数据包含的SDR字段: {available_sdr_fields}")

                    return complete_result
                else:
                    logger.warning(f"未找到data_row_id={data_row_id}的完整数据")
                    return {}
            else:
                logger.warning("向量搜索未找到匹配结果")
                return {}

        except Exception as e:
            logger.error(f"向量搜索推荐失败: {e}")
            return {}

    async def _get_full_submission_data(self, data_row_id: int) -> Dict[str, Any]:
        """
        根据data_row_id获取dd_submission_data表的完整数据

        Args:
            data_row_id: dd_submission_data表的主键ID

        Returns:
            完整的submission数据（包含sdr01-sdr15字段）
        """
        try:
            # 使用DD CRUD查询dd_submission_data表
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            logger.info(f"查询dd_submission_data: data_row_id={data_row_id}")

            # 使用get_submission_data方法（通过主键ID查询）
            submission_data = await dd_crud.get_submission_data(submission_pk=data_row_id)

            if submission_data:
                logger.info(f"成功获取完整数据: 包含{len(submission_data)}个字段")
                logger.debug(f"数据字段: {list(submission_data.keys())}")
                return submission_data
            else:
                logger.warning(f"未找到data_row_id={data_row_id}的数据")
                return {}

        except Exception as e:
            logger.error(f"获取完整submission数据失败: data_row_id={data_row_id}, error={e}")
            return {}

    async def _format_recommend_output(
        self,
        recommend_data: Dict[str, Any],
        entry_id: str,
        entry_type: str
    ) -> Dict[str, str]:
        """格式化推荐输出（包含DR01-DR22, BDR01-BDR17, SDR01-SDR15 + entry_id + entry_type）"""
        try:
            # 基础字段
            output = {
                "entry_id": entry_id,
                "entry_type": entry_type
            }

            # 添加DR01-DR22字段（从小写提取，赋值大写）
            for i in range(1, 23):  # dr01 到 dr22
                dr_field_lower = f"dr{i:02d}"  # 小写提取
                dr_field_upper = f"DR{i:02d}"  # 大写赋值
                output[dr_field_upper] = recommend_data.get(dr_field_lower, "")

            # 添加BDR01-BDR17字段（从小写提取，赋值大写）
            for i in range(1, 18):  # bdr01 到 bdr17
                bdr_field_lower = f"bdr{i:02d}"  # 小写提取
                bdr_field_upper = f"BDR{i:02d}"  # 大写赋值
                output[bdr_field_upper] = recommend_data.get(bdr_field_lower, "")

            # 添加SDR01-SDR15字段（从小写提取，赋值大写）
            for i in range(1, 16):  # sdr01 到 sdr15
                sdr_field_lower = f"sdr{i:02d}"  # 小写提取
                sdr_field_upper = f"SDR{i:02d}"  # 大写赋值
                output[sdr_field_upper] = recommend_data.get(sdr_field_lower, "")

            logger.info(f"DD-C推荐输出格式化完成: entry_id={entry_id}, 包含{len(output)}个字段")
            return output

        except Exception as e:
            logger.error(f"DD-C推荐输出格式化失败: {e}")
            # 返回基础格式
            output = {
                "entry_id": entry_id,
                "entry_type": entry_type
            }
            # 空的字段
            for i in range(1, 23):  # DR01-DR22
                dr_field_upper = f"DR{i:02d}"
                output[dr_field_upper] = ""
            for i in range(1, 18):  # BDR01-BDR17
                bdr_field_upper = f"BDR{i:02d}"
                output[bdr_field_upper] = ""
            for i in range(1, 16):  # SDR01-SDR15
                sdr_field_upper = f"SDR{i:02d}"
                output[sdr_field_upper] = ""
            return output


async def recommend_dd_c(
    rdb_client: Any,
    entry_id: str,
    report_code: str,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> RecommendResult:
    """
    DD-C推荐便捷函数

    Args:
        rdb_client: RDB客户端
        entry_id: 条目ID（submission_id）
        report_code: 报告代码（version）
        vdb_client: VDB客户端
        embedding_client: 嵌入客户端

    Returns:
        RecommendResult: 推荐处理结果
    """
    processor = DDCRecommendProcessor(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client
    )

    return await processor.recommend_logic(entry_id, report_code)
