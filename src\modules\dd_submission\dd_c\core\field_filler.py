"""
DD-B字段填充引擎

负责根据业务规则填充空字段的核心逻辑
"""

import logging
from typing import List, Dict, Any, Tuple
from copy import deepcopy

from modules.dd_submission.dd_b.infrastructure.models import (
    DDBRecord,
    FieldFillInfo,
    FieldStatusEnum
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBConstants, DDBUtils
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBProcessingError,
    handle_ddb_errors,
    create_processing_error
)

logger = logging.getLogger(__name__)


class FieldFiller:
    """字段填充引擎"""
    
    def __init__(self):
        """初始化字段填充引擎"""
        self.fill_rules = DDBConstants.DEFAULT_FILL_VALUES
        self.main_check_fields = DDBConstants.MAIN_CHECK_FIELDS
        self.group1_fields = DDBConstants.FILL_GROUP_1_FIELDS
        self.group2_fields = DDBConstants.FILL_GROUP_2_FIELDS
    
    @handle_ddb_errors
    def check_main_fields_complete(self, record: DDBRecord) -> bool:
        """
        检查主要字段是否完整
        
        Args:
            record: DD-B记录
            
        Returns:
            是否完整
        """
        return record.is_main_fields_complete()
    
    @handle_ddb_errors
    def analyze_fill_requirements(self, record: DDBRecord) -> Dict[str, Any]:
        """
        分析填充需求
        
        Args:
            record: DD-B记录
            
        Returns:
            填充需求分析结果
        """
        analysis = {
            "main_fields_complete": self.check_main_fields_complete(record),
            "needs_fill": False,
            "group1_empty_fields": record.get_empty_group1_fields(),
            "group2_empty_fields": record.get_empty_group2_fields(),
            "total_empty_fields": 0
        }
        
        # 计算总的空字段数
        total_empty = len(analysis["group1_empty_fields"]) + len(analysis["group2_empty_fields"])
        analysis["total_empty_fields"] = total_empty
        analysis["needs_fill"] = total_empty > 0
        
        logger.debug(f"记录 {record.id} 填充需求分析: {analysis}")
        
        return analysis
    
    @handle_ddb_errors
    def fill_record_fields(self, record: DDBRecord) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        填充记录字段
        
        Args:
            record: 原始记录
            
        Returns:
            (填充后的记录, 填充信息列表)
        """
        # 创建记录副本
        filled_record = deepcopy(record)
        fill_details = []
        
        try:
            # 分析填充需求
            analysis = self.analyze_fill_requirements(record)
            
            if not analysis["needs_fill"]:
                logger.debug(f"记录 {record.id} 无需填充")
                return filled_record, fill_details
            
            # 填充第一组字段
            group1_fills = self._fill_group1_fields(
                filled_record, 
                analysis["group1_empty_fields"]
            )
            fill_details.extend(group1_fills)
            
            # 填充第二组字段
            group2_fills = self._fill_group2_fields(
                filled_record,
                analysis["group2_empty_fields"]
            )
            fill_details.extend(group2_fills)
            
            logger.info(f"记录 {record.id} 填充完成，共填充 {len(fill_details)} 个字段")

            # 详细记录填充结果
            if fill_details:
                logger.info(f"📋 字段填充详情 (record_id={record.id}):")
                for fill_info in fill_details:
                    logger.info(f"  ✅ {fill_info.field_name}: '{fill_info.original_value}' → '{fill_info.filled_value}' ({fill_info.fill_reason})")
            else:
                logger.info(f"📋 无字段需要填充 (record_id={record.id})")
            
            return filled_record, fill_details
            
        except Exception as e:
            logger.error(f"填充记录 {record.id} 时发生错误: {e}")
            raise create_processing_error(
                f"填充记录字段失败: {str(e)}",
                processing_step="field_filling",
                record_id=record.id
            )
    
    def _fill_group1_fields(self, record: DDBRecord, empty_fields: List[str]) -> List[FieldFillInfo]:
        """
        填充第一组字段 (brd06, brd07, brd08)
        
        Args:
            record: 记录对象
            empty_fields: 空字段列表
            
        Returns:
            填充信息列表
        """
        fill_details = []
        
        for field_name in empty_fields:
            if field_name in self.group1_fields:
                original_value = getattr(record, field_name)
                fill_value = self.fill_rules[field_name]
                
                # 设置填充值
                setattr(record, field_name, fill_value)
                
                # 记录填充信息
                fill_info = FieldFillInfo(
                    field_name=field_name,
                    original_value=original_value,
                    filled_value=fill_value,
                    status=FieldStatusEnum.FILLED,
                    fill_reason=DDBUtils.get_fill_reason(field_name, original_value)
                )
                fill_details.append(fill_info)
                
                logger.debug(f"填充字段 {field_name}: {original_value} -> {fill_value}")
        
        return fill_details
    
    def _fill_group2_fields(self, record: DDBRecord, empty_fields: List[str]) -> List[FieldFillInfo]:
        """
        填充第二组字段 (BDR12, BDR13, BDR14, BDR15, brd17)
        
        Args:
            record: 记录对象
            empty_fields: 空字段列表
            
        Returns:
            填充信息列表
        """
        fill_details = []
        
        for field_name in empty_fields:
            if field_name in self.group2_fields:
                original_value = getattr(record, field_name)
                fill_value = self.fill_rules[field_name]
                
                # 设置填充值
                setattr(record, field_name, fill_value)
                
                # 记录填充信息
                fill_info = FieldFillInfo(
                    field_name=field_name,
                    original_value=original_value,
                    filled_value=fill_value,
                    status=FieldStatusEnum.FILLED,
                    fill_reason=DDBUtils.get_fill_reason(field_name, original_value)
                )
                fill_details.append(fill_info)
                
                logger.debug(f"填充字段 {field_name}: {original_value} -> {fill_value}")
        
        return fill_details
    
    @handle_ddb_errors
    def batch_fill_records(self, records: List[DDBRecord]) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
        """
        批量填充记录
        
        Args:
            records: 记录列表
            
        Returns:
            (填充后的记录列表, 所有填充信息列表)
        """
        filled_records = []
        all_fill_details = []
        
        for record in records:
            try:
                filled_record, fill_details = self.fill_record_fields(record)
                filled_records.append(filled_record)
                all_fill_details.extend(fill_details)
                
            except Exception as e:
                logger.error(f"批量填充中处理记录 {record.id} 失败: {e}")
                # 对于批量处理，我们继续处理其他记录
                filled_records.append(record)  # 保留原始记录
        
        logger.info(f"批量填充完成: 处理 {len(records)} 条记录，填充 {len(all_fill_details)} 个字段")
        
        return filled_records, all_fill_details
    
    @handle_ddb_errors
    def get_fill_statistics(self, fill_details: List[FieldFillInfo]) -> Dict[str, Any]:
        """
        获取填充统计信息
        
        Args:
            fill_details: 填充详情列表
            
        Returns:
            统计信息
        """
        stats = {
            "total_fills": len(fill_details),
            "group1_fills": 0,
            "group2_fills": 0,
            "field_breakdown": {},
            "fill_reasons": {}
        }
        
        for fill_info in fill_details:
            field_name = fill_info.field_name
            
            # 统计字段分组
            if field_name in self.group1_fields:
                stats["group1_fills"] += 1
            elif field_name in self.group2_fields:
                stats["group2_fills"] += 1
            
            # 统计字段分布
            if field_name not in stats["field_breakdown"]:
                stats["field_breakdown"][field_name] = 0
            stats["field_breakdown"][field_name] += 1
            
            # 统计填充原因
            reason = fill_info.fill_reason
            if reason not in stats["fill_reasons"]:
                stats["fill_reasons"][reason] = 0
            stats["fill_reasons"][reason] += 1
        
        return stats


# 便捷函数
def create_field_filler() -> FieldFiller:
    """创建字段填充引擎实例"""
    return FieldFiller()


def fill_single_record(record: DDBRecord) -> Tuple[DDBRecord, List[FieldFillInfo]]:
    """填充单个记录的便捷函数"""
    filler = create_field_filler()
    return filler.fill_record_fields(record)


def fill_multiple_records(records: List[DDBRecord]) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
    """填充多个记录的便捷函数"""
    filler = create_field_filler()
    return filler.batch_fill_records(records)
