"""
Knowledge V2 API - 表管理路由

基于批量操作的高性能表管理API
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends

from ..models.request_models import (
    BatchCreateRequest,
    BatchUpdateRequest,
    BatchDeleteRequest,
    UnifiedGetRequest,
    UnifiedListRequest
)
from ..models.response_models import (
    BatchOperationResponse,
    UnifiedResponse,
    UnifiedListResponse,
    ErrorResponse
)
from ..dependencies import (
    get_metadata_crud,
    validate_batch_config,
    create_batch_stats,
    log_batch_operation,
    handle_api_errors,
    PerformanceMonitor
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/tables", tags=["表管理V2"])


@router.post("/batch", response_model=BatchOperationResponse, summary="批量创建表")
@handle_api_errors
async def batch_create_tables(
    request: BatchCreateRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    批量创建表
    
    - **tables**: 表数据列表
    - **config**: 批量操作配置（可选）
    
    支持高并发批量创建，自动处理向量数据同步
    """
    with PerformanceMonitor("批量创建表") as monitor:
        # 验证配置
        config = validate_batch_config(request.config)
        
        # 转换数据格式
        tables_data = [table.dict() for table in request.tables]
        
        try:
            # 执行批量创建
            table_ids, vector_results = await metadata_crud.batch_create_source_tables(
                tables_data,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            # 创建统计信息
            stats = create_batch_stats(
                total_requested=len(tables_data),
                total_successful=len(table_ids),
                execution_time=monitor.execution_time,
                batch_count=len(tables_data) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            # 记录日志
            log_batch_operation(
                operation="创建",
                entity_type="表",
                count=len(table_ids),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return BatchOperationResponse(
                success=True,
                message=f"成功创建 {len(table_ids)} 个表",
                data=table_ids,
                stats=stats,
                created_ids=table_ids,
                affected_rows=len(table_ids)
            )
            
        except Exception as e:
            logger.error(f"批量创建表失败: {e}")
            stats = create_batch_stats(
                total_requested=len(tables_data),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量创建表失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.put("/batch", response_model=BatchOperationResponse, summary="批量更新表")
@handle_api_errors
async def batch_update_tables(
    request: BatchUpdateRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    批量更新表
    
    - **updates**: 更新数据列表
    - **conditions**: 更新条件列表
    - **config**: 批量操作配置（可选）
    """
    with PerformanceMonitor("批量更新表") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量更新
            success = await metadata_crud.batch_update_source_tables(
                updates=request.updates,
                conditions=request.conditions,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            stats = create_batch_stats(
                total_requested=len(request.updates),
                total_successful=len(request.updates) if success else 0,
                execution_time=monitor.execution_time,
                batch_count=len(request.updates) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="更新",
                entity_type="表",
                count=len(request.updates),
                execution_time=monitor.execution_time,
                success=success
            )
            
            return BatchOperationResponse(
                success=success,
                message=f"批量更新表{'成功' if success else '失败'}",
                stats=stats,
                affected_rows=len(request.updates) if success else 0
            )
            
        except Exception as e:
            logger.error(f"批量更新表失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.updates),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量更新表失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.delete("/batch", response_model=BatchOperationResponse, summary="批量删除表")
@handle_api_errors
async def batch_delete_tables(
    request: BatchDeleteRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    批量删除表
    
    - **conditions**: 删除条件列表
    - **config**: 批量操作配置（可选）
    
    注意：删除表会级联删除相关的字段数据
    """
    with PerformanceMonitor("批量删除表") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量删除
            success = await metadata_crud.batch_delete_source_tables(
                conditions=request.conditions,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=len(request.conditions) if success else 0,
                execution_time=monitor.execution_time,
                batch_count=len(request.conditions) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="删除",
                entity_type="表",
                count=len(request.conditions),
                execution_time=monitor.execution_time,
                success=success
            )
            
            return BatchOperationResponse(
                success=success,
                message=f"批量删除表{'成功' if success else '失败'}",
                stats=stats,
                affected_rows=len(request.conditions) if success else 0
            )
            
        except Exception as e:
            logger.error(f"批量删除表失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量删除表失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.post("/batch/query", response_model=UnifiedListResponse, summary="批量查询表")
@handle_api_errors
async def batch_query_tables(
    request: UnifiedListRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    批量查询表
    
    - **queries**: 查询条件列表
    - **config**: 批量操作配置（可选）
    """
    with PerformanceMonitor("批量查询表") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量查询
            results = await metadata_crud.batch_query_source_tables(
                conditions_list=request.queries,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            # 计算总记录数
            total_records = sum(len(result) for result in results)
            
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=len(results),
                execution_time=monitor.execution_time,
                batch_count=len(request.queries) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="查询",
                entity_type="表",
                count=len(request.queries),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return UnifiedListResponse(
                success=True,
                message=f"成功查询 {len(results)} 批表，共 {total_records} 条记录",
                data=results,
                pagination={"total_records": total_records, "page": 1, "page_size": len(results)}
            )
            
        except Exception as e:
            logger.error(f"批量查询表失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return UnifiedListResponse(
                success=False,
                message=f"批量查询表失败: {str(e)}",
                data=[],
                pagination={"total_records": 0, "page": 1, "page_size": 0}
            )
