from openpyxl import load_workbook
from openpyxl.utils import get_column_letter, column_index_from_string
import pandas as pd
import logging
from copy import copy
from openpyxl.styles import Font, PatternFill, Border, Alignment
from openpyxl.comments import Comment


def has_border(cell, ws, merged_ranges):
    """检查单元格是否具有右边框或下边框，对于合并单元格检查所有边界单元格"""
    if cell.coordinate in merged_ranges:
        for merged_range in merged_ranges:
            if cell.coordinate in merged_range:
                # 检查合并单元格的所有边界单元格
                for row in range(merged_range.min_row, merged_range.max_row + 1):
                    for col in range(merged_range.min_col, merged_range.max_col + 1):
                        border_cell = ws.cell(row=row, column=col)
                        border = border_cell.border
                        # 右边框或下边框任一存在即返回 True
                        has_right = border.right and border.right.style is not None
                        has_bottom = border.bottom and border.bottom.style is not None
                        if has_right or has_bottom:
                            return True
                return False
    # 非合并单元格检查右边框或下边框
    border = cell.border
    has_right = border.right and border.right.style is not None
    has_bottom = border.bottom and border.bottom.style is not None
    return has_right or has_bottom  # 修改为或逻辑，符合“填充边框”需求


def get_cell_styles(cell):
    """提取单元格样式，包括字体、填充、边框和对齐方式"""
    return {
        'font': copy(cell.font) if cell.font else Font(),
        'fill': copy(cell.fill) if cell.fill else PatternFill(),
        'border': copy(cell.border) if cell.border else Border(),
        'alignment': copy(cell.alignment) if cell.alignment else Alignment()
    }


def get_merged_cell_value(ws, cell, merged_ranges):
    """获取单元格的值，处理合并单元格，返回左上角单元格的值"""
    for merged_range in merged_ranges:
        if cell.coordinate in merged_range:
            top_left_cell = ws[merged_range.min_row][merged_range.min_col - 1]
            return top_left_cell.value
    return cell.value


def is_continuous(prev_last_value, current_first_value):
    """检查两个值是否连续（差值为2），仅当两者均为数字时有效"""
    try:
        prev_val = float(prev_last_value)
        curr_val = float(current_first_value)
        # print(prev_val)
        # print(curr_val)
        return abs(curr_val - prev_val) == 2
    except (TypeError, ValueError):
        return False


def get_sheetname_nums(input_path):
    wb = load_workbook(input_path)
    return wb.sheetnames


def detect_tables_by_border(input_path, max_col='BZ'):
    """基于边框检测Excel中的表格，表格外行包含A、B、C时作为表格首行，处理合并单元格，
    检查表格连续性，要求至少3行2列，保留连续表格之间的中间行和新表格首行，跳过首列为‘附注项目：’等值的行"""
    wb = load_workbook(input_path)
    tables_by_sheet = {}

    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        tables = []
        current_table = []
        styles = []
        intermediate_rows = []
        intermediate_styles = []
        in_table = False
        after_table = False
        merged_ranges = ws.merged_cells.ranges
        prev_last_value = None

        # 计算最大列索引
        max_col_idx = min(ws.max_column, column_index_from_string(max_col))

        for row_idx, row in enumerate(ws.iter_rows(min_row=1, max_col=max_col_idx), 1):
            row_has_border = any(has_border(cell, ws, merged_ranges) for cell in row)
            current_row_values = [get_merged_cell_value(ws, cell, merged_ranges) for cell in row]
            current_row_styles = [(row_idx, col_idx + 1, get_cell_styles(cell))
                                  for col_idx, cell in enumerate(row)]

            # 跳过特定首列值的行
            skip_values = ["附注项目：", "附注项目", "附注：","0.币种（请选择）："]
            if current_row_values and current_row_values[0] in skip_values:
                logging.info(f"工作表: {sheet_name}, 行 {row_idx} 首列为‘{current_row_values[0]}’，跳过")
                continue

            # 检查行中是否包含 A、B、C
            # print(current_row_values)
            has_abc = any(str(val).strip() in ['A'] for val in current_row_values if val)
            has_abc = has_abc and any(str(val).strip() in ['项    目'] for val in current_row_values if val)
            # print(f"行 {row_idx} 值: {current_row_values}, has_abc: {has_abc}, in_table: {in_table}")
            # print(f"行 {row_idx} 值: {current_row_values}, has_abc: {has_abc}, in_table: {in_table}")

            # 先检查 has_abc，确保包含 'A' 的行作为表格首行
            if not in_table and has_abc:
                in_table = True
                intermediate_rows = []
                intermediate_styles = []
                current_table.append((row_idx, current_row_values))
                styles.append(current_row_styles)
                after_table = True
                logging.info(f"工作表: {sheet_name}, 行 {row_idx} 包含A，开始新表格")
                # print(f"这是row_idx: {row_idx}")  # 调试打印
            # 如果已经在表格中，或表格外但有边框，继续收集
            elif (in_table and row_has_border) or (not in_table and row_has_border):
                # print('哈哈哈哈')
                if not in_table:
                    in_table = True
                    intermediate_rows = []
                    intermediate_styles = []
                    logging.info(f"工作表: {sheet_name}, 行 {row_idx} 有边框，开始新表格")
                    # print(f"这是row_idx: {row_idx}")  # 调试打印
                current_table.append((row_idx, current_row_values))
                styles.append(current_row_styles)
                after_table = True
            # 表格结束或中间行
            elif in_table and not row_has_border:
                # 表格结束，处理当前表格
                headers = [f"列_{i}" for i in range(len(current_table[0][1]))]
                unique_headers = []
                seen = {}
                for h in headers:
                    if h in seen:
                        seen[h] += 1
                        unique_headers.append(f"{h}_{seen[h]}")
                    else:
                        seen[h] = 0
                        unique_headers.append(h)

                table_values = [row[1] for row in current_table]
                df = pd.DataFrame(table_values, columns=unique_headers)
                current_first_value = current_table[0][1][0] if current_table else None

                if (prev_last_value is not None and current_first_value is not None and
                        is_continuous(prev_last_value, current_first_value) and tables):
                    # 连续表格，合并到上一个表格
                    last_table, last_styles, last_row_indices = tables[-1]
                    last_df_values = last_table.values.tolist()
                    intermediate_values = [row[1][:len(unique_headers)] for row in intermediate_rows]
                    current_values = [row[1][:len(unique_headers)] for row in current_table]
                    intermediate_styles = [s[:len(unique_headers)] for s in intermediate_styles]
                    styles = [s[:len(unique_headers)] for s in styles]
                    combined_values = last_df_values + intermediate_values + current_values
                    last_table = pd.DataFrame(combined_values, columns=unique_headers)
                    last_styles.extend(intermediate_styles + styles)
                    last_row_indices.extend([row[0] for row in intermediate_rows] +
                                            [row[0] for row in current_table])
                    tables[-1] = (last_table, last_styles, last_row_indices)
                    logging.info(
                        f"工作表: {sheet_name}, 表格连续，合并到上一个表格，包含中间行（{len(intermediate_rows)}行）和当前表格（{len(current_table)}行）")
                    # print(f"合并表格，row_indices: {last_row_indices}")  # 调试打印
                else:
                    # 非连续，新建表格
                    if df.shape[0] >= 3 and df.shape[1] >= 2:
                        non_empty_cols = [col for col in df.columns if df[col].notna().any()]
                        if non_empty_cols:
                            df = df[non_empty_cols]
                            col_indices = [unique_headers.index(col) for col in non_empty_cols]
                            styles_subset = [[s for i, s in enumerate(row) if i in col_indices] for row in styles]
                            row_indices = [row[0] for row in current_table]
                            tables.append((df, styles_subset, row_indices))
                            logging.info(f"工作表: {sheet_name}, 新表格添加，尺寸: {df.shape}")
                            # print(f"添加表格，row_indices: {row_indices}")  # 调试打印
                        else:
                            logging.warning(f"工作表: {sheet_name}, 空DataFrame，跳过")
                    else:
                        logging.info(f"工作表: {sheet_name}, 表格尺寸小于3行2列，跳过（尺寸: {df.shape}）")

                prev_last_value = current_table[-1][1][0] if current_table else None
                current_table = []
                styles = []
                in_table = False
                intermediate_rows.append((row_idx, current_row_values))
                intermediate_styles.append(current_row_styles)
                logging.info(f"工作表: {sheet_name}, 行 {row_idx} 无边框，记录为中间行")
                # print(f"添加中间行: {row_idx}, 值: {current_row_values}")  # 调试打印

            # 表格外，记录中间行
            elif after_table:
                intermediate_rows.append((row_idx, current_row_values))
                intermediate_styles.append(current_row_styles)
                # print(f"添加中间行: {row_idx}, 值: {current_row_values}")  # 调试打印

            # 处理最后一行
            if row_idx == ws.max_row and in_table and current_table:
                headers = [f"列_{i}" for i in range(len(current_table[0][1]))]
                unique_headers = []
                seen = {}
                for h in headers:
                    if h in seen:
                        seen[h] += 1
                        unique_headers.append(f"{h}_{seen[h]}")
                    else:
                        seen[h] = 0
                        unique_headers.append(h)

                table_values = [row[1] for row in current_table]
                df = pd.DataFrame(table_values, columns=unique_headers)
                current_first_value = current_table[0][1][0] if current_table else None

                if (prev_last_value is not None and current_first_value is not None and
                        is_continuous(prev_last_value, current_first_value) and tables):
                    last_table, last_styles, last_row_indices = tables[-1]
                    last_df_values = last_table.values.tolist()
                    intermediate_values = [row[1][:len(unique_headers)] for row in intermediate_rows]
                    current_values = [row[1][:len(unique_headers)] for row in current_table]
                    intermediate_styles = [s[:len(unique_headers)] for s in intermediate_styles]
                    styles = [s[:len(unique_headers)] for s in styles]
                    combined_values = last_df_values + intermediate_values + current_values
                    last_table = pd.DataFrame(combined_values, columns=unique_headers)
                    last_styles.extend(intermediate_styles + styles)
                    last_row_indices.extend([row[0] for row in intermediate_rows] +
                                            [row[0] for row in current_table])
                    tables[-1] = (last_table, last_styles, last_row_indices)
                    logging.info(
                        f"工作表: {sheet_name}, 最后一个表格连续，合并，包含中间行（{len(intermediate_rows)}行）和当前表格（{len(current_table)}行）")
                    # print(f"合并最后一个表格，row_indices: {last_row_indices}")  # 调试打印
                else:
                    if df.shape[0] >= 3 and df.shape[1] >= 2:
                        non_empty_cols = [col for col in df.columns if df[col].notna().any()]
                        if non_empty_cols:
                            df = df[non_empty_cols]
                            col_indices = [unique_headers.index(col) for col in non_empty_cols]
                            styles_subset = [[s for i, s in enumerate(row) if i in col_indices] for row in styles]
                            row_indices = [row[0] for row in current_table]
                            tables.append((df, styles_subset, row_indices))
                            logging.info(f"工作表: {sheet_name}, 最后一个表格添加，尺寸: {df.shape}")
                            # print(f"添加最后一个表格，row_indices: {row_indices}")  # 调试打印
                        else:
                            logging.warning(f"工作表: {sheet_name}, 最后一个空DataFrame，跳过")
                    else:
                        logging.info(f"工作表: {sheet_name}, 最后一个表格尺寸小于3行2列，跳过（尺寸: {df.shape}）")

        # 处理剩余中间行
        if intermediate_rows and tables:
            last_table, last_styles, last_row_indices = tables[-1]
            if prev_last_value is not None and len(intermediate_rows) > 0:
                first_intermediate_value = intermediate_rows[0][1][0]
                if is_continuous(prev_last_value, first_intermediate_value):
                    last_df_values = last_table.values.tolist()
                    intermediate_values = [row[1][:len(last_table.columns)] for row in intermediate_rows]
                    intermediate_styles = [s[:len(last_table.columns)] for s in intermediate_styles]
                    if len(intermediate_values[0]) == last_table.shape[1]:
                        combined_values = last_df_values + intermediate_values
                        last_table = pd.DataFrame(combined_values, columns=last_table.columns)
                        last_styles.extend(intermediate_styles)
                        last_row_indices.extend([row[0] for row in intermediate_rows])
                        tables[-1] = (last_table, last_styles, last_row_indices)
                        logging.info(f"工作表: {sheet_name}, 附加中间行（{len(intermediate_rows)}行）到最后一个表格")
                        print(f"附加中间行到最后一个表格，row_indices: {last_row_indices}")  # 调试打印
                    else:
                        logging.warning(f"工作表: {sheet_name}, 中间行列数不匹配，无法附加")
                else:
                    logging.info(f"工作表: {sheet_name}, 中间行与最后一个表格不连续，无法附加")

        if tables:
            tables_by_sheet[sheet_name] = tables
        else:
            logging.info(f"工作表: {sheet_name} 没有符合要求的表格，跳过")

    return tables_by_sheet


def save_tables_to_excel(tables_by_sheet, output_path, original_wb_path):
    """将表格保存到Excel，保留工作表名称并为多个表格命名，使用原始坐标获取样式，并存储原始表格位置"""
    if not tables_by_sheet:
        logging.error("没有找到任何符合要求的表格，无法保存Excel文件")
        return None

    original_wb = load_workbook(original_wb_path)
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        for sheet_name, tables in tables_by_sheet.items():
            for i, (table, styles, row_indices) in enumerate(tables):
                new_sheet_name = sheet_name if i == 0 else f"{sheet_name}_Table<momomo>_{i + 1}"

                # 保存表格到 Excel，不添加 _source_coords 列
                table.to_excel(writer, sheet_name=new_sheet_name, index=False, header=True)

                # 应用样式并存储原始表格位置
                ws = writer.book[new_sheet_name]
                original_ws = original_wb[sheet_name]
                for row_idx, (row_styles, orig_row_idx) in enumerate(zip(styles, row_indices), start=2):
                    for col_idx, style_tuple in enumerate(row_styles, start=1):
                        if row_idx <= ws.max_row and col_idx <= ws.max_column:
                            cell = ws.cell(row=row_idx, column=col_idx)
                            # 从原始工作表获取样式
                            orig_col_idx = style_tuple[1]
                            orig_cell = original_ws.cell(row=orig_row_idx, column=orig_col_idx)
                            cell.font = copy(orig_cell.font) if orig_cell.font else Font()
                            cell.fill = copy(orig_cell.fill) if orig_cell.fill else PatternFill()
                            cell.border = copy(orig_cell.border) if orig_cell.border else Border()
                            cell.alignment = copy(orig_cell.alignment) if orig_cell.alignment else Alignment()

                            # 存储原始表格的标准化位置到单元格的 comment 属性
                            # 标准化：保持原始索引，无需偏移（直接使用 orig_row_idx 和 orig_col_idx）
                            comment_text = f"Original Position: ({orig_row_idx}, {orig_col_idx})"
                            cell.comment = Comment(comment_text, "System")

    logging.info(f"Excel文件已成功保存到: {output_path}")
    return output_path


if __name__ == "__main__":
    input_file = r"D:\pythonProject\works\test_dd_file\file\G0107\G01_VII（231版）.xlsx"
    output_file = r"D:\pythonProject\works\test_dd_file\file\G0107\output_border.xlsx"
    tables_by_sheet = detect_tables_by_border(input_file, max_col='BZ')
    # print(tables_by_sheet)
    if tables_by_sheet:
        save_tables_to_excel(tables_by_sheet, output_file, input_file)
    else:
        logging.error("没有检测到符合要求的表格，程序退出")
