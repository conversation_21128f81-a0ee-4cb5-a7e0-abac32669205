"""
DD部门分配API路由模块

包含核心接口的路由定义
"""

from .duty_distribution import router as duty_distribution_router
from .data_backfill import router as data_backfill_router
from .dd_b_enhanced import router as dd_b_enhanced_router
from .dd_b_recommend import router as dd_b_recommend_router
from .dd_b_update import router as dd_b_update_router
from .dd_c_recommend import router as dd_c_recommend_router
from .dd_c_process import router as dd_c_process_router

__all__ = [
    "duty_distribution_router",
    "data_backfill_router",
    "dd_b_enhanced_router",
    "dd_b_recommend_router",
    "dd_b_update_router",
    "dd_c_recommend_router",
    "dd_c_process_router"
]
