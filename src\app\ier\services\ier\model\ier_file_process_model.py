#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：ier_file_process_model.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/22 10:13 
@Desc    ：外规内化文件处理接口模型
"""
from dataclasses import dataclass, asdict
from datetime import date
from enum import Enum
from pydantic import BaseModel, constr
from typing import List, Optional, Dict

# todo 完成api接口的输入、输出模型开发
class UploadLawFileRequestModel(BaseModel):
    """上传法规文件请求模型"""
    file_id: int
    file_path: str

class UploadLawRequestModel(BaseModel):
    """上传法规请求模型"""
    request_id: str
    file_list: List[UploadLawFileRequestModel]
    def to_dict(self, **kwargs) -> dict:
        """
        Args:
            **kwargs: 传递给pydantic的dict()方法的参数
                - exclude: 排除的字段
                - include: 包含的字段
                - by_alias: 使用别名
                - exclude_unset: 排除未设置的值
                - exclude_defaults: 排除默认值
                - exclude_none: 排除None值
        """
        return self.model_dump(**kwargs)

class OcrStatusEnum(str, Enum):
    """法规文件OCR解析状态"""
    PROCESSING = "processing"
    FAILURE = "failure"
    SUCCESS = "success"

@dataclass
class UploadLawFileResult:
    """上传法规文件解析结果模型"""
    request_id: str # 请求id
    file_id: str # java端返回过来的文件id
    file_path: str # java端返回过来的文件路径，需要当前服务可以访问到
    ocr_result_path: str # ocr结果路径
    ocr_status: OcrStatusEnum # 法规文件ocr解析状态

    @classmethod
    def from_dict(cls, data: dict) -> "UploadLawFileResult":
        """
        类方法：从字典创建实例
        """
        # 将字典中的ocr_status转换为枚举类型
        ocr_status = OcrStatusEnum(data.get("ocr_status", OcrStatusEnum.PROCESSING))
        return cls(
            request_id=data["request_id"],
            file_id=data["file_id"],
            file_path=data["file_path"],
            ocr_result_path=data["ocr_result_path"],
            ocr_status=ocr_status
        )

    def to_dict(self) -> dict:
        """
        实例方法：将当前实例转换为字典
        """
        result = asdict(self)
        # 将枚举类型的值转换为字符串
        result["ocr_status"] = self.ocr_status.value
        return result

class QueryLawFileOcrStatusRequestModel(BaseModel):
    request_id: str

@dataclass
class QueryLawFileOcrStatusResult:
    percentage: float
    message: str
    ocr_status: OcrStatusEnum  # 法规文件ocr解析状态

    @classmethod
    def from_dict(cls, data: dict) -> "QueryLawFileOcrStatusResult":
        """
        类方法：从字典创建实例
        """
        ocr_status = OcrStatusEnum(data.get("ocr_status", OcrStatusEnum.PROCESSING))
        return cls(
            percentage=data['percentage'],
            message=data["message"],
            ocr_status=ocr_status
        )

    def to_dict(self) -> dict:
        """
        实例方法：将当前实例转换为字典
        """
        result = asdict(self)
        # 将枚举类型的值转换为字符串
        result["ocr_status"] = self.ocr_status.value
        return result

@dataclass
class IsMainLawFile:
    file_id: str
    main_label: bool

    @classmethod
    def from_dict(cls, data: dict) -> "IsMainLawFile":
        """
        类方法：从字典创建实例
        """
        return cls(
            file_id=data['file_id'],
            main_label=data["main_label"]
        )

    def to_dict(self) -> dict:
        """
        实例方法：将当前实例转换为字典
        """
        result = asdict(self)
        return result

class IsMainLawFileRequestModel(BaseModel):
    law_file_list: List[UploadLawFileRequestModel]

class LawParseRequestModel(BaseModel):
    law_file_id: int
    file_type: str
    file_name:str

# 文件信息子模型
class FileInfo(BaseModel):
    file_law_id: int  # 文件关联的法规ID（整数类型）
    is_main: constr(pattern=r'^[01]$')  # 限制为0或1的字符串（使用正则验证）
    file_path: constr(min_length=1)  # 文件路径不能为空字符串
    file_id: int

# 主法规模型
class LawModel(BaseModel):
    law_id: int  # 法规ID（整数类型）
    law_type: Optional[str] = None  # 法规类型（可选字符串）
    law_name: constr(min_length=1)  # 法规名（不能为空字符串）
    law_number: Optional[str] = None  # 法规号（可选字符串）
    law_release_date: date # 发布时间
    law_summary: str = None  # 概要
    file_list: List[FileInfo]  # 文件列表（嵌套FileInfo模型列表）

class LawRelationModel(BaseModel):
    law_id: int
    law_alias: str
    law_authority: str
    law_year: str
    law_number: str
    is_repealed: bool
    is_related: bool
    update_law_id: int
    update_law_name: str

# 匹配法规模型
class LawMatchModel(BaseModel):
    law_id: int  # 法规ID（整数类型）
    law_type: Optional[str] = None  # 法规类型（可选字符串）
    law_name: constr(min_length=1)  # 法规名（不能为空字符串）

# 法规知识图谱模型
class LawKnowledgeDomainModel(BaseModel):
    law_id: int  # 法规ID（整数类型）
    law_name: constr(min_length=1)  # 法规名（不能为空字符串）



if __name__ == '__main__':
    request = UploadLawRequestModel(
        request_id="req_12345",
        file_list=[
            UploadLawFileRequestModel(file_id=1, file_path="/path/to/file1.pdf"),
            UploadLawFileRequestModel(file_id=2, file_path="/path/to/file2.pdf")
        ]
    )

    default_dict = request.to_dict()
    print("默认转换:", default_dict)

    # 从字典创建实例
    data_dict = {
        "request_id": "req_20240723001",
        "file_id": "file_12345",
        "file_path": "/upload/files/law.pdf",
        "ocr_result_path": "/ocr/result.json",
        "ocr_status": "success"
    }

    # 使用类方法创建实例
    instance = UploadLawFileResult.from_dict(data_dict)

    # 使用实例方法转换为字典
    print(instance.to_dict())

    data = {
        "percentage": 75.5,
        "message": "OCR处理中"
    }

    result = QueryLawFileOcrStatusResult.from_dict(data)

    result = QueryLawFileOcrStatusResult(
        percentage=90.0,
        message="OCR完成"
    )

    result_dict = result.to_dict()
    print(result_dict)