"""
Knowledge V2 API 请求模型

基于现有数据库表结构的统一API请求模型，确保与数据模型完全一致
支持统一的接口设计模式：GET(id/ids), POST(create), PUT(update), DELETE(delete)
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


# ==================== 枚举类型 ====================

class DataLayerEnum(str, Enum):
    """数据层级枚举"""
    ADM = "adm"  # 管理层
    BDM = "bdm"  # 基础数据层
    ODS = "ods"  # 操作数据层
    ADS = "ads"  # 应用数据层


class ColumnTypeEnum(str, Enum):
    """字段类型枚举"""
    SOURCE = "source"  # 源字段
    INDEX = "index"   # 指标字段


class IndexTypeEnum(str, Enum):
    """指标类型枚举"""
    ATOM = "atom"      # 原子指标
    COMPUTE = "compute"  # 计算指标


class RelationTypeEnum(str, Enum):
    """关联类型枚举"""
    FK = "FK"    # 外键
    REF = "REF"  # 参考关联


class CodeSetTypeEnum(str, Enum):
    """码值集类型枚举"""
    ENUM = "enum"
    LOOKUP = "lookup"
    REFERENCE = "reference"


# ==================== 基础模型 ====================

class BatchOperationConfig(BaseModel):
    """批量操作配置"""
    batch_size: int = Field(500, description="批次大小", ge=1, le=1000)
    max_concurrency: int = Field(5, description="最大并发数", ge=1, le=10)
    timeout_per_batch: float = Field(300.0, description="每批次超时时间(秒)", ge=10.0, le=600.0)


# ==================== 源数据库相关模型 ====================

class SourceDatabaseCreate(BaseModel):
    """源数据库创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_name: str = Field(..., description="数据库名称", max_length=100, example="customer_db")
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100, example="客户数据库")
    data_layer: DataLayerEnum = Field(DataLayerEnum.ODS, description="数据层级")
    db_desc: Optional[str] = Field(None, description="数据库描述", example="存储客户相关信息的数据库")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "db_name": "customer_db",
                "db_name_cn": "客户数据库",
                "data_layer": "ods",
                "db_desc": "存储客户相关信息的数据库",
                "is_active": True
            }
        }


class SourceDatabaseUpdate(BaseModel):
    """源数据库更新模型"""
    db_id: int = Field(..., description="数据库ID", gt=0)
    db_name: Optional[str] = Field(None, description="数据库名称", max_length=100)
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100)
    data_layer: Optional[DataLayerEnum] = Field(None, description="数据层级")
    db_desc: Optional[str] = Field(None, description="数据库描述")
    is_active: Optional[bool] = Field(None, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "db_id": 1,
                "db_name_cn": "更新后的客户数据库",
                "db_desc": "更新后的描述",
                "is_active": True
            }
        }


# ==================== 指标数据库相关模型 ====================

class IndexDatabaseCreate(BaseModel):
    """指标数据库创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_name: str = Field(..., description="数据库名称", max_length=100, example="index_db")
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100, example="指标数据库")
    data_layer: DataLayerEnum = Field(DataLayerEnum.ADS, description="数据层级")
    db_desc: Optional[str] = Field(None, description="数据库描述", example="存储指标相关信息的数据库")
    is_active: bool = Field(True, description="是否激活")


class IndexDatabaseUpdate(BaseModel):
    """指标数据库更新模型"""
    db_id: int = Field(..., description="数据库ID", gt=0)
    db_name: Optional[str] = Field(None, description="数据库名称", max_length=100)
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100)
    data_layer: Optional[DataLayerEnum] = Field(None, description="数据层级")
    db_desc: Optional[str] = Field(None, description="数据库描述")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ==================== 源表相关模型 ====================

class SourceTableCreate(BaseModel):
    """源表创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_id: int = Field(..., description="数据库ID", gt=0, example=1)
    table_name: str = Field(..., description="表名称", max_length=100, example="customer_info")
    table_name_cn: Optional[str] = Field(None, description="表中文名称", max_length=100, example="客户信息表")
    table_desc: Optional[str] = Field(None, description="表描述", example="存储客户基本信息")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "db_id": 1,
                "table_name": "customer_info",
                "table_name_cn": "客户信息表",
                "table_desc": "存储客户基本信息",
                "is_active": True
            }
        }


class SourceTableUpdate(BaseModel):
    """源表更新模型"""
    table_id: int = Field(..., description="表ID", gt=0)
    table_name: Optional[str] = Field(None, description="表名称", max_length=100)
    table_name_cn: Optional[str] = Field(None, description="表中文名称", max_length=100)
    table_desc: Optional[str] = Field(None, description="表描述")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ==================== 指标表相关模型 ====================

class IndexTableCreate(BaseModel):
    """指标表创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_id: int = Field(..., description="数据库ID", gt=0, example=1)
    table_name: str = Field(..., description="表名称", max_length=100, example="customer_metrics")
    table_name_cn: Optional[str] = Field(None, description="表中文名称", max_length=100, example="客户指标表")
    table_desc: Optional[str] = Field(None, description="表描述", example="存储客户相关指标")
    is_active: bool = Field(True, description="是否激活")


class IndexTableUpdate(BaseModel):
    """指标表更新模型"""
    table_id: int = Field(..., description="表ID", gt=0)
    table_name: Optional[str] = Field(None, description="表名称", max_length=100)
    table_name_cn: Optional[str] = Field(None, description="表中文名称", max_length=100)
    table_desc: Optional[str] = Field(None, description="表描述")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ==================== 源字段相关模型 ====================

class SourceColumnCreate(BaseModel):
    """源字段创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    table_id: int = Field(..., description="表ID", gt=0, example=1)
    column_name: str = Field(..., description="字段名称", max_length=100, example="customer_id")
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", max_length=100, example="客户ID")
    column_desc: Optional[str] = Field(None, description="字段描述", example="客户唯一标识")
    data_type: Optional[str] = Field(None, description="数据类型", max_length=50, example="STRING")
    data_example: Optional[str] = Field(None, description="数据样例", example="CUST001")
    is_vectorized: bool = Field(False, description="是否已向量化")
    is_primary_key: bool = Field(False, description="是否主键")
    is_sensitive: bool = Field(False, description="是否敏感数据")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "table_id": 1,
                "column_name": "customer_id",
                "column_name_cn": "客户ID",
                "column_desc": "客户唯一标识",
                "data_type": "STRING",
                "data_example": "CUST001",
                "is_vectorized": False,
                "is_primary_key": True,
                "is_sensitive": False
            }
        }


class SourceColumnUpdate(BaseModel):
    """源字段更新模型"""
    column_id: int = Field(..., description="字段ID", gt=0)
    column_name: Optional[str] = Field(None, description="字段名称", max_length=100)
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", max_length=100)
    column_desc: Optional[str] = Field(None, description="字段描述")
    data_type: Optional[str] = Field(None, description="数据类型", max_length=50)
    data_example: Optional[str] = Field(None, description="数据样例")
    is_vectorized: Optional[bool] = Field(None, description="是否已向量化")
    is_primary_key: Optional[bool] = Field(None, description="是否主键")
    is_sensitive: Optional[bool] = Field(None, description="是否敏感数据")


# ==================== 指标字段相关模型 ====================

class IndexColumnCreate(BaseModel):
    """指标字段创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    table_id: int = Field(..., description="表ID", gt=0, example=1)
    column_name: str = Field(..., description="字段名称", max_length=100, example="customer_count")
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", max_length=100, example="客户数量")
    index_type: Optional[IndexTypeEnum] = Field(None, description="指标类型")
    column_desc: Optional[str] = Field(None, description="字段描述", example="统计客户总数")
    data_type: Optional[str] = Field(None, description="数据类型", max_length=50, example="NUMBER")
    data_example: Optional[str] = Field(None, description="数据样例", example="1000")
    comment: Optional[str] = Field(None, description="备注说明")
    is_vectorized: bool = Field(False, description="是否已向量化")
    is_primary_key: bool = Field(False, description="是否主键")
    is_sensitive: bool = Field(False, description="是否敏感数据")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "table_id": 1,
                "column_name": "customer_count",
                "column_name_cn": "客户数量",
                "index_type": "atom",
                "column_desc": "统计客户总数",
                "data_type": "NUMBER",
                "data_example": "1000",
                "comment": "原子指标",
                "is_vectorized": False,
                "is_primary_key": False,
                "is_sensitive": False
            }
        }


class IndexColumnUpdate(BaseModel):
    """指标字段更新模型"""
    column_id: int = Field(..., description="字段ID", gt=0)
    column_name: Optional[str] = Field(None, description="字段名称", max_length=100)
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", max_length=100)
    index_type: Optional[IndexTypeEnum] = Field(None, description="指标类型")
    column_desc: Optional[str] = Field(None, description="字段描述")
    data_type: Optional[str] = Field(None, description="数据类型", max_length=50)
    data_example: Optional[str] = Field(None, description="数据样例")
    comment: Optional[str] = Field(None, description="备注说明")
    is_vectorized: Optional[bool] = Field(None, description="是否已向量化")
    is_primary_key: Optional[bool] = Field(None, description="是否主键")
    is_sensitive: Optional[bool] = Field(None, description="是否敏感数据")


# ==================== 码值集相关模型 ====================

class CodeSetCreate(BaseModel):
    """码值集创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    code_set_name: str = Field(..., description="码值集名称", max_length=100, example="gender_code")
    code_set_desc: Optional[str] = Field(None, description="码值集描述", max_length=500, example="性别码值集")
    code_set_type: str = Field("ENUM", description="码值集类型", max_length=50, example="ENUM")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "code_set_name": "gender_code",
                "code_set_desc": "性别码值集",
                "code_set_type": "ENUM",
                "is_active": True
            }
        }


class CodeSetUpdate(BaseModel):
    """码值集更新模型"""
    id: int = Field(..., description="码值集ID", gt=0)
    code_set_name: Optional[str] = Field(None, description="码值集名称", max_length=100)
    code_set_desc: Optional[str] = Field(None, description="码值集描述", max_length=500)
    code_set_type: Optional[str] = Field(None, description="码值集类型", max_length=50)
    is_active: Optional[bool] = Field(None, description="是否激活")


# ==================== 码值关联相关模型 ====================

class CodeRelationCreate(BaseModel):
    """码值关联创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    column_id: int = Field(..., description="字段ID", gt=0, example=1)
    code_set_id: int = Field(..., description="码值集ID", gt=0, example=1)
    column_type: ColumnTypeEnum = Field(ColumnTypeEnum.SOURCE, description="字段类型")
    comment: Optional[str] = Field(None, description="备注", max_length=500)

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "column_id": 1,
                "code_set_id": 1,
                "column_type": "source",
                "comment": "客户性别字段关联性别码值集"
            }
        }


# ==================== 码值相关模型 ====================

class CodeValueCreate(BaseModel):
    """码值创建模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    code_set_id: int = Field(..., description="码值集ID", gt=0, example=1)
    code_value: str = Field(..., description="码值", max_length=100, example="M")
    code_desc: str = Field(..., description="码值描述", max_length=200, example="男性")
    code_value_cn: Optional[str] = Field(None, description="码值中文描述", max_length=200, example="男")
    is_active: bool = Field(True, description="是否激活")
    comment: Optional[str] = Field(None, description="备注", max_length=500)

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "code_set_id": 1,
                "code_value": "M",
                "code_desc": "男性",
                "code_value_cn": "男",
                "is_active": True,
                "comment": "性别码值-男性"
            }
        }


class CodeValueUpdate(BaseModel):
    """码值更新模型"""
    id: int = Field(..., description="码值ID", gt=0)
    code_value: Optional[str] = Field(None, description="码值", max_length=100)
    code_desc: Optional[str] = Field(None, description="码值描述", max_length=200)
    code_value_cn: Optional[str] = Field(None, description="码值中文描述", max_length=200)
    is_active: Optional[bool] = Field(None, description="是否激活")
    comment: Optional[str] = Field(None, description="备注", max_length=500)


# ==================== 统一API请求模型 ====================

class UnifiedGetRequest(BaseModel):
    """统一GET请求模型 - 支持单个ID或多个ID查询"""
    id: Optional[int] = Field(None, description="单个ID查询", gt=0, example=1)
    ids: Optional[List[int]] = Field(None, description="多个ID查询", min_items=1, max_items=1000, example=[1, 2, 3])
    knowledge_id: Optional[str] = Field(None, description="知识库ID过滤", example="kb_001")

    @validator('ids')
    def validate_id_or_ids(cls, v, values):
        if v is None and values.get('id') is None:
            raise ValueError('必须提供id或ids参数')
        if v is not None and values.get('id') is not None:
            raise ValueError('id和ids参数不能同时提供')
        return v

    class Config:
        json_schema_extra = {
            "examples": [
                {"id": 1, "knowledge_id": "kb_001"},
                {"ids": [1, 2, 3], "knowledge_id": "kb_001"}
            ]
        }


class UnifiedListRequest(BaseModel):
    """统一LIST请求模型 - 支持任意条件查询"""
    knowledge_id: Optional[str] = Field(None, description="知识库ID过滤", example="kb_001")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件", example={"is_active": True})
    page: int = Field(1, description="页码", ge=1, example=1)
    page_size: int = Field(20, description="每页大小", ge=1, le=1000, example=20)
    sort_by: Optional[str] = Field(None, description="排序字段", example="create_time")
    sort_order: str = Field("desc", description="排序方向", pattern="^(asc|desc)$", example="desc")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "filters": {"is_active": True, "data_layer": "ods"},
                "page": 1,
                "page_size": 20,
                "sort_by": "create_time",
                "sort_order": "desc"
            }
        }


# ==================== 批量操作请求模型 ====================

class BatchCreateRequest(BaseModel):
    """批量创建请求模型"""
    data: List[Dict[str, Any]] = Field(..., description="创建数据列表", min_items=1, max_items=1000)

    class Config:
        json_schema_extra = {
            "example": {
                "data": [
                    {
                        "knowledge_id": "kb_001",
                        "db_name": "customer_db",
                        "db_name_cn": "客户数据库",
                        "data_layer": "ods",
                        "is_active": True
                    }
                ]
            }
        }


class BatchUpdateRequest(BaseModel):
    """批量更新请求模型"""
    data: List[Dict[str, Any]] = Field(..., description="更新数据列表（必须包含主键）", min_items=1, max_items=1000)

    class Config:
        json_schema_extra = {
            "example": {
                "data": [
                    {
                        "db_id": 1,
                        "db_name_cn": "更新后的客户数据库",
                        "is_active": True
                    },
                    {
                        "db_id": 2,
                        "db_desc": "更新后的描述"
                    }
                ]
            }
        }


class BatchDeleteRequest(BaseModel):
    """批量删除请求模型"""
    data: List[Dict[str, Any]] = Field(..., description="删除条件列表", min_items=1, max_items=1000)

    class Config:
        json_schema_extra = {
            "example": {
                "data": [
                    {"db_id": 1},
                    {"db_id": 2}
                ]
            }
        }


# 源数据库专用请求模型
class SourceDatabaseCreateRequest(BaseModel):
    """源数据库创建请求模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_name: str = Field(..., description="数据库名称", max_length=100, example="customer_db")
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100, example="客户数据库")
    data_layer: DataLayerEnum = Field(DataLayerEnum.ODS, description="数据层级")
    db_desc: Optional[str] = Field(None, description="数据库描述", max_length=500, example="存储客户相关信息的数据库")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "db_name": "customer_db",
                "db_name_cn": "客户数据库",
                "data_layer": "ods",
                "db_desc": "存储客户相关信息的数据库",
                "is_active": True
            }
        }


class SourceDatabaseUpdateRequest(BaseModel):
    """源数据库更新请求模型"""
    db_id: int = Field(..., description="数据库ID", example=1)
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", max_length=100, example="更新后的客户数据库")
    db_desc: Optional[str] = Field(None, description="数据库描述", max_length=500, example="更新后的描述")
    is_active: Optional[bool] = Field(None, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "db_id": 1,
                "db_name_cn": "更新后的客户数据库",
                "is_active": True
            }
        }


# 源表专用请求模型
class SourceTableCreateRequest(BaseModel):
    """源表创建请求模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_id: int = Field(..., description="数据库ID", example=1)
    table_name: str = Field(..., description="表名称", max_length=100, example="customer_info")
    table_name_cn: Optional[str] = Field(None, description="表中文名称", max_length=100, example="客户信息表")
    table_desc: Optional[str] = Field(None, description="表描述", max_length=500, example="存储客户基本信息")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "db_id": 1,
                "table_name": "customer_info",
                "table_name_cn": "客户信息表",
                "table_desc": "存储客户基本信息",
                "is_active": True
            }
        }


# 源字段专用请求模型
class SourceColumnCreateRequest(BaseModel):
    """源字段创建请求模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    table_id: int = Field(..., description="表ID", example=1)
    column_name: str = Field(..., description="字段名称", max_length=100, example="customer_id")
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", max_length=100, example="客户ID")
    column_desc: Optional[str] = Field(None, description="字段描述", max_length=500, example="客户唯一标识")
    data_type: ColumnTypeEnum = Field(..., description="数据类型")
    data_example: Optional[str] = Field(None, description="数据示例", max_length=200, example="CUST001")
    is_vectorized: bool = Field(False, description="是否已向量化")
    is_primary_key: bool = Field(False, description="是否主键")
    is_sensitive: bool = Field(False, description="是否敏感字段")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "table_id": 1,
                "column_name": "customer_id",
                "column_name_cn": "客户ID",
                "column_desc": "客户唯一标识",
                "data_type": "string",
                "data_example": "CUST001",
                "is_vectorized": False,
                "is_primary_key": True,
                "is_sensitive": False
            }
        }


# 码值集专用请求模型
class CodeSetCreateRequest(BaseModel):
    """码值集创建请求模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    code_set_name: str = Field(..., description="码值集名称", max_length=100, example="gender_code")
    code_set_desc: Optional[str] = Field(None, description="码值集描述", max_length=500, example="性别码值集")
    code_set_type: CodeSetTypeEnum = Field(..., description="码值集类型")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "code_set_name": "gender_code",
                "code_set_desc": "性别码值集",
                "code_set_type": "enum",
                "is_active": True
            }
        }


# ==================== 搜索相关请求模型 ====================

class MetadataSearchRequest(BaseModel):
    """元数据搜索请求"""
    query: str = Field(..., description="搜索查询", min_length=1, max_length=200, example="客户")
    knowledge_id: Optional[str] = Field(None, description="知识库ID过滤", example="kb_001")
    entity_type: Optional[str] = Field(None, description="实体类型过滤", example="database")
    search_type: str = Field("hybrid", description="搜索类型", pattern="^(vector|text|hybrid)$", example="hybrid")
    limit: int = Field(10, description="返回结果数量限制", ge=1, le=100, example=10)
    min_score: float = Field(0.5, description="最小相似度分数", ge=0.0, le=1.0, example=0.5)
    vector_weight: float = Field(0.7, description="向量搜索权重", ge=0.0, le=1.0, example=0.7)
    text_weight: float = Field(0.3, description="文本搜索权重", ge=0.0, le=1.0, example=0.3)

    class Config:
        json_schema_extra = {
            "example": {
                "query": "客户信息",
                "knowledge_id": "kb_001",
                "entity_type": "table",
                "search_type": "hybrid",
                "limit": 10,
                "min_score": 0.5,
                "vector_weight": 0.7,
                "text_weight": 0.3
            }
        }
