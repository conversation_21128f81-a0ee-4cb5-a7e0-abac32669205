"""
DD提交数据最终确定服务

处理前端最终确定的内容，支持两种策略：
1. 删除重建策略：根据条件删除所有记录，然后批量插入新数据
2. 批量更新策略：根据条件批量更新现有记录

主要用于各个解读阶段的数据最终落库。
"""

from typing import Any, Dict, List, Optional, Literal
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class FinalizationService:
    """DD提交数据最终确定服务"""
    
    def __init__(self, dd_crud):
        """
        初始化服务
        
        Args:
            dd_crud: DD CRUD操作实例
        """
        self.dd_crud = dd_crud
    
    async def finalize_extraction_data(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]],
        strategy: Literal["delete_insert", "batch_update"] = "delete_insert"
    ) -> Dict[str, Any]:
        """
        最终确定报表解读数据（EXTRACTION阶段）
        
        Args:
            report_code: 报表代码（前端的report_code，对应我们的version）
            data_list: 前端提供的数据列表，每个元素包含：
                - entry_id: 对应我们的submission_id
                - entry_type: 对应我们的submission_type
                - DR22, BDR01, BDR03等字段
            strategy: 处理策略，"delete_insert"或"batch_update"
            
        Returns:
            Dict[str, Any]: 操作结果
            {
                "success": bool,
                "strategy": str,
                "affected_records": int,
                "message": str
            }
        """
        try:
            logger.info(f"开始最终确定报表解读数据: report_code={report_code}, "
                       f"数据量={len(data_list)}, 策略={strategy}")
            
            if strategy == "delete_insert":
                return await self._delete_insert_strategy(report_code, data_list)
            elif strategy == "batch_update":
                return await self._batch_update_strategy(report_code, data_list)
            else:
                raise ValueError(f"不支持的策略: {strategy}")
                
        except Exception as e:
            logger.error(f"最终确定报表解读数据失败: {e}")
            return {
                "success": False,
                "strategy": strategy,
                "affected_records": 0,
                "message": f"操作失败: {e}"
            }
    
    async def _delete_insert_strategy(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        删除重建策略：先删除所有匹配的记录，然后批量插入新数据
        
        Args:
            report_code: 报表代码
            data_list: 数据列表
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        logger.info(f"执行删除重建策略: report_code={report_code}")
        
        # 1. 删除所有匹配version的记录
        delete_conditions = [{"version": report_code}]
        delete_success = await self.dd_crud.delete_pre_distributions(delete_conditions)
        
        if not delete_success:
            raise Exception("删除现有记录失败")
        
        # 2. 准备插入数据
        insert_data = []
        for item in data_list:
            # 将前端字段映射到数据库字段
            record = self._map_frontend_to_db_fields(item, report_code)
            insert_data.append(record)
        
        # 3. 批量插入新数据
        if insert_data:
            insert_ids = await self.dd_crud.batch_create_pre_distributions(insert_data)
            affected_records = len(insert_ids)
        else:
            affected_records = 0
        
        logger.info(f"删除重建策略完成: 影响记录数={affected_records}")
        
        return {
            "success": True,
            "strategy": "delete_insert",
            "affected_records": affected_records,
            "message": f"成功删除重建 {affected_records} 条记录"
        }
    
    async def _batch_update_strategy(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        批量更新策略：根据submission_id和version批量更新现有记录
        
        Args:
            report_code: 报表代码
            data_list: 数据列表
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        logger.info(f"执行批量更新策略: report_code={report_code}")
        
        # 准备批量更新数据
        updates = []
        for item in data_list:
            # 将前端字段映射到数据库字段
            update_data = self._map_frontend_to_db_fields(item, report_code, exclude_keys=True)
            
            # 构建更新条件：根据submission_id和version定位唯一记录
            conditions = {
                "submission_id": item["entry_id"],
                "version": report_code
            }
            
            updates.append({
                "data": update_data,
                "filters": conditions
            })
        
        # 执行批量更新
        if updates:
            update_success = await self.dd_crud.update_pre_distributions(updates)
            affected_records = len(updates) if update_success else 0
        else:
            affected_records = 0
        
        logger.info(f"批量更新策略完成: 影响记录数={affected_records}")
        
        return {
            "success": update_success,
            "strategy": "batch_update", 
            "affected_records": affected_records,
            "message": f"成功更新 {affected_records} 条记录"
        }
    
    def _map_frontend_to_db_fields(
        self,
        frontend_item: Dict[str, Any],
        report_code: str,
        exclude_keys: bool = False
    ) -> Dict[str, Any]:
        """
        将前端字段映射到数据库字段
        
        Args:
            frontend_item: 前端数据项
            report_code: 报表代码
            exclude_keys: 是否排除主键字段（用于更新操作）
            
        Returns:
            Dict[str, Any]: 映射后的数据库字段
        """
        # 基础字段映射
        db_record = {
            "version": report_code,
            "submission_id": frontend_item["entry_id"],
            "submission_type": frontend_item.get("entry_type", ""),
        }
        
        # 如果是更新操作，排除主键字段
        if exclude_keys:
            db_record.pop("submission_id", None)
            db_record.pop("version", None)
        
        # 映射其他字段（DR22, BDR01, BDR03等）
        field_mappings = {
            "DR22": "dr22",
            "BDR01": "bdr01", 
            "BDR03": "bdr03",
            # 可以根据需要添加更多字段映射
        }
        
        for frontend_field, db_field in field_mappings.items():
            if frontend_field in frontend_item:
                value = frontend_item[frontend_field]
                # 如果是列表类型（如部门ID列表），转换为字符串
                if isinstance(value, list):
                    db_record[db_field] = ",".join(str(v) for v in value)
                else:
                    db_record[db_field] = str(value) if value is not None else ""
        
        return db_record
    
    async def finalize_duty_data(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]],
        strategy: Literal["delete_insert", "batch_update"] = "delete_insert"
    ) -> Dict[str, Any]:
        """
        最终确定义务解读数据（DUTY阶段）
        
        Args:
            report_code: 报表代码
            data_list: 数据列表
            strategy: 处理策略
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        # TODO: 实现义务解读阶段的数据处理逻辑
        # 目前先返回占位符结果
        logger.info(f"义务解读数据处理（待实现）: report_code={report_code}")
        
        return {
            "success": True,
            "strategy": strategy,
            "affected_records": 0,
            "message": "义务解读数据处理功能待实现"
        }
    
    async def finalize_biz_data(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]],
        strategy: Literal["delete_insert", "batch_update"] = "delete_insert"
    ) -> Dict[str, Any]:
        """
        最终确定业务解读数据（BIZ阶段）
        
        Args:
            report_code: 报表代码
            data_list: 数据列表
            strategy: 处理策略
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        # TODO: 实现业务解读阶段的数据处理逻辑
        logger.info(f"业务解读数据处理（待实现）: report_code={report_code}")
        
        return {
            "success": True,
            "strategy": strategy,
            "affected_records": 0,
            "message": "业务解读数据处理功能待实现"
        }
    
    async def finalize_tech_data(
        self,
        report_code: str,
        data_list: List[Dict[str, Any]],
        strategy: Literal["delete_insert", "batch_update"] = "delete_insert"
    ) -> Dict[str, Any]:
        """
        最终确定IT解读数据（TECH阶段）
        
        Args:
            report_code: 报表代码
            data_list: 数据列表
            strategy: 处理策略
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        # TODO: 实现IT解读阶段的数据处理逻辑
        logger.info(f"IT解读数据处理（待实现）: report_code={report_code}")
        
        return {
            "success": True,
            "strategy": strategy,
            "affected_records": 0,
            "message": "IT解读数据处理功能待实现"
        }
