"""
DD-B更新数据主处理器
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from utils.common.cache_manager import cache_manager
from .models.update_models import (
    UpdateRequest, UpdateItem, UpdateResult, BatchUpdateResult, UpdateStrategy
)
from .core.update_strategy import UpdateStrategyProcessor

logger = logging.getLogger(__name__)


class DDBUpdateProcessor:
    """DD-B更新数据主处理器"""
    
    def __init__(
        self, 
        rdb_client: Any, 
        vdb_client: Any = None, 
        embedding_client: Any = None,
        max_workers: int = 5,
        enable_cache: bool = True
    ):
        """
        初始化DD-B更新处理器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 嵌入模型客户端
            max_workers: 最大并发数
            enable_cache: 是否启用缓存
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.max_workers = max_workers
        self.enable_cache = enable_cache
        self.cache_session_dir: Optional[str] = None
        
        # 初始化策略处理器
        self.strategy_processor = UpdateStrategyProcessor(
            rdb_client, vdb_client, embedding_client
        )

        # 初始化更新CRUD
        from .crud.update_crud import UpdateCrud
        self.update_crud = UpdateCrud(rdb_client)

        # 初始化DD CRUD（用于Range查询）
        from modules.knowledge.dd.crud import DDCrud
        self.dd_crud = DDCrud(rdb_client)
        
        logger.debug(f"DD-B更新处理器初始化完成: max_workers={max_workers}, enable_cache={enable_cache}")
    
    async def process_update_request(self, request: UpdateRequest) -> BatchUpdateResult:
        """
        处理更新请求
        
        Args:
            request: 更新请求
            
        Returns:
            批量更新结果
        """
        start_time = time.time()
        result = BatchUpdateResult()
        
        try:
            logger.info(f"🚀 开始处理更新请求: report_code={request.report_code}, dept_id={request.dept_id}, 数据量={len(request.data)}")
            
            # 初始化缓存会话
            if self.enable_cache:
                self.cache_session_dir = cache_manager.create_session(
                    app_name="dd_b_update",
                    session_id=f"{request.report_code}_{request.dept_id}_{int(time.time())}"
                )
                logger.debug(f"缓存会话已创建: {self.cache_session_dir}")
            
            # 1. 构建更新项目列表
            update_items = []
            range_items = []  # 范围项列表，需要最后处理

            for item_data in request.data.copy():  # 使用copy避免修改原始数据
                entry_id = item_data.pop('entry_id')
                # 优先使用前端传入的entry_type，如果没有则从数据库获取
                frontend_entry_type = item_data.pop('entry_type', None)

                update_item = UpdateItem(
                    entry_id=entry_id,
                    entry_type=frontend_entry_type or 'ITEM',  # 优先使用前端值，否则用默认值
                    update_fields=item_data
                )

                # 标记是否来自前端
                update_item.frontend_entry_type = frontend_entry_type is not None

                # 暂时都添加到update_items，在验证阶段会根据数据库的submission_type重新分类
                update_items.append(update_item)
            
            logger.debug(f"构建更新项目完成: 总计{len(update_items)}个")

            # 2. 验证更新项目并加载原始数据（会从数据库的submission_type设置正确的entry_type）
            logger.info("1️⃣ 验证更新项目...")
            validated_items = await self.strategy_processor.validate_update_items(
                request.report_code, request.dept_id, update_items
            )

            # 3. 根据验证后的entry_type重新分类
            range_items = []
            submission_items = []
            for item in validated_items:
                if item.entry_type == 'TABLE':
                    range_items.append(item)
                else:
                    submission_items.append(item)

            logger.debug(f"重新分类完成: 填报项(ITEM){len(submission_items)}个, 范围项(TABLE){len(range_items)}个")

            # 更新validated_items为填报项列表
            validated_items = submission_items
            
            if not validated_items:
                logger.warning("没有有效的更新项目")
                result.execution_time = time.time() - start_time
                return result
            
            logger.info(f"1️⃣ 验证完成: 有效项目={len(validated_items)}/{len(update_items)}")
            
            # 3. 并发处理更新项目
            logger.info("2️⃣ 并发处理更新项目...")
            semaphore = asyncio.Semaphore(self.max_workers)
            
            async def process_single_item(item: UpdateItem) -> UpdateResult:
                async with semaphore:
                    return await self.strategy_processor.process_update_item(item)
            
            # 创建并发任务
            tasks = [process_single_item(item) for item in validated_items]
            
            # 执行并发处理
            update_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, update_result in enumerate(update_results):
                if isinstance(update_result, Exception):
                    # 处理异常情况
                    error_result = UpdateResult(
                        entry_id=validated_items[i].entry_id,
                        success=False,
                        strategy=validated_items[i].strategy,
                        error_message=f"处理异常: {update_result}"
                    )
                    result.add_result(error_result)
                else:
                    result.add_result(update_result)
            
            logger.info(f"2️⃣ 并发处理完成: 成功={result.success_count}, 失败={result.failed_count}")

            # 3. 处理范围项聚合（如果有填报项被更新）
            if range_items and result.success_count > 0:
                logger.info("3️⃣ 处理范围项聚合...")
                # 使用统一的Range聚合方法
                range_aggregation_result = await self._execute_range_aggregation(
                    request.report_code, request.dept_id
                )

                # 将Range聚合结果添加到返回数据中
                if range_aggregation_result and range_aggregation_result.get('range_items'):
                    for range_item in range_aggregation_result['range_items']:
                        result.results.append(UpdateResult(
                            entry_id=range_item['entry_id'],
                            success=True,
                            strategy=UpdateStrategy.RANGE_CALCULATION,
                            entry_type='TABLE',
                            updated_fields=range_item['updated_fields'],
                            error_message=None
                        ))

            # 4. 自动计算Range（如果前端没有提供Range且修改了需要Pipeline的字段）
            if not range_items and result.success_count > 0:
                logger.info("4️⃣ 检查是否需要自动计算Range...")
                range_calculation_result = await self._auto_calculate_range_if_needed(
                    request.report_code, request.dept_id, validated_items, result
                )

                # 如果计算了Range，需要将Range结果添加到返回结果中
                if range_calculation_result and range_calculation_result.get('range_items'):
                    logger.info("5️⃣ 添加Range计算结果到返回数据...")
                    for range_item in range_calculation_result['range_items']:
                        result.results.append(UpdateResult(
                            entry_id=range_item['entry_id'],
                            success=True,
                            strategy=UpdateStrategy.RANGE_CALCULATION,
                            entry_type='TABLE',
                            updated_fields=range_item['updated_fields'],
                            error_message=None
                        ))

            # 4. 保存处理结果到缓存（可选）
            if self.enable_cache and self.cache_session_dir:
                try:
                    cache_data = {
                        "request": {
                            "report_code": request.report_code,
                            "dept_id": request.dept_id,
                            "data_count": len(request.data)
                        },
                        "result": result.get_summary(),
                        "timestamp": time.time()
                    }
                    cache_manager.save_file("update_result.json", cache_data)
                    logger.debug("处理结果已保存到缓存")
                except Exception as e:
                    logger.warning(f"保存缓存失败: {e}")
            
            result.execution_time = time.time() - start_time
            logger.info(f"✅ 更新请求处理完成: 总耗时={result.execution_time:.2f}s, 成功率={result.success_count/result.total_count*100:.1f}%")
            
            return result
            
        except Exception as e:
            error_msg = f"处理更新请求失败: {e}"
            logger.error(error_msg)
            
            # 创建失败结果
            for item_data in request.data:
                entry_id = item_data.get('entry_id', 'unknown')
                error_result = UpdateResult(
                    entry_id=entry_id,
                    success=False,
                    strategy=None,
                    error_message=error_msg
                )
                result.add_result(error_result)
            
            result.execution_time = time.time() - start_time
            return result
            
        finally:
            # 清理缓存会话
            if self.enable_cache and self.cache_session_dir:
                try:
                    # 清理旧的会话目录（保留最近24小时的）
                    cache_manager.cleanup_old_sessions("dd_b_update", max_age_hours=24)
                    
                    # 清理当前会话目录
                    cache_manager.cleanup_session()
                    logger.debug("缓存会话已清理")
                except Exception as e:
                    logger.warning(f"清理缓存会话失败: {e}")
    
    async def get_update_preview(
        self, 
        request: UpdateRequest, 
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        获取更新预览（不实际执行更新）
        
        Args:
            request: 更新请求
            limit: 预览数量限制
            
        Returns:
            预览结果
        """
        try:
            logger.debug(f"生成更新预览: report_code={request.report_code}, dept_id={request.dept_id}, limit={limit}")
            
            # 构建更新项目列表（限制数量）
            preview_data = request.data[:limit]
            update_items = []
            
            for item_data in preview_data:
                entry_id = item_data.get('entry_id')
                update_fields = {k: v for k, v in item_data.items() if k != 'entry_id'}
                
                update_item = UpdateItem(
                    entry_id=entry_id,
                    update_fields=update_fields
                )
                update_items.append(update_item)
            
            # 验证更新项目
            validated_items = await self.strategy_processor.validate_update_items(
                request.report_code, request.dept_id, update_items
            )
            
            # 分析更新策略
            preview_results = []
            for item in validated_items:
                analysis_result = self.strategy_processor.field_analyzer.analyze_update_strategy(item)
                
                preview_results.append({
                    "entry_id": item.entry_id,
                    "strategy": analysis_result.strategy.value,
                    "pipeline_required": analysis_result.pipeline_required,
                    "affected_fields": analysis_result.affected_fields,
                    "reason": analysis_result.reason,
                    "update_fields": item.update_fields
                })
            
            return {
                "total_count": len(request.data),
                "preview_count": len(preview_results),
                "valid_count": len(validated_items),
                "invalid_count": len(update_items) - len(validated_items),
                "preview_results": preview_results
            }
            
        except Exception as e:
            logger.error(f"生成更新预览失败: {e}")
            return {
                "error": str(e),
                "total_count": len(request.data),
                "preview_count": 0,
                "valid_count": 0,
                "invalid_count": len(request.data),
                "preview_results": []
            }
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """
        获取处理器统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "max_workers": self.max_workers,
            "enable_cache": self.enable_cache,
            "current_session": self.cache_session_dir,
            "cache_stats": cache_manager.get_cache_stats() if self.enable_cache else None
        }

    async def _process_range_item_aggregation(
        self,
        report_code: str,
        dept_id: str,
        batch_result: BatchUpdateResult
    ) -> None:
        """
        处理范围项聚合

        当填报项被更新后，需要重新聚合相关的范围项

        Args:
            report_code: 报告代码
            dept_id: 部门ID
            batch_result: 批量更新结果
        """
        try:
            logger.debug(f"开始范围项聚合: report_code={report_code}, dept_id={dept_id}")

            # 导入聚合相关的模块
            from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
            from modules.dd_submission.dd_b.crud.metadata_crud import MetadataCrud

            # 初始化聚合组件
            metadata_crud = MetadataCrud(self.rdb_client)
            field_mapper = PipelineFieldMapper(metadata_crud)

            # 查询所有需要聚合的范围项
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            # 查询当前版本和部门的所有范围项（使用submission_type）
            conditions = [{
                "version": report_code,
                "dept_id": dept_id,
                "submission_type": "RANGE"
            }]

            range_records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions,
                batch_size=100,
                max_concurrency=3
            )

            if not range_records:
                logger.debug("没有找到需要聚合的范围项")
                return

            logger.debug(f"找到 {len(range_records)} 个范围项需要聚合")

            # 对每个范围项执行聚合
            aggregation_results = []

            for range_record in range_records:
                try:
                    # 查询该范围项下的所有填报项
                    range_submission_id = range_record.get('submission_id')

                    # 这里需要根据实际的业务逻辑来查询相关的填报项
                    # 暂时使用简化的逻辑：查询同一个dept_id下的所有填报项
                    fill_conditions = [{
                        "version": report_code,
                        "dept_id": dept_id,
                        "submission_type": "SUBMISSION"
                    }]

                    fill_records = await dd_crud.batch_query_post_distributions(
                        conditions_list=fill_conditions,
                        batch_size=100,
                        max_concurrency=3
                    )

                    if fill_records:
                        # 执行聚合逻辑（复用现有的聚合方法）
                        aggregated_result = field_mapper.aggregate_mapping_results(fill_records)

                        if aggregated_result:
                            # 更新范围项的聚合字段
                            range_record_id = range_record.get('id')

                            # 构建聚合后的更新字段
                            aggregated_fields = {}

                            # 这里需要根据实际的聚合逻辑来构建字段
                            # 暂时使用示例逻辑
                            if hasattr(aggregated_result, 'bdr05_aggregated'):
                                aggregated_fields['bdr05'] = aggregated_result.bdr05_aggregated
                            if hasattr(aggregated_result, 'bdr06_aggregated'):
                                aggregated_fields['bdr06'] = aggregated_result.bdr06_aggregated

                            if aggregated_fields:
                                # 更新范围项记录
                                success = await self.strategy_processor.update_crud.update_record_fields(
                                    range_record_id, aggregated_fields
                                )

                                if success:
                                    aggregation_results.append({
                                        "range_entry_id": range_submission_id,
                                        "success": True,
                                        "updated_fields": list(aggregated_fields.keys())
                                    })
                                    logger.debug(f"范围项聚合成功: {range_submission_id}")
                                else:
                                    aggregation_results.append({
                                        "range_entry_id": range_submission_id,
                                        "success": False,
                                        "error": "数据库更新失败"
                                    })

                except Exception as e:
                    logger.error(f"范围项聚合失败: {range_submission_id}, error={e}")
                    aggregation_results.append({
                        "range_entry_id": range_submission_id,
                        "success": False,
                        "error": str(e)
                    })

            # 统计聚合结果
            successful_aggregations = sum(1 for r in aggregation_results if r.get('success'))
            total_aggregations = len(aggregation_results)

            logger.info(f"范围项聚合完成: {successful_aggregations}/{total_aggregations} 成功")

        except Exception as e:
            logger.error(f"范围项聚合处理失败: {e}")
            # 聚合失败不影响主要的更新流程

    async def _auto_calculate_range_if_needed(
        self,
        report_code: str,
        dept_id: str,
        updated_items: List[UpdateItem],
        result: BatchUpdateResult
    ) -> Optional[Dict[str, Any]]:
        """
        自动计算Range（如果需要）

        Args:
            report_code: 报告代码
            dept_id: 部门ID
            updated_items: 已更新的项目列表
            result: 批量更新结果

        Returns:
            Range计算结果，包含range_items列表
        """
        try:
            # 检查是否有修改需要Pipeline的字段（BDR09/BDR10/BDR11/BDR16）
            # 包括原始更新项目和实际更新结果（兜底更新）
            # 注意：BDR16表范围未变化的SIMPLE_UPDATE不触发Range计算
            pipeline_fields_modified = False

            # 1. 检查原始更新项目
            for item in updated_items:
                pipeline_fields = item.get_pipeline_fields()
                # 只检查BDR字段，不包括SDR
                bdr_fields = {k: v for k, v in pipeline_fields.items()
                             if k.upper() in ['BDR09', 'BDR10', 'BDR11', 'BDR16']}

                # 检查是否有非空的BDR字段修改
                non_empty_bdr_fields = {k: v for k, v in bdr_fields.items()
                                       if v != '' and v is not None}

                # 特殊处理BDR16：如果只修改了BDR16且表范围未变化（SIMPLE_UPDATE），不触发Range计算
                if non_empty_bdr_fields:
                    # 如果只有BDR16被修改，检查是否为表范围变化
                    if (len(non_empty_bdr_fields) == 1 and 'BDR16' in non_empty_bdr_fields and
                        hasattr(item, 'strategy') and item.strategy == UpdateStrategy.SIMPLE_UPDATE):
                        logger.debug(f"BDR16表范围未变化（SIMPLE_UPDATE），跳过Range计算: entry_id={item.entry_id}")
                        continue

                    pipeline_fields_modified = True
                    logger.debug(f"检测到原始项目非空BDR字段修改: {list(non_empty_bdr_fields.keys())}")
                    break

            # 2. 检查实际更新结果（包括兜底更新）
            if not pipeline_fields_modified:
                for update_result in result.results:
                    if update_result.success and update_result.updated_fields:
                        # 检查更新结果中的BDR字段
                        updated_bdr_fields = {k: v for k, v in update_result.updated_fields.items()
                                            if k.upper() in ['BDR09', 'BDR10', 'BDR11', 'BDR16']
                                            and v != '' and v is not None}

                        # 特殊处理BDR16：如果只修改了BDR16且为SIMPLE_UPDATE策略，不触发Range计算
                        if updated_bdr_fields:
                            if (len(updated_bdr_fields) == 1 and 'bdr16' in updated_bdr_fields and
                                update_result.strategy == UpdateStrategy.SIMPLE_UPDATE):
                                logger.debug(f"BDR16表范围未变化（SIMPLE_UPDATE），跳过Range计算: entry_id={update_result.entry_id}")
                                continue

                            pipeline_fields_modified = True
                            logger.debug(f"检测到更新结果非空BDR字段修改: {list(updated_bdr_fields.keys())}")
                            break

            if not pipeline_fields_modified:
                logger.debug("没有修改需要Pipeline的非空BDR字段，跳过Range计算")
                return None

            logger.info(f"检测到BDR字段修改，开始自动计算Range: report_code={report_code}, dept_id={dept_id}")

            # 执行统一的Range聚合（避免重复查询）
            return await self._execute_range_aggregation(report_code, dept_id)



        except Exception as e:
            logger.error(f"自动计算Range失败: {e}")
            # 不影响主流程，继续执行
            return None

    async def _execute_range_aggregation(self, report_code: str, dept_id: str) -> Optional[Dict[str, Any]]:
        """
        执行统一的Range聚合（避免重复查询）

        Args:
            report_code: 报告代码
            dept_id: 部门ID

        Returns:
            Range聚合结果
        """
        try:
            logger.info(f"执行统一Range聚合: report_code={report_code}, dept_id={dept_id}")

            # 使用RangeAggregator进行轻量级Range聚合
            from modules.dd_submission.dd_b.utils.range_aggregator import RangeAggregator

            # 1. 一次性查询所有submission记录（已经包含最新更新的数据）
            submission_conditions = [{
                "version": report_code,
                "dept_id": dept_id,
                "submission_type": "SUBMISSION"
            }]
            submission_records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=submission_conditions,
                batch_size=100,
                max_concurrency=3,
                timeout_per_batch=60.0
            )

            # 2. 一次性查询所有Range记录
            range_conditions = [{
                "version": report_code,
                "dept_id": dept_id,
                "submission_type": "RANGE"
            }]
            range_records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=range_conditions,
                batch_size=100,
                max_concurrency=3,
                timeout_per_batch=60.0
            )

            if not range_records:
                logger.debug("没有找到Range记录，跳过聚合")
                return None

            logger.debug(f"找到 {len(submission_records)} 个submission记录, {len(range_records)} 个Range记录")

            # 3. 使用RangeAggregator并发聚合所有Range记录
            aggregator = RangeAggregator()

            # 并发处理Range记录聚合
            semaphore = asyncio.Semaphore(3)  # 限制并发数为3

            async def process_single_range_record(range_record):
                async with semaphore:
                    try:
                        # 聚合Range字段（使用统一查询的submission_records）
                        aggregated_record = await aggregator.aggregate_range_fields(
                            range_record=range_record,
                            submission_list=submission_records
                        )

                        # 更新数据库中的Range记录
                        record_id = int(range_record.get('id', 0))
                        update_fields = {k.lower(): v for k, v in aggregated_record.items()
                                       if k.lower().startswith(('bdr', 'sdr'))}

                        await self.update_crud.update_record_fields(record_id, update_fields)

                        logger.debug(f"Range记录聚合完成: record_id={record_id}")
                        return aggregated_record

                    except Exception as e:
                        logger.error(f"Range记录聚合失败: record_id={range_record.get('id')}, error={e}")
                        return range_record  # 保留原记录

            # 创建并发任务
            range_tasks = [process_single_range_record(record) for record in range_records]

            # 执行并发聚合
            updated_range_records = await asyncio.gather(*range_tasks, return_exceptions=True)

            # 处理异常结果
            for i, result in enumerate(updated_range_records):
                if isinstance(result, Exception):
                    logger.error(f"Range记录聚合异常: record_id={range_records[i].get('id')}, error={result}")
                    updated_range_records[i] = range_records[i]  # 使用原记录

            # 准备返回给前端的Range记录
            range_items = []
            for record in updated_range_records:
                # Range记录的entry_id就是submission_id
                entry_id = record.get('submission_id', '')

                range_items.append({
                    'entry_id': entry_id,
                    'updated_fields': {
                        # 返回所有BDR和SDR字段
                        k.lower(): v for k, v in record.items()
                        if k.lower().startswith(('bdr', 'sdr')) and v is not None
                    }
                })

            logger.info(f"✅ 统一Range聚合完成: 聚合Range记录数={len(updated_range_records)}, 返回Range项目数={len(range_items)}")

            return {
                'success': True,
                'range_items': range_items,
                'affected_count': len(updated_range_records)
            }

        except Exception as e:
            logger.error(f"统一Range聚合失败: {e}")
            return None
