"""
DD-B更新模块的CRUD操作
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from modules.knowledge.dd.crud import DDCrud

logger = logging.getLogger(__name__)


class UpdateCrud:
    """更新相关的CRUD操作"""
    
    def __init__(self, rdb_client: Any):
        """
        初始化更新CRUD
        
        Args:
            rdb_client: 关系数据库客户端
        """
        self.rdb_client = rdb_client
        self.dd_crud = DDCrud(rdb_client)
        
        logger.debug("更新CRUD初始化完成")
    
    async def get_record_by_identifiers(
        self, 
        report_code: str, 
        dept_id: str, 
        entry_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        根据标识符获取记录
        
        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID
            entry_id: 条目ID（submission_id）
            
        Returns:
            记录数据，如果不存在返回None
        """
        try:
            logger.debug(f"查询记录: report_code={report_code}, dept_id={dept_id}, entry_id={entry_id}")
            
            # 查询post_distribution表
            post_conditions = [{
                "version": report_code,
                "dept_id": dept_id,
                "submission_id": entry_id
            }]
            
            post_records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=post_conditions,
                batch_size=1,
                max_concurrency=1
            )
            
            if not post_records:
                logger.warning(f"未找到记录: report_code={report_code}, dept_id={dept_id}, entry_id={entry_id}")
                return None
            
            post_record = post_records[0]
            
            # 查询关联的pre_distribution记录
            pre_id = post_record.get('pre_distribution_id')
            if pre_id:
                pre_conditions = [{"id": pre_id}]
                pre_records = await self.dd_crud.batch_query_pre_distributions(
                    conditions_list=pre_conditions,
                    batch_size=1,
                    max_concurrency=1
                )
                
                if pre_records:
                    pre_record = pre_records[0]
                    # 合并dr字段
                    post_record['dr01'] = pre_record.get('dr01', '')
                    post_record['dr09'] = pre_record.get('dr09', '')
                    post_record['dr17'] = pre_record.get('dr17', '')
            
            # 查询table_ids（复用现有逻辑）
            table_ids = await self._get_table_ids_for_dept(dept_id)
            post_record['table_ids'] = table_ids
            
            logger.debug(f"记录查询成功: id={post_record.get('id')}")
            return post_record
            
        except Exception as e:
            logger.error(f"查询记录失败: report_code={report_code}, dept_id={dept_id}, entry_id={entry_id}, error={e}")
            return None
    
    async def update_record_fields(
        self, 
        record_id: int, 
        update_fields: Dict[str, Any]
    ) -> bool:
        """
        更新记录字段
        
        Args:
            record_id: 记录ID
            update_fields: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            if not update_fields:
                logger.debug(f"没有字段需要更新: record_id={record_id}")
                return True
            
            logger.debug(f"更新记录字段: record_id={record_id}, fields={list(update_fields.keys())}")

            # 使用现有的DDCrud.update_post_distributions方法
            success = await self.dd_crud.update_post_distributions(
                updates=update_fields,
                conditions={"id": record_id}
            )

            if success:
                logger.debug(f"记录更新成功: record_id={record_id}")
                return True
            else:
                logger.warning(f"记录更新失败: record_id={record_id}")
                return False
            
        except Exception as e:
            logger.error(f"更新记录字段失败: record_id={record_id}, error={e}")
            return False
    
    async def batch_update_records(
        self, 
        updates: List[Tuple[int, Dict[str, Any]]]
    ) -> Tuple[int, int]:
        """
        批量更新记录
        
        Args:
            updates: 更新列表，每个元素为(record_id, update_fields)
            
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        try:
            logger.debug(f"开始批量更新: 总数={len(updates)}")
            
            for record_id, update_fields in updates:
                success = await self.update_record_fields(record_id, update_fields)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
            
            logger.info(f"批量更新完成: 成功={success_count}, 失败={failed_count}")
            
        except Exception as e:
            logger.error(f"批量更新失败: error={e}")
            failed_count = len(updates) - success_count
        
        return success_count, failed_count
    
    async def _get_table_ids_for_dept(self, dept_id: str) -> List[str]:
        """
        获取部门对应的table_ids
        
        Args:
            dept_id: 部门ID
            
        Returns:
            table_ids列表
        """
        try:
            # 查询department relations
            conditions = [{"dept_id": dept_id}]
            relations = await self.dd_crud.batch_query_department_relations(
                conditions_list=conditions,
                batch_size=100,
                max_concurrency=1
            )
            
            if not relations:
                # 如果没有找到关联，使用默认值
                logger.warning(f"dept_id={dept_id} 没有找到关联的table_ids，使用默认值")
                return [str(i) for i in range(1, 95)]
            
            # 提取table_ids
            table_ids = []
            for relation in relations:
                table_id = relation.get('table_id')
                if table_id:
                    try:
                        table_id_int = int(table_id)
                        table_ids.append(str(table_id_int))
                    except (ValueError, TypeError):
                        continue
            
            return table_ids if table_ids else [str(i) for i in range(1, 95)]
            
        except Exception as e:
            logger.error(f"获取table_ids失败: dept_id={dept_id}, error={e}")
            return [str(i) for i in range(1, 95)]
    
    async def validate_update_request(
        self, 
        report_code: str, 
        dept_id: str, 
        entry_ids: List[str]
    ) -> Tuple[List[str], List[str]]:
        """
        验证更新请求中的记录是否存在
        
        Args:
            report_code: 报告代码
            dept_id: 部门ID
            entry_ids: 条目ID列表
            
        Returns:
            (存在的entry_ids, 不存在的entry_ids)
        """
        existing_ids = []
        missing_ids = []
        
        try:
            logger.debug(f"验证更新请求: report_code={report_code}, dept_id={dept_id}, entry_ids={len(entry_ids)}")
            
            # 批量查询所有记录
            conditions = []
            for entry_id in entry_ids:
                conditions.append({
                    "version": report_code,
                    "dept_id": dept_id,
                    "submission_id": entry_id
                })
            
            records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=conditions,
                batch_size=100,
                max_concurrency=5
            )
            
            # 提取存在的submission_ids
            found_submission_ids = set()
            for record in records:
                submission_id = record.get('submission_id')
                if submission_id:
                    found_submission_ids.add(submission_id)
            
            # 分类存在和不存在的IDs
            for entry_id in entry_ids:
                if entry_id in found_submission_ids:
                    existing_ids.append(entry_id)
                else:
                    missing_ids.append(entry_id)
            
            logger.debug(f"验证完成: 存在={len(existing_ids)}, 不存在={len(missing_ids)}")
            
        except Exception as e:
            logger.error(f"验证更新请求失败: error={e}")
            missing_ids = entry_ids  # 出错时认为都不存在
        
        return existing_ids, missing_ids
