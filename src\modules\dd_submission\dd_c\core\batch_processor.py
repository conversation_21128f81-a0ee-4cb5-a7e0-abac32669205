"""
DD-B批量处理器

这个模块负责处理大批量的DD-B数据，包括：
1. 批量查询数据库记录
2. 过滤RANGE类型记录
3. 分配worker进行Pipeline处理
4. 聚合处理结果
5. 处理RANGE记录的特殊逻辑

主要特性：
- 支持几千条记录的高效处理
- 15个worker并发处理
- 智能的RANGE记录处理
- 完整的错误处理和重试机制
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import time

from modules.dd_submission.dd_b.utils.pipeline_field_mapper import (
    PipelineFieldMapper,
    PipelineFieldMappingResult
)
from modules.dd_submission.dd_b.core.field_aggregator import (
    FieldAggregator,
    FieldAggregationResult
)

logger = logging.getLogger(__name__)


@dataclass
class BatchRecord:
    """批量处理记录"""
    id: int  # 主键ID
    dept_id: str  # 部门ID
    version: str  # 版本（对应report_code）
    submission_type: str  # 提交类型
    pre_distribution_id: Optional[int] = None  # 预分发ID
    dr09: Optional[str] = None  # 数据项名称
    dr17: Optional[str] = None  # 需求口径
    raw_data: Optional[Dict[str, Any]] = None  # 原始数据


@dataclass
class BatchProcessingResult:
    """批量处理结果"""
    total_records: int = 0
    normal_records: int = 0
    range_records: int = 0
    processed_records: int = 0
    failed_records: int = 0
    
    # 处理结果
    normal_results: List[Dict[str, str]] = field(default_factory=list)
    range_result: Optional[Dict[str, str]] = None
    
    # 统计信息
    processing_time: float = 0.0
    pipeline_time: float = 0.0
    aggregation_time: float = 0.0
    
    # 错误信息
    errors: List[str] = field(default_factory=list)


class BatchProcessor:
    """DD-B批量处理器"""
    
    def __init__(
        self,
        rdb_client,
        dd_crud,
        pipeline_integrator,
        metadata_crud,
        max_workers: int = 15,
        batch_size: int = 100,
        max_concurrency: int = 5
    ):
        """
        初始化批量处理器
        
        Args:
            rdb_client: RDB客户端
            dd_crud: DD CRUD对象
            pipeline_integrator: Pipeline集成器
            metadata_crud: 元数据CRUD
            max_workers: 最大worker数量，默认15
            batch_size: 批量查询大小，默认100
            max_concurrency: 最大并发数，默认5
        """
        self.rdb_client = rdb_client
        self.dd_crud = dd_crud
        self.pipeline_integrator = pipeline_integrator
        self.metadata_crud = metadata_crud
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.max_concurrency = max_concurrency
        
        # 创建Pipeline字段映射器和聚合器
        self.field_mapper = PipelineFieldMapper(metadata_crud)
        self.field_aggregator = FieldAggregator(frequency_threshold=0.5)
        
        # 线程池用于并发处理
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        logger.info(f"批量处理器初始化完成: max_workers={max_workers}, "
                   f"batch_size={batch_size}, max_concurrency={max_concurrency}")
    
    async def process_batch_records(
        self,
        dept_id: str,
        version: str,
        where_conditions: List[Dict[str, Any]],
        table_ids: Optional[List[str]] = None
    ) -> BatchProcessingResult:
        """
        处理批量记录的主入口

        Args:
            dept_id: 部门ID
            version: 版本号（对应report_code）
            where_conditions: 查询条件列表，每个dict包含额外的查询条件
            table_ids: 表ID列表（可选）

        Returns:
            BatchProcessingResult: 批量处理结果
        """
        start_time = time.time()
        result = BatchProcessingResult()
        
        try:
            logger.info(f"开始批量处理: dept_id={dept_id}, version={version}, 条件数={len(where_conditions)}")

            # 1. 批量查询记录
            all_records = await self._query_batch_records(dept_id, version, where_conditions)
            result.total_records = len(all_records)
            
            if not all_records:
                logger.warning(f"未找到匹配的记录: dept_id={dept_id}, version={version}")
                return result
            
            logger.info(f"查询到 {len(all_records)} 条记录")
            
            # 2. 分离普通记录和RANGE记录
            normal_records, range_records = self._separate_records_by_type(all_records)
            result.normal_records = len(normal_records)
            result.range_records = len(range_records)
            
            logger.info(f"记录分类: 普通记录={len(normal_records)}, RANGE记录={len(range_records)}")
            
            # 3. 处理普通记录
            if normal_records:
                pipeline_start = time.time()
                normal_mapping_results = await self._process_normal_records(
                    normal_records, table_ids
                )
                result.pipeline_time = time.time() - pipeline_start
                
                # 转换为字符串格式
                result.normal_results = self.field_aggregator.convert_to_string_format(
                    normal_mapping_results
                )
                result.processed_records = len(result.normal_results)
                
                logger.info(f"普通记录处理完成: 成功={len(result.normal_results)}")
                
                # 4. 聚合结果并处理RANGE记录
                if range_records:
                    aggregation_start = time.time()
                    result.range_result = await self._process_range_records(
                        normal_mapping_results, range_records[0]
                    )
                    result.aggregation_time = time.time() - aggregation_start
                    
                    logger.info("RANGE记录处理完成")
            
            result.processing_time = time.time() - start_time
            
            logger.info(f"批量处理完成: 总耗时={result.processing_time:.2f}s, "
                       f"成功记录={result.processed_records + (1 if result.range_result else 0)}")
            
            return result
            
        except Exception as e:
            error_msg = f"批量处理失败: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            result.processing_time = time.time() - start_time
            return result
    
    async def _query_batch_records(
        self,
        dept_id: str,
        version: str,
        where_conditions: List[Dict[str, Any]]
    ) -> List[BatchRecord]:
        """
        批量查询数据库记录

        Args:
            dept_id: 部门ID
            version: 版本号
            where_conditions: 查询条件列表

        Returns:
            List[BatchRecord]: 批量记录列表
        """
        try:
            logger.info(f"开始批量查询: dept_id={dept_id}, version={version}, 条件数={len(where_conditions)}")

            # 1. 先查询pre_distribution获取dr09和dr17
            pre_conditions = []
            for where_condition in where_conditions:
                condition = {
                    "dept_id": dept_id,
                    "version": version,
                    **where_condition  # 合并额外的查询条件
                }
                pre_conditions.append(condition)

            logger.info(f"查询pre_distribution: {len(pre_conditions)}个条件")
            pre_records = await self.dd_crud.batch_query_pre_distributions(
                conditions_list=pre_conditions,
                batch_size=self.batch_size,
                max_concurrency=self.max_concurrency
            )

            if not pre_records:
                logger.warning("pre_distribution查询结果为空")
                return []

            logger.info(f"pre_distribution查询完成: 找到{len(pre_records)}条记录")

            # 2. 构建post_distribution查询条件
            post_conditions = []
            for pre_record in pre_records:
                condition = {
                    "dept_id": dept_id,
                    "version": version,
                    "dr09": pre_record.get("dr09"),
                    "dr17": pre_record.get("dr17"),
                    "pre_distribution_id": pre_record.get("id")
                }
                post_conditions.append(condition)

            logger.info(f"查询post_distribution: {len(post_conditions)}个条件")
            raw_records = await self.dd_crud.batch_query_post_distributions(
                conditions_list=post_conditions,
                batch_size=self.batch_size,
                max_concurrency=self.max_concurrency
            )
            
            # 转换为BatchRecord对象
            batch_records = []
            for raw_record in raw_records:
                batch_record = BatchRecord(
                    id=raw_record.get("id"),
                    dept_id=raw_record.get("dept_id"),
                    version=raw_record.get("version"),
                    submission_type=raw_record.get("submission_type", "NORMAL"),
                    pre_distribution_id=raw_record.get("pre_distribution_id"),
                    dr09=raw_record.get("dr09"),
                    dr17=raw_record.get("dr17"),
                    raw_data=raw_record
                )
                batch_records.append(batch_record)
            
            logger.info(f"批量查询完成: 找到 {len(batch_records)} 条记录")
            return batch_records
            
        except Exception as e:
            logger.error(f"批量查询失败: {str(e)}")
            raise
    
    def _separate_records_by_type(
        self,
        all_records: List[BatchRecord]
    ) -> Tuple[List[BatchRecord], List[BatchRecord]]:
        """
        根据submission_type分离记录
        
        Args:
            all_records: 所有记录
            
        Returns:
            Tuple[List[BatchRecord], List[BatchRecord]]: (普通记录, RANGE记录)
        """
        normal_records = []
        range_records = []
        
        for record in all_records:
            if record.submission_type == "RANGE":
                range_records.append(record)
            else:
                normal_records.append(record)
        
        return normal_records, range_records

    async def _process_normal_records(
        self,
        normal_records: List[BatchRecord],
        table_ids: Optional[List[str]] = None
    ) -> List[PipelineFieldMappingResult]:
        """
        处理普通记录（非RANGE）

        Args:
            normal_records: 普通记录列表
            table_ids: 表ID列表

        Returns:
            List[PipelineFieldMappingResult]: 映射结果列表
        """
        if not normal_records:
            return []

        logger.info(f"开始处理 {len(normal_records)} 个普通记录")

        # 使用15个worker并发处理
        semaphore = asyncio.Semaphore(self.max_workers)

        async def process_single_record(record: BatchRecord) -> Optional[PipelineFieldMappingResult]:
            async with semaphore:
                try:
                    # 执行Pipeline
                    pipeline_request = self._create_pipeline_request(record, table_ids)
                    pipeline_result = await self.pipeline_integrator.execute_pipeline(pipeline_request)

                    # 映射字段
                    if pipeline_result.execution_success:
                        mapping_result = await self.field_mapper.map_single_record(
                            pipeline_result=pipeline_result.__dict__,
                            original_record=record,
                            keep_raw_format=True
                        )
                        return mapping_result
                    else:
                        logger.warning(f"Pipeline执行失败: record_id={record.id}")
                        return None

                except Exception as e:
                    logger.error(f"处理记录失败: record_id={record.id}, error={str(e)}")
                    return None

        # 并发处理所有记录
        tasks = [process_single_record(record) for record in normal_records]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤成功的结果
        successful_results = []
        for result in results:
            if isinstance(result, PipelineFieldMappingResult) and result.mapping_success:
                successful_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"处理异常: {str(result)}")

        logger.info(f"普通记录处理完成: 成功={len(successful_results)}/{len(normal_records)}")
        return successful_results

    async def _process_range_records(
        self,
        normal_mapping_results: List[PipelineFieldMappingResult],
        range_record: BatchRecord
    ) -> Dict[str, str]:
        """
        处理RANGE记录

        Args:
            normal_mapping_results: 普通记录的映射结果
            range_record: RANGE记录

        Returns:
            Dict[str, str]: RANGE记录的字段值
        """
        logger.info(f"开始处理RANGE记录: record_id={range_record.id}")

        # 使用新的字段聚合器聚合普通记录的结果
        aggregation_result = self.field_aggregator.aggregate_mapping_results(normal_mapping_results)

        # 生成RANGE记录的字段
        range_fields = aggregation_result.to_range_record_fields()

        # 添加记录标识信息
        range_fields.update({
            'record_id': str(range_record.id),
            'dept_id': range_record.dept_id,
            'version': range_record.version,
            'submission_type': range_record.submission_type
        })

        logger.info(f"RANGE记录处理完成: record_id={range_record.id}, 字段数={len(range_fields)}")
        logger.info(f"聚合统计: 成功记录={aggregation_result.successful_records}, "
                   f"表数={len(aggregation_result.bdr09_aggregated)}, "
                   f"字段数={len(aggregation_result.bdr11_aggregated)}")

        return range_fields

    def _create_pipeline_request(
        self,
        record: BatchRecord,
        table_ids: Optional[List[str]] = None
    ):
        """
        创建Pipeline请求对象

        Args:
            record: 批量记录
            table_ids: 表ID列表

        Returns:
            Pipeline请求对象
        """
        # 这里需要根据实际的Pipeline请求类来创建
        # 假设有一个PipelineRequest类
        from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineRequest

        # 如果没有提供table_ids，使用默认值
        if table_ids is None:
            table_ids = [str(i) for i in range(1, 95)]  # 默认1-94

        return PipelineRequest(
            record_id=record.id,
            table_ids=table_ids,
            user_question=record.dr09 or "",
            hint=record.dr17 or ""
        )

    async def get_final_results(
        self,
        processing_result: BatchProcessingResult
    ) -> List[Dict[str, str]]:
        """
        获取最终的合并结果

        Args:
            processing_result: 批量处理结果

        Returns:
            List[Dict[str, str]]: 最终结果列表
        """
        final_results = []

        # 添加普通记录结果
        final_results.extend(processing_result.normal_results)

        # 添加RANGE记录结果
        if processing_result.range_result:
            final_results.append(processing_result.range_result)

        logger.info(f"最终结果合并完成: 总记录数={len(final_results)}")
        return final_results

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
