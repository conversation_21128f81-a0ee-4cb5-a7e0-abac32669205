def get_indicator_variable(indicator_sql):
    variables = ["${start_date}", "${end_date}", "${org_num}"]
    result = []

    for var in variables:
        if var in indicator_sql:
            result.append({
                "paramName": var,
                "paramType": ""
            })

    return result


def get_indicator_platform(item):
    if item['submission_type'] == "SUBMISSION":
        out_data = {'indexName': item['dr09'],
                    'indexClassifyId': "",
                    'indexCaliberBiz': item['dr17'],
                    'indexCaliberCal': item['sdr10'],
                    'indexType': 'sql',
                    'sql': item['sdr10'],
                    "sqlIndexInfoCols": [{
                        "indexColType": "index_date",  # 不能为空，支持 index_date, index_dim, index_val，分别代表，日期列，纬度列，指标列
                        "colCode": "data_dt",  # 不能为空，sql指标中该字段为输出列的列名，例如 select aa from ..., 那么这个aa就是一个输出列列名
                        "colFormat": {  # 只有indexColType为index_val的列才生效，对指标值的格式化，可以为空
                            "type": "STANDARD",  # 格式化类型，支持 PERCENTAGE ， CUSTOM ，STANDARD(该模式不对输出内容做任何修改)。
                            "option": {},
                            "quantityUnit": "",
                        }
                    }, {
                        "indexColType": "index_val",  # 不能为空，支持 index_date, index_dim, index_val，分别代表，日期列，纬度列，指标列
                        "colCode": "indicator",  # 不能为空，sql指标中该字段为输出列的列名，例如 select aa from ..., 那么这个aa就是一个输出列列名
                        "colFormat": {  # 只有indexColType为index_val的列才生效，对指标值的格式化，可以为空
                            "type": "STANDARD",  # 格式化类型，支持 PERCENTAGE ， CUSTOM ，STANDARD(该模式不对输出内容做任何修改)。
                            "option": {"type": "CUSTOM", "decimal": 4, "useThousandSeparator": True},
                            "unit": "万元",  # 单位：即后缀
                        }}],
                    'sqlIndexFormulaParams': get_indicator_variable(item['sdr10'])
                    }
    else:
        return {}
    return out_data


if __name__ == '__main__':
    test_data = {"type":"SUBMISSION","dr09": "项目1", "dr17": "业务描述", "sdr10": '''
    WITH tmp_00 AS ( 
SELECT data_dt, amount * 0.025 AS value_a, amount AS value_b 
FROM bdm_gdc_index_fin 
WHERE data_dt between  '${start_date}' and  '${end_date}' AND `1104_report` = 'G40' AND index_code = '2_A' ), ccp_margin AS ( 
SELECT `1104_report`, reference_content_1, vlookup_key_value_1, vlookup_key_value_2, vlookup_key_value_3, value_1, acct_type, p_dt 
FROM ( 
SELECT a.`1104_report`, a.reference_content_1, a.vlookup_key_value_1, a.vlookup_key_value_2, a.vlookup_key_value_3, a.value_1, a.value_2, a.p_dt 
FROM ods_cdp_gdc_table_1104_reference_table_full AS a 
WHERE a.p_dt between  '${start_date}' and  '${end_date}' AND a.`1104_report` = 'G14' AND a.reference_content_1 = 'CCP Margin' ) AS ui LATERAL VIEW EXPLODE(SPLIT(ui.value_2, ",")) adtable AS acct_type ), t_all AS ( 
SELECT '1_4_1' AS row_id, SUM(a.non_exempt_exposure) AS value_a1, SUM(CASE WHEN a.exposure_type = '1' THEN a.total_risk_exposure_miti END) AS value_b, SUM(CASE WHEN a.exposure_type = '2' THEN a.total_risk_exposure_miti END) AS value_c, SUM(CASE WHEN a.exposure_type = '3' THEN a.total_risk_exposure_miti END) AS value_d, SUM(CASE WHEN a.exposure_type = '4' THEN a.total_risk_exposure_miti END) AS value_e, SUM(CASE WHEN a.exposure_type = '5' THEN a.total_risk_exposure_miti END) AS value_f, SUM(CASE WHEN a.exposure_type = '6' THEN a.total_risk_exposure_miti END) AS value_g 
,a.data_dt as data_dt FROM adm_rsk_exposure AS a 
WHERE a.data_dt between  '${start_date}' and  '${end_date}' AND group_cust_no IN ( 
SELECT group_cust_no 
FROM ( 
SELECT group_cust_no 
FROM adm_rsk_exposure AS p1 
LEFT JOIN tmp_00 AS p2 
ON p1.data_dt = p2.data_dt 
WHERE p1.data_dt between  '${start_date}' and  '${end_date}' AND group_cust_type = '01' AND group_cust_flag = 'Y' 
GROUP BY group_cust_no HAVING SUM(total_risk_exposure_miti) >= MAX(p2.value_a) 
ORDER BY SUM(non_exempt_exposure) DESC LIMIT 1 ) AS a ) ) 
SELECT data_dt, value_a1 as indicator 
FROM ( 
SELECT 'CNHSBC900Z' AS org_no, row_id AS rowid, COALESCE(CAST(value_a1 AS DECIMAL(20, 4)), 0) AS value_a1, GETDATE() AS rec_creat_dt_tm 
,data_dt FROM t_all ) sub 
WHERE org_no='${org_num}' and rowid = '1_4_1'
    '''}
    data = get_indicator_platform(test_data)
    print(data)
