"""
通用缓存管理器
用于管理各种应用的缓存文件和检查点
"""

import os
import time
import shutil
import json
from typing import Optional, Dict, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """
    通用缓存管理器
    
    目录结构:
    cache/
    ├── dd_b/
    │   ├── 20241128_143022_abc123/
    │   │   ├── checkpoint_001.json
    │   │   └── checkpoint_002.json
    │   └── 20241128_150030_def456/
    ├── dd_c/
    └── other_app/
    """
    
    def __init__(self, base_cache_dir: str = "./cache"):
        """
        初始化缓存管理器
        
        Args:
            base_cache_dir: 基础缓存目录路径
        """
        self.base_cache_dir = base_cache_dir
        self.current_session_dir: Optional[str] = None
        self.app_name: Optional[str] = None
        
        # 确保基础缓存目录存在
        os.makedirs(self.base_cache_dir, exist_ok=True)
        logger.debug(f"缓存管理器初始化: base_dir={self.base_cache_dir}")
    
    def create_session(self, app_name: str, session_id: Optional[str] = None) -> str:
        """
        创建新的会话目录
        
        Args:
            app_name: 应用名称 (如: dd_b, dd_c, etc.)
            session_id: 可选的会话ID，如果不提供则自动生成
            
        Returns:
            会话目录的完整路径
        """
        self.app_name = app_name
        
        # 生成会话目录名: 时间戳_随机ID
        if session_id is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = f"{timestamp}_{int(time.time() * 1000) % 1000000:06d}"
        
        # 创建应用目录
        app_dir = os.path.join(self.base_cache_dir, app_name)
        os.makedirs(app_dir, exist_ok=True)
        
        # 创建会话目录
        self.current_session_dir = os.path.join(app_dir, session_id)
        os.makedirs(self.current_session_dir, exist_ok=True)
        
        logger.debug(f"创建会话目录: {self.current_session_dir}")
        return self.current_session_dir
    
    def get_session_dir(self) -> Optional[str]:
        """获取当前会话目录"""
        return self.current_session_dir
    
    def save_file(self, filename: str, data: Any, as_json: bool = True) -> str:
        """
        保存文件到当前会话目录
        
        Args:
            filename: 文件名
            data: 要保存的数据
            as_json: 是否以JSON格式保存
            
        Returns:
            保存的文件完整路径
        """
        if not self.current_session_dir:
            raise ValueError("没有活动的会话目录，请先调用create_session()")
        
        file_path = os.path.join(self.current_session_dir, filename)
        
        if as_json:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        else:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(data))
        
        logger.debug(f"文件已保存: {file_path}")
        return file_path
    
    def load_file(self, filename: str, as_json: bool = True) -> Optional[Any]:
        """
        从当前会话目录加载文件
        
        Args:
            filename: 文件名
            as_json: 是否以JSON格式加载
            
        Returns:
            加载的数据，如果文件不存在返回None
        """
        if not self.current_session_dir:
            return None
        
        file_path = os.path.join(self.current_session_dir, filename)
        if not os.path.exists(file_path):
            return None
        
        try:
            if as_json:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = f.read()
            
            logger.debug(f"文件已加载: {file_path}")
            return data
        except Exception as e:
            logger.error(f"加载文件失败: {file_path}, error={e}")
            return None
    
    def cleanup_session(self, keep_on_error: bool = False) -> bool:
        """
        清理当前会话目录
        
        Args:
            keep_on_error: 如果清理失败是否保留目录
            
        Returns:
            清理是否成功
        """
        if not self.current_session_dir or not os.path.exists(self.current_session_dir):
            return True
        
        try:
            shutil.rmtree(self.current_session_dir)
            logger.debug(f"会话目录已清理: {self.current_session_dir}")
            self.current_session_dir = None
            return True
        except Exception as e:
            logger.error(f"清理会话目录失败: {self.current_session_dir}, error={e}")
            if not keep_on_error:
                # 尝试重命名为错误目录
                try:
                    error_dir = f"{self.current_session_dir}_error_{int(time.time())}"
                    os.rename(self.current_session_dir, error_dir)
                    logger.warning(f"会话目录重命名为错误目录: {error_dir}")
                except:
                    pass
            return False
    
    def cleanup_old_sessions(self, app_name: str, max_age_hours: int = 24) -> int:
        """
        清理指定应用的旧会话目录
        
        Args:
            app_name: 应用名称
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的目录数量
        """
        app_dir = os.path.join(self.base_cache_dir, app_name)
        if not os.path.exists(app_dir):
            return 0
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        try:
            for session_dir in os.listdir(app_dir):
                session_path = os.path.join(app_dir, session_dir)
                if not os.path.isdir(session_path):
                    continue
                
                # 检查目录创建时间
                dir_mtime = os.path.getmtime(session_path)
                if current_time - dir_mtime > max_age_seconds:
                    try:
                        shutil.rmtree(session_path)
                        cleaned_count += 1
                        logger.debug(f"清理旧会话目录: {session_path}")
                    except Exception as e:
                        logger.warning(f"清理旧会话目录失败: {session_path}, error={e}")
        
        except Exception as e:
            logger.error(f"清理旧会话目录时出错: {app_dir}, error={e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个旧会话目录")
        
        return cleaned_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        stats = {
            "base_dir": self.base_cache_dir,
            "current_session": self.current_session_dir,
            "apps": {}
        }
        
        try:
            if os.path.exists(self.base_cache_dir):
                for app_name in os.listdir(self.base_cache_dir):
                    app_path = os.path.join(self.base_cache_dir, app_name)
                    if os.path.isdir(app_path):
                        sessions = [d for d in os.listdir(app_path) 
                                  if os.path.isdir(os.path.join(app_path, d))]
                        stats["apps"][app_name] = {
                            "session_count": len(sessions),
                            "sessions": sessions
                        }
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
        
        return stats


# 全局缓存管理器实例
cache_manager = CacheManager()
