# Pipeline字段映射器使用文档

## 概述

`PipelineFieldMapper` 是一个可复用的模块，用于将Pipeline执行结果映射到DD-B的BDR和SDR字段。

## 主要特性

1. **单记录映射**: 将单个Pipeline结果映射到字段
2. **批量映射**: 批量处理多个Pipeline结果
3. **字段聚合**: 聚合多个映射结果，支持统计分析
4. **格式转换**: 支持原始格式和字符串格式的转换
5. **RANGE处理**: 特殊处理submission_type为RANGE的记录

## 字段映射关系

### BDR字段
- **BDR09**: 表英文名列表 (从parser_info.tables提取)
- **BDR10**: 表中文名列表 (查询metadata获取)
- **BDR11**: 字段信息字典 (从parser_info.columns提取)
- **BDR16**: 业务逻辑描述 (从business_logic构建)

### SDR字段
- **SDR05**: 与BDR09相同
- **SDR06**: 与BDR10相同
- **SDR08**: 与BDR11相同
- **SDR09**: 字段中文名字典 (查询metadata获取)
- **SDR10**: SQL语句 (从sql_candidates提取)
- **SDR12**: JOIN条件 (从parser_info.join提取)

## 基本使用

### 1. 创建映射器

```python
from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
from modules.knowledge.metadata.crud import MetadataCRUD

# 创建metadata CRUD
metadata_crud = MetadataCRUD(rdb_client)

# 创建映射器
mapper = PipelineFieldMapper(metadata_crud)
```

### 2. 单记录映射

```python
# Pipeline执行结果
pipeline_result = {
    "business_logic": {...},
    "parser_info": {...},
    "sql_candidates": [...]
}

# 映射单个记录
mapping_result = await mapper.map_single_record(
    pipeline_result=pipeline_result,
    original_record=record,
    keep_raw_format=True  # 保持原始格式，便于聚合
)

# 检查映射结果
if mapping_result.mapping_success:
    print(f"BDR09: {mapping_result.bdr09_raw}")
    print(f"BDR10: {mapping_result.bdr10_raw}")
    print(f"SDR09: {mapping_result.sdr09_raw}")
```

### 3. 批量映射

```python
# 批量映射多个记录
mapping_results = await mapper.map_batch_records(
    pipeline_results=pipeline_results,
    original_records=original_records,
    keep_raw_format=True
)

print(f"成功映射 {len(mapping_results)} 个记录")
```

### 4. 字段聚合

```python
# 聚合映射结果
aggregated = mapper.aggregate_mapping_results(mapping_results)

print(f"聚合后的表名: {aggregated.bdr09_aggregated}")
print(f"聚合后的字段: {aggregated.bdr11_aggregated}")
```

### 5. 格式转换

```python
# 转换为字符串格式（用于存储）
string_results = mapper.convert_to_string_format(mapping_results)

# 单个结果转换
string_format = mapping_result.to_string_format()
```

## 高级使用场景

### 场景1: 处理大批量数据

```python
async def process_large_batch(report_code: str, dept_id: str):
    # 1. 查询所有记录
    all_records = await query_records(report_code, dept_id)
    
    # 2. 过滤掉RANGE类型
    normal_records = [r for r in all_records if r.submission_type != 'RANGE']
    range_records = [r for r in all_records if r.submission_type == 'RANGE']
    
    # 3. 批量处理普通记录
    pipeline_results = await execute_pipelines_batch(normal_records)
    mapping_results = await mapper.map_batch_records(pipeline_results, normal_records)
    
    # 4. 聚合结果
    aggregated = mapper.aggregate_mapping_results(mapping_results)
    
    # 5. 处理RANGE记录
    range_fields = aggregated.to_range_record()
    
    # 6. 转换为最终格式
    normal_string_results = mapper.convert_to_string_format(mapping_results)
    
    return normal_string_results, range_fields
```

### 场景2: 自定义聚合逻辑

```python
def custom_aggregate_fields(mapping_results: List[PipelineFieldMappingResult]):
    # 自定义聚合逻辑
    all_tables = set()
    all_columns = {}
    
    for result in mapping_results:
        if result.bdr09_raw:
            all_tables.update(result.bdr09_raw)
        if result.bdr11_raw:
            all_columns.update(result.bdr11_raw)
    
    return {
        'tables': list(all_tables),
        'columns': all_columns
    }
```

## 数据结构说明

### PipelineFieldMappingResult

```python
@dataclass
class PipelineFieldMappingResult:
    record_id: int
    mapping_success: bool
    mapping_notes: List[str]
    
    # 原始格式字段（便于聚合）
    bdr09_raw: Optional[List[str]]  # 表英文名
    bdr10_raw: Optional[List[str]]  # 表中文名
    bdr11_raw: Optional[Dict[str, str]]  # 字段信息
    sdr09_raw: Optional[Dict[str, str]]  # 字段中文名
    # ... 其他字段
```

### AggregatedFieldResult

```python
@dataclass
class AggregatedFieldResult:
    bdr09_aggregated: List[str]  # 聚合后的表英文名
    bdr10_aggregated: List[str]  # 聚合后的表中文名
    bdr11_aggregated: Dict[str, str]  # 聚合后的字段信息
    sdr09_aggregated: Dict[str, str]  # 聚合后的字段中文名
    sdr12_candidates: List[str]  # SDR12候选值
```

## 错误处理

映射器内置了完善的错误处理机制：

1. **Pipeline结果为空**: 返回失败状态，记录错误信息
2. **Metadata查询失败**: 使用原始名称作为备选
3. **数据格式异常**: 自动转换为合适的格式
4. **批量处理异常**: 单个失败不影响其他记录

## 性能优化建议

1. **批量查询**: 使用批量映射而不是逐个处理
2. **缓存metadata**: 对频繁查询的表和字段信息进行缓存
3. **并发控制**: 大批量处理时控制并发数量
4. **内存管理**: 及时释放不需要的中间结果

## 注意事项

1. **格式一致性**: 保持原始格式直到最后转换，便于聚合
2. **错误容忍**: 单个字段映射失败不应影响整体流程
3. **日志记录**: 重要操作都有详细的日志记录
4. **类型安全**: 使用类型注解确保数据类型正确
