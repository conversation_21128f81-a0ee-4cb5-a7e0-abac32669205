"""
Knowledge V2 API 响应模型

基于现有数据库表结构的统一API响应模型，确保与数据模型完全一致
提供完整的数据模型定义，便于FastAPI/Apifox等工具识别
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


# ==================== 基础响应模型 ====================

class BatchOperationStats(BaseModel):
    """批量操作统计信息"""
    total_requested: int = Field(..., description="请求处理总数", example=10)
    total_successful: int = Field(..., description="成功处理数量", example=10)
    total_failed: int = Field(..., description="失败处理数量", example=0)
    execution_time: float = Field(..., description="执行时间(秒)", example=1.23)
    batch_count: int = Field(..., description="批次数量", example=1)
    concurrency_used: int = Field(..., description="实际并发数", example=5)

    class Config:
        json_schema_extra = {
            "example": {
                "total_requested": 10,
                "total_successful": 10,
                "total_failed": 0,
                "execution_time": 1.23,
                "batch_count": 1,
                "concurrency_used": 5
            }
        }


class PaginationInfo(BaseModel):
    """分页信息"""
    page: int = Field(..., description="当前页码", example=1)
    page_size: int = Field(..., description="每页大小", example=20)
    total_pages: int = Field(..., description="总页数", example=5)
    total_records: int = Field(..., description="总记录数", example=100)
    has_next: bool = Field(..., description="是否有下一页", example=True)
    has_prev: bool = Field(..., description="是否有上一页", example=False)

    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 20,
                "total_pages": 5,
                "total_records": 100,
                "has_next": True,
                "has_prev": False
            }
        }


# ==================== 源数据库响应模型 ====================

class SourceDatabaseResponse(BaseModel):
    """源数据库响应模型"""
    db_id: int = Field(..., description="数据库ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_name: str = Field(..., description="数据库名称", example="customer_db")
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", example="客户数据库")
    data_layer: str = Field(..., description="数据层级", example="ods")
    db_desc: Optional[str] = Field(None, description="数据库描述", example="存储客户相关信息的数据库")
    is_active: bool = Field(..., description="是否激活", example=True)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "db_id": 1,
                "knowledge_id": "kb_001",
                "db_name": "customer_db",
                "db_name_cn": "客户数据库",
                "data_layer": "ods",
                "db_desc": "存储客户相关信息的数据库",
                "is_active": True,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 指标数据库响应模型 ====================

class IndexDatabaseResponse(BaseModel):
    """指标数据库响应模型"""
    db_id: int = Field(..., description="数据库ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_name: str = Field(..., description="数据库名称", example="index_db")
    db_name_cn: Optional[str] = Field(None, description="数据库中文名称", example="指标数据库")
    data_layer: str = Field(..., description="数据层级", example="ads")
    db_desc: Optional[str] = Field(None, description="数据库描述", example="存储指标相关信息的数据库")
    is_active: bool = Field(..., description="是否激活", example=True)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")


# ==================== 源表响应模型 ====================

class SourceTableResponse(BaseModel):
    """源表响应模型"""
    table_id: int = Field(..., description="表ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_id: int = Field(..., description="数据库ID", example=1)
    table_name: str = Field(..., description="表名称", example="customer_info")
    table_name_cn: Optional[str] = Field(None, description="表中文名称", example="客户信息表")
    table_desc: Optional[str] = Field(None, description="表描述", example="存储客户基本信息")
    is_active: bool = Field(..., description="是否激活", example=True)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "table_id": 1,
                "knowledge_id": "kb_001",
                "db_id": 1,
                "table_name": "customer_info",
                "table_name_cn": "客户信息表",
                "table_desc": "存储客户基本信息",
                "is_active": True,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 指标表响应模型 ====================

class IndexTableResponse(BaseModel):
    """指标表响应模型"""
    table_id: int = Field(..., description="表ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    db_id: int = Field(..., description="数据库ID", example=1)
    table_name: str = Field(..., description="表名称", example="customer_metrics")
    table_name_cn: Optional[str] = Field(None, description="表中文名称", example="客户指标表")
    table_desc: Optional[str] = Field(None, description="表描述", example="存储客户相关指标")
    is_active: bool = Field(..., description="是否激活", example=True)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")


# ==================== 源字段响应模型 ====================

class SourceColumnResponse(BaseModel):
    """源字段响应模型"""
    column_id: int = Field(..., description="字段ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    table_id: int = Field(..., description="表ID", example=1)
    column_name: str = Field(..., description="字段名称", example="customer_id")
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", example="客户ID")
    column_desc: Optional[str] = Field(None, description="字段描述", example="客户唯一标识")
    data_type: Optional[str] = Field(None, description="数据类型", example="STRING")
    data_example: Optional[str] = Field(None, description="数据样例", example="CUST001")
    is_vectorized: bool = Field(..., description="是否已向量化", example=False)
    is_primary_key: bool = Field(..., description="是否主键", example=True)
    is_sensitive: bool = Field(..., description="是否敏感数据", example=False)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "column_id": 1,
                "knowledge_id": "kb_001",
                "table_id": 1,
                "column_name": "customer_id",
                "column_name_cn": "客户ID",
                "column_desc": "客户唯一标识",
                "data_type": "STRING",
                "data_example": "CUST001",
                "is_vectorized": False,
                "is_primary_key": True,
                "is_sensitive": False,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 指标字段响应模型 ====================

class IndexColumnResponse(BaseModel):
    """指标字段响应模型"""
    column_id: int = Field(..., description="字段ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    table_id: int = Field(..., description="表ID", example=1)
    column_name: str = Field(..., description="字段名称", example="customer_count")
    column_name_cn: Optional[str] = Field(None, description="字段中文名称", example="客户数量")
    index_type: Optional[str] = Field(None, description="指标类型", example="atom")
    column_desc: Optional[str] = Field(None, description="字段描述", example="统计客户总数")
    data_type: Optional[str] = Field(None, description="数据类型", example="NUMBER")
    data_example: Optional[str] = Field(None, description="数据样例", example="1000")
    comment: Optional[str] = Field(None, description="备注说明", example="原子指标")
    is_vectorized: bool = Field(..., description="是否已向量化", example=False)
    is_primary_key: bool = Field(..., description="是否主键", example=False)
    is_sensitive: bool = Field(..., description="是否敏感数据", example=False)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")


# ==================== 码值集响应模型 ====================

class CodeSetResponse(BaseModel):
    """码值集响应模型"""
    id: int = Field(..., description="码值集ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    code_set_name: str = Field(..., description="码值集名称", example="gender_code")
    code_set_desc: Optional[str] = Field(None, description="码值集描述", example="性别码值集")
    code_set_type: str = Field(..., description="码值集类型", example="ENUM")
    is_active: bool = Field(..., description="是否激活", example=True)
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "knowledge_id": "kb_001",
                "code_set_name": "gender_code",
                "code_set_desc": "性别码值集",
                "code_set_type": "ENUM",
                "is_active": True,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 码值关联响应模型 ====================

class CodeRelationResponse(BaseModel):
    """码值关联响应模型"""
    id: int = Field(..., description="关联ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    column_id: int = Field(..., description="字段ID", example=1)
    code_set_id: int = Field(..., description="码值集ID", example=1)
    column_type: str = Field(..., description="字段类型", example="source")
    comment: Optional[str] = Field(None, description="备注", example="客户性别字段关联性别码值集")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "knowledge_id": "kb_001",
                "column_id": 1,
                "code_set_id": 1,
                "column_type": "source",
                "comment": "客户性别字段关联性别码值集",
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 码值响应模型 ====================

class CodeValueResponse(BaseModel):
    """码值响应模型"""
    id: int = Field(..., description="码值ID", example=1)
    knowledge_id: str = Field(..., description="知识库ID", example="kb_001")
    code_set_id: int = Field(..., description="码值集ID", example=1)
    code_value: str = Field(..., description="码值", example="M")
    code_desc: str = Field(..., description="码值描述", example="男性")
    code_value_cn: Optional[str] = Field(None, description="码值中文描述", example="男")
    is_active: bool = Field(..., description="是否激活", example=True)
    comment: Optional[str] = Field(None, description="备注", example="性别码值-男性")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "knowledge_id": "kb_001",
                "code_set_id": 1,
                "code_value": "M",
                "code_desc": "男性",
                "code_value_cn": "男",
                "is_active": True,
                "comment": "性别码值-男性",
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 统一API响应模型 ====================

class UnifiedResponse(BaseModel):
    """统一API响应模型"""
    success: bool = Field(..., description="操作是否成功", example=True)
    message: str = Field(..., description="响应消息", example="操作成功")
    data: Optional[Any] = Field(None, description="返回数据")
    timestamp: datetime = Field(..., description="响应时间")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)", example=0.123)

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {"id": 1, "name": "示例数据"},
                "timestamp": "2024-01-01T10:00:00",
                "execution_time": 0.123
            }
        }


class UnifiedListResponse(BaseModel):
    """统一列表响应模型"""
    success: bool = Field(..., description="操作是否成功", example=True)
    message: str = Field(..., description="响应消息", example="查询成功")
    data: List[Dict[str, Any]] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")
    timestamp: datetime = Field(..., description="响应时间")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)", example=0.123)

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "查询成功",
                "data": [{"id": 1, "name": "示例数据1"}, {"id": 2, "name": "示例数据2"}],
                "pagination": {
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 5,
                    "total_records": 100,
                    "has_next": True,
                    "has_prev": False
                },
                "timestamp": "2024-01-01T10:00:00",
                "execution_time": 0.123
            }
        }


class BatchOperationResponse(BaseModel):
    """批量操作响应模型"""
    success: bool = Field(..., description="操作是否成功", example=True)
    message: str = Field(..., description="响应消息", example="批量操作成功")
    data: Optional[List[Any]] = Field(None, description="返回数据（创建操作时为创建的ID列表，查询操作时为查询结果列表）", example=[1, 2, 3])
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="错误详情", example=None)
    affected_rows: Optional[int] = Field(None, description="影响的行数", example=3)

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "批量创建成功",
                "data": [1, 2, 3],
                "errors": None,
                "affected_rows": 3
            }
        }


# ==================== 搜索响应模型 ====================

class MetadataSearchResultItem(BaseModel):
    """元数据搜索结果项"""
    score: float = Field(..., description="相似度分数", example=0.85)
    entity_type: str = Field(..., description="实体类型", example="database")
    entity_data: Dict[str, Any] = Field(..., description="实体数据")
    vector_info: Optional[Dict[str, Any]] = Field(None, description="向量信息")

    class Config:
        json_schema_extra = {
            "example": {
                "score": 0.85,
                "entity_type": "database",
                "entity_data": {
                    "db_id": 1,
                    "db_name": "customer_db",
                    "db_name_cn": "客户数据库"
                },
                "vector_info": {
                    "vector_id": "vec_001",
                    "embedding_model": "moka-m3e-base"
                }
            }
        }


class MetadataSearchResponse(BaseModel):
    """元数据搜索响应"""
    success: bool = Field(..., description="搜索是否成功", example=True)
    message: str = Field(..., description="响应消息", example="搜索完成")
    results: List[MetadataSearchResultItem] = Field(..., description="搜索结果")
    total_found: int = Field(..., description="找到的总数", example=10)
    search_time: float = Field(..., description="搜索耗时(秒)", example=0.456)
    search_params: Dict[str, Any] = Field(..., description="搜索参数")
    timestamp: datetime = Field(..., description="响应时间")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "搜索完成，找到10条结果",
                "results": [
                    {
                        "score": 0.85,
                        "entity_type": "database",
                        "entity_data": {"db_id": 1, "db_name": "customer_db"}
                    }
                ],
                "total_found": 10,
                "search_time": 0.456,
                "search_params": {
                    "query": "客户",
                    "search_type": "hybrid",
                    "limit": 10
                },
                "timestamp": "2024-01-01T10:00:00"
            }
        }


# ==================== 系统状态响应模型 ====================

class SystemStatusResponse(BaseModel):
    """系统状态响应"""
    status: str = Field(..., description="系统状态", example="healthy")
    version: str = Field(..., description="API版本", example="2.0.0")
    timestamp: datetime = Field(..., description="响应时间")
    database_status: Dict[str, Any] = Field(..., description="数据库状态")
    vector_status: Dict[str, Any] = Field(..., description="向量数据库状态")
    performance_metrics: Dict[str, Any] = Field(..., description="性能指标")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "2.0.0",
                "timestamp": "2024-01-01T10:00:00",
                "database_status": {
                    "connected": True,
                    "response_time_ms": 12.5,
                    "type": "MySQL"
                },
                "vector_status": {
                    "connected": True,
                    "response_time_ms": 8.3,
                    "type": "PGVector"
                },
                "performance_metrics": {
                    "uptime_seconds": 3600,
                    "memory_usage": "512MB",
                    "cpu_usage": "15%"
                }
            }
        }


# ==================== 错误响应模型 ====================

class ErrorDetail(BaseModel):
    """错误详情"""
    code: str = Field(..., description="错误代码", example="VALIDATION_ERROR")
    message: str = Field(..., description="错误消息", example="字段验证失败")
    field: Optional[str] = Field(None, description="相关字段", example="db_name")
    value: Optional[Any] = Field(None, description="错误值", example="")

    class Config:
        json_schema_extra = {
            "example": {
                "code": "VALIDATION_ERROR",
                "message": "字段验证失败",
                "field": "db_name",
                "value": ""
            }
        }


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = Field(False, description="操作是否成功")
    error_type: str = Field(..., description="错误类型", example="ValidationError")
    message: str = Field(..., description="错误消息", example="请求参数验证失败")
    details: Optional[List[ErrorDetail]] = Field(None, description="错误详情")
    timestamp: datetime = Field(..., description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID", example="req_12345")

    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "error_type": "ValidationError",
                "message": "请求参数验证失败",
                "details": [
                    {
                        "code": "VALIDATION_ERROR",
                        "message": "字段不能为空",
                        "field": "db_name",
                        "value": ""
                    }
                ],
                "timestamp": "2024-01-01T10:00:00",
                "request_id": "req_12345"
            }
        }
