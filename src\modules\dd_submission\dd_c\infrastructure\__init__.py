"""
DD-B模块基础设施组件

提供DD-B模块的基础设施支持，包括：
- 数据模型定义
- 常量和配置
- 异常处理机制
"""

# 数据模型
from .models import (
    ProcessingStatusEnum,
    FieldStatusEnum,
    GenerationStrategyEnum,
    DDBProcessRequest,
    DDBRecord,
    FieldFillInfo,
    DDBProcessResult,
    DDBValidationRequest,
    DDBValidationResult,
    HistorySearchResult,
    GenerationDecision
)

# 常量和工具
from .constants import (
    DDBConstants,
    DDBFieldTypes,
    DDBErrorCodes,
    DDBUtils
)

# 异常处理
from .exceptions import (
    DDBError,
    DDBValidationError,
    DDBDatabaseError,
    DDBProcessingError,
    DDBTimeoutError,
    DDBDataNotFoundError,
    create_validation_error,
    create_database_error,
    create_processing_error,
    create_timeout_error,
    create_data_not_found_error,
    handle_ddb_errors,
    handle_async_ddb_errors,
    DDBError<PERSON>ormatter,
    DDBErrorRecovery
)

__all__ = [
    # 数据模型
    "ProcessingStatusEnum",
    "FieldStatusEnum",
    "GenerationStrategyEnum",
    "DDBProcessRequest",
    "DDBRecord",
    "FieldFillInfo",
    "DDBProcessResult",
    "DDBValidationRequest",
    "DDBValidationResult",
    "HistorySearchResult",
    "GenerationDecision",
    
    # 常量和工具
    "DDBConstants",
    "DDBFieldTypes",
    "DDBErrorCodes",
    "DDBUtils",
    
    # 异常处理
    "DDBError",
    "DDBValidationError",
    "DDBDatabaseError",
    "DDBProcessingError",
    "DDBTimeoutError",
    "DDBDataNotFoundError",
    "create_validation_error",
    "create_database_error",
    "create_processing_error",
    "create_timeout_error",
    "create_data_not_found_error",
    "handle_ddb_errors",
    "handle_async_ddb_errors",
    "DDBErrorFormatter",
    "DDBErrorRecovery"
]
