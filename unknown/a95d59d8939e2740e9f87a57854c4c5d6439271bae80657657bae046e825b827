from typing import List, Dict, Any, Callable, Optional
import logging
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class Task:
    """表示一个子任务"""
    name: str  # 任务名称
    function: Callable  # 执行任务的函数
    dependencies: List[str] = None  # 依赖的任务名称
    params: Dict[str, Any] = None  # 任务参数

    def __post_init__(self):
        self.dependencies = self.dependencies or []
        self.params = self.params or {}
        self.result = None  # 任务执行结果
        self.status = "pending"  # 状态：pending, running, completed, failed


class Workflow:
    """工作流管理器，用于组织和执行一系列任务"""

    def __init__(self, name: str):
        self.name = name
        self.tasks = {}  # 任务字典: {任务名称: Task对象}
        self.results = {}  # 结果字典: {任务名称: 任务结果}
        self.execution_order = []  # 执行顺序

    def add_task(self, task: Task) -> None:
        """添加任务到工作流"""
        if task.name in self.tasks:
            raise ValueError(f"任务名称已存在: {task.name}")
        self.tasks[task.name] = task
        logger.info(f"添加任务: {task.name}")

    def add_tasks(self, tasks: List[Task]) -> None:
        """批量添加任务"""
        for task in tasks:
            self.add_task(task)

    def _validate_dependencies(self) -> None:
        """验证所有任务的依赖关系是否有效"""
        for task_name, task in self.tasks.items():
            for dep in task.dependencies:
                if dep not in self.tasks:
                    raise ValueError(f"任务 {task_name} 依赖不存在的任务: {dep}")

    def _determine_execution_order(self) -> None:
        """确定任务执行顺序（拓扑排序）"""
        self._validate_dependencies()

        # Kahn算法实现拓扑排序
        in_degree = {task_name: 0 for task_name in self.tasks}
        for task in self.tasks.values():
            for dep in task.dependencies:
                in_degree[task.name] += 1

        queue = [task_name for task_name, degree in in_degree.items() if degree == 0]
        self.execution_order = []

        while queue:
            task_name = queue.pop(0)
            self.execution_order.append(task_name)

            for task in self.tasks.values():
                if task_name in task.dependencies:
                    in_degree[task.name] -= 1
                    if in_degree[task.name] == 0:
                        queue.append(task.name)

        if len(self.execution_order) != len(self.tasks):
            raise ValueError("工作流中存在循环依赖")

    def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行工作流"""
        self._determine_execution_order()
        context = context or {}
        logger.info(f"开始执行工作流: {self.name}")

        for task_name in self.execution_order:
            task = self.tasks[task_name]

            # 检查依赖是否已完成
            for dep in task.dependencies:
                if self.tasks[dep].status != "completed":
                    raise RuntimeError(f"任务 {task_name} 的依赖 {dep} 未完成")

            # 准备任务参数：解析 lambda 表达式
            task_params = {}
            for param_name, param_value in task.params.items():
                if callable(param_value):
                    # 如果参数值是可调用对象（如 lambda），则传入 results 执行
                    logger.info(f"{self.name} workflow content:{self.results}")
                    task_params[param_name] = param_value(self.results)
                else:
                    # 否则直接使用参数值
                    task_params[param_name] = param_value

            # 执行任务
            logger.info(f"执行任务: {task_name}")
            task.status = "running"
            try:
                logger.info(f"{task_name}任务传入参数:{task_params}")
                task.result = task.function(**task_params)
                logger.info(f"{task_name}任务返回结果:{task.result}")
                task.status = "completed"
                self.results[task_name] = task.result
                logger.info(f"任务 {task_name} 完成")
            except Exception as e:
                task.status = "failed"
                logger.error(f"任务 {task_name} 失败: {str(e)}")
                raise Exception(f"工作流 {self.name} 执行失败\n"
                                f"任务 {task_name} 失败: {str(e)}")

        logger.info(f"工作流 {self.name} 执行完成")
        return self.results

    def get_task_status(self, task_name: str) -> str:
        """获取任务状态"""
        return self.tasks.get(task_name, Task("unknown", lambda: None)).status

    def get_task_result(self, task_name: str) -> Any:
        """获取任务结果"""
        return self.tasks.get(task_name, Task("unknown", lambda: None)).result


