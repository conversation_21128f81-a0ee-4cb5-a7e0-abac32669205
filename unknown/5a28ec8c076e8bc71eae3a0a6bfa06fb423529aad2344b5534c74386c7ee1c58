#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：config.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/22 14:57 
@Desc    ：ier业务逻辑相关配置
"""
"""
law config
"""
LAW_TYPE_INSIDE = "inside"
LAW_TYPE_OUTSIDE = "outside"

"""
File Processing Logic
"""
# 法规文件OCR结果保存表
LAW_FILE_OCR_RESULT_TABLE = "ocr_read_data"
LAW_FILE_OCR_RESULT_TABLE_REQUEST_ID = "request_id"
LAW_FILE_OCR_RESULT_TABLE_FILE_ID = "file_id"
LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS = "ocr_status"
LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH = "ocr_result_path"
LAW_FILE_OCR_RESULT_TABLE_FILE_PATH = "file_path"

LAW_TABLE = "ier_dev_law"
LAW_FILES_TABLE = "ier_dev_law_files"
LAW_REGULATION_TABLE = "ier_dev_law_regulation"
LAW_TABLE_LAW_ID = "law_id"
LAW_TABLE_LAW_TYPE = "law_type"
LAW_TABLE_LAW_NAME = "law_name"
LAW_TABLE_LAW_NUMBER = "law_number"
LAW_TABLE_LAW_RELEASE_DATE = "law_release_date"
LAW_TABLE_LAW_SUMMARY = "law_summary"

LAW_FILES_TABLE_FILE_ID = "file_id"
LAW_FILES_TABLE_FILE_LAW_ID = "file_law_id"
LAW_FILES_TABLE_IS_MAIN = "is_main"
LAW_FILES_TABLE_FILE_PATH = "file_path"

LAW_DOC_RELATION_TABLE = "ier_dev_law_doc_rel"
LAW_DOC_RELATION_TABLE_LAW_ID = "law_id"
LAW_DOC_RELATION_TABLE_LAW_FILE_ID = "law_file_id"
LAW_DOC_RELATION_TABLE_DOC_ID = "doc_id"

LAW_RELATION_TABLE = "ier_dev_law_relations"
LAW_RELATION_TABLE_LAW_ID = "law_id"
LAW_RELATION_TABLE_LAW_ALIAS = "law_alias"
LAW_RELATION_TABLE_LAW_AUTHORITY = "law_authority"
LAW_RELATION_TABLE_LAW_YEAR = "law_year"
LAW_RELATION_TABLE_LAW_NUMBER = "law_number"
LAW_RELATION_TABLE_LAW_REPEALED = "is_repealed"
LAW_RELATION_TABLE_LAW_RELATED = "is_related"
LAW_RELATION_TABLE_UPDATE_LAW_ID = "update_law_id"
LAW_RELATION_TABLE_UPDATE_LAW_NAME = "update_law_name"

DOC_CHUNKS_TABLE = "doc_dev_chunks"
DOC_CHUNKS_TABLE_DOC_ID = "doc_id"
DOC_CHUNKS_TABLE_CHUNK_ID = "chunk_id"
DOC_CHUNKS_TABLE_CREATE_TIME = "created_time"

DOC_CHUNKS_INFO_TABLE = "doc_dev_chunks_info"
DOC_CHUNKS_INFO_TABLE_CHUNK_ID = "chunk_id"
DOC_CHUNKS_INFO_TABLE_INFO_VALUE = "info_value"
DOC_CHUNKS_INFO_TABLE_INFO_TYPE = "info_type"
IER_INFO_TYPE_CONTENT = "content"

CHATBOT_RESULT_TABLE = "ier_dev_chatbot_records"
CHATBOT_RESULT_TABLE_USER_QUERY = "user_query"
CHATBOT_RESULT_TABLE_REQUEST_ID = "request_id"
CHATBOT_RESULT_TABLE_USER_ID = "user_id"
CHATBOT_RESULT_TABLE_SESSION_ID = "session_id"
CHATBOT_RESULT_TABLE_ANSWER = "answer"
CHATBOT_RESULT_TABLE_CREATED_TIME = "created_time"
CHATBOT_HISTORY_NUMBER = 3






# OCR结果保存路径
OCR_RESULT_SAVE_DIR = r"/data/ideal/code/qns_code/ier/app/ier/result/ocr_result"
OCR_RESULT_SAVE_TYPE = "md"

"""
client config
"""
RDB_CLIENT_CONFIG = "database.rdbs.mysql"
VDB_CLIENT_CONFIG = "database.vdbs.pgvector"
LLM_CLIENT_CONFIG = "model.llms.opentrek"
EMBEDDING_CLIENT_CONFIG = "model.embeddings.moka-m3e-base"

"""
knowledge config
"""
KNOWLEDGE_ID = "core-business-kb"

"""
关键词匹配配置
"""
REPEALED_KEYWORD = ['废止', '废除']
RELATED_KEYWORD = ['根据', '相关']
VS_COLLECTION_NAME = "ier_dev_law_name_embeddings"
