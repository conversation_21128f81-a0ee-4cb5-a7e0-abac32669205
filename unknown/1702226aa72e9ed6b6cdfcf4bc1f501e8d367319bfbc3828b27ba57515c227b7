import logging
import os
from pathlib import Path
from typing import List, Tuple


from app.dd_one.doc_parse.parses.workflow_djz.base import Workflow, Task
from app.dd_one.doc_parse.parses.file_ops.pdf_ops import (get_pdf_dir, split_pdf,
                                               extract_main_content, extract_toc_items,
                                               task_extract_form_number)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GCPdfSplitWorkflow(Workflow):
    """大集中PDF拆分工作流"""

    def __init__(self, input_path: str, output_dir: str = None):
        super().__init__("大集中PDF拆分工作流")
        self.input_path = input_path
        # 处理输出目录
        if output_dir:
            self.output_dir = output_dir
        else:
            # 自动生成输出目录：输入文件所在目录 + 文件名 + "temp"
            input_file = Path(input_path)
            self.output_dir = os.path.join(
                input_file.parent,  # 输入文件所在目录
                f"{input_file.stem}_temp"  # 文件名（不含扩展名）+ "_temp"
            )

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"输出目录: {self.output_dir}")
        self._setup_tasks()

    def _setup_tasks(self):
        """设置工作流任务"""
        # 任务1：获取PDF目录页码范围
        self.add_task(Task(
            name="get_pdf_dir",
            function=get_pdf_dir,
            params={"input_path": self.input_path}
        ))

        # 任务2：拆分出目录部分
        self.add_task(Task(
            name="split_dir_pdf",
            function=split_pdf,
            dependencies=["get_pdf_dir"],
            params={
                "input_path": self.input_path,
                "output_prefix": "dir_pdf",
                "page_ranges": lambda results: [(results["get_pdf_dir"]['dir_page_s'], results["get_pdf_dir"]['dir_page_e'])],
                "o_path": self.output_dir
            }
        ))

        # 任务3：提取目录文本
        self.add_task(Task(
            name="extract_dir_text",
            function=extract_main_content,
            dependencies=["split_dir_pdf"],
            params={"pdf_path": lambda results: results['split_dir_pdf'][0]}
        ))

        # 任务4：解析目录项
        self.add_task(Task(
            name="parse_toc",
            function=extract_toc_items,
            dependencies=["extract_dir_text"],
            params={"toc_text": lambda results: results["extract_dir_text"]}
        ))

        # 任务5：生成拆分列表
        self.add_task(Task(
            name="generate_split_list",
            function=self._generate_split_list,
            dependencies=["parse_toc", "get_pdf_dir"],
            params={
                "dir_ext_result": lambda results: results["parse_toc"],
                "dir_e_page": lambda results: results["get_pdf_dir"]['dir_page_e']
            }
        ))

        # 任务6：按目录拆分PDF
        self.add_task(Task(
            name="split_pdf_by_toc",
            function=split_pdf,
            dependencies=["generate_split_list"],
            params={
                "input_path": self.input_path,
                "output_prefix": "output",
                "page_ranges": lambda results: results["generate_split_list"],
                "o_path": self.output_dir
            }
        ))

        # 任务7: 根据拆分的子文件提取各自的表单号
        self.add_task(Task(
            name="task_extract_form_number",
            function=task_extract_form_number,
            dependencies=["split_pdf_by_toc"],
            params={
                "file_parts": lambda results: results["split_pdf_by_toc"],
            }
        ))

    def _generate_split_list(self, dir_ext_result: List[Tuple[str, int]], dir_e_page: int) -> List[Tuple[int, int]]:
        """生成拆分列表（闭包函数，用于任务5）"""
        split_list = []
        for idx, (title, page) in enumerate(dir_ext_result):
            if idx < (len(dir_ext_result) - 1):
                split_list.append((page + dir_e_page, int(dir_ext_result[idx + 1][1]) - 1 + dir_e_page))
            else:
                split_list.append((page + dir_e_page, -1))
        return split_list

if __name__ == '__main__':
    # 创建并执行工作流
    workflow = GCPdfSplitWorkflow(
        input_path="D:/project/work/hsbc/文件解析/code/src/file/gc/doc/金融统计制度汇编（2023版）印刷.pdf",
        output_dir=None
    )

    results = workflow.execute()

    # 打印结果
    print("\n工作流执行结果:")
    for task_name, result in results.items():
        print(f"{task_name}: {result}")