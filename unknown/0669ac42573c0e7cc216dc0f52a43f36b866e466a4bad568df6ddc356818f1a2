#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：file_process_logic.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/22 14:11 
@Desc    ：文件处理逻辑
"""
import asyncio
from datetime import datetime
import json
import os
import re
import time
from typing import Any, List, Tuple, AsyncGenerator, Optional, Dict, DefaultDict
from collections import defaultdict
import uuid

from loguru import logger

from app.ier.config.config import (KNOWLEDGE_ID,
                                   LAW_FILE_OCR_RESULT_TABLE,
                                   OCR_RESULT_SAVE_DIR,
                                   OCR_RESULT_SAVE_TYPE,
                                   LAW_FILE_OCR_RESULT_TABLE_REQUEST_ID,
                                   LAW_FILE_OCR_RESULT_TABLE_FILE_ID,
                                   LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS,
                                   LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH,
                                   LAW_FILE_OCR_RESULT_TABLE_FILE_PATH,
                                   LAW_TABLE, LAW_TABLE_LAW_ID, LAW_TABLE_LAW_NAME, LAW_TABLE_LAW_TYPE, LAW_TABLE_LAW_NUMBER, LAW_TABLE_LAW_RELEASE_DATE, LAW_TABLE_LAW_SUMMARY,
                                   LAW_FILES_TABLE,LAW_FILES_TABLE_FILE_ID, LAW_FILES_TABLE_FILE_LAW_ID, LAW_FILES_TABLE_IS_MAIN, LAW_FILES_TABLE_FILE_PATH,
                                   LAW_REGULATION_TABLE,
                                   LAW_DOC_RELATION_TABLE, LAW_DOC_RELATION_TABLE_LAW_ID, LAW_DOC_RELATION_TABLE_LAW_FILE_ID, LAW_DOC_RELATION_TABLE_DOC_ID,
                                   DOC_CHUNKS_TABLE, DOC_CHUNKS_TABLE_DOC_ID, DOC_CHUNKS_TABLE_CHUNK_ID,
                                   DOC_CHUNKS_INFO_TABLE, DOC_CHUNKS_INFO_TABLE_CHUNK_ID, DOC_CHUNKS_INFO_TABLE_INFO_VALUE,DOC_CHUNKS_INFO_TABLE_INFO_TYPE, IER_INFO_TYPE_CONTENT,
                                   RELATED_KEYWORD,
                                   REPEALED_KEYWORD,
                                   LAW_TYPE_INSIDE,LAW_TYPE_OUTSIDE,
                                   VS_COLLECTION_NAME,
                                   LAW_RELATION_TABLE,LAW_RELATION_TABLE_UPDATE_LAW_ID,
                                   LAW_RELATION_TABLE_LAW_ID, LAW_RELATION_TABLE_LAW_ALIAS,
                                   LAW_RELATION_TABLE_LAW_AUTHORITY, LAW_RELATION_TABLE_LAW_YEAR,
                                   LAW_RELATION_TABLE_LAW_NUMBER, LAW_RELATION_TABLE_LAW_REPEALED,
                                   LAW_RELATION_TABLE_LAW_RELATED
                                   )
from app.ier.services.deepdoc.parser import DocxParser, converter
from app.ier.services.ier.exceptions import create_ocr_result_save_error
from app.ier.services.ier.model.ier_file_process_model import (UploadLawRequestModel,
                                                               UploadLawFileResult,
                                                               OcrStatusEnum,
                                                               QueryLawFileOcrStatusRequestModel,
                                                               QueryLawFileOcrStatusResult,
                                                               IsMainLawFile,
                                                               LawParseRequestModel,
                                                               LawModel,
                                                               FileInfo,
                                                               LawMatchModel, LawKnowledgeDomainModel)
from app.ier.services.ier.prompts import (extract_json_from_response,
                                          law_info_extraction_prompt,
                                          is_main_file_prompt,
                                          law_info_classification_prompt,
                                          law_name_extract_prompt,
                                          law_match_reason_prompt)
from app.ier.utils.file_utils import (get_file_extension,
                                      get_file_name,
                                      detect_format,
                                      detect_document_type,
                                      convert_to_md,
                                      convert_tiff_to_pdf,
                                      convert_doc_to_docx,
                                      batch_chunks)
from base.model_serve.model_runtime.entities import PromptMessage
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from modules.knowledge.doc.operations.document_ops import DocumentOperation
from modules.knowledge.doc.entities.base_models import (
    DocumentStatus, ParseType, DocumentFormat, Chunk, ChunkInfo
)

class TextChunker:
    def __init__(self):
        # 定义分割模式的正则表达式及其优先级
        self.patterns = {
            'by_article': {
                'pattern': r'(^|\s)第[一二三四五六七八九十百千万]+条\s',
                'priority': 1
            },
            'by_qa': {
                'pattern': r'([^。？！\n]+[？?][^\n]*)',
                'priority': 2
            },
            'by_section': {
                'pattern': r'(^|\s)\d+\.\d+(?:\.\d+)*\s',
                'priority': 3
            },
            'by_clause': {
                'pattern': r'[一二三四五六七八九十]+、',
                'priority': 4
            },
            'by_chapter': {
                'pattern': r'(^|\s)第[一二三四五六七八九十百千万]+章\s',
                'priority': 5
            },
            'by_numbered_clause': {
                'pattern': r'[一二三四五六七八九十]+是',
                'priority': 6
            },


        }
        # 页码模式的正则表达式（如 - 4 -）
        self.page_pattern = r'-\s*\d+\s*-'
        # 引用条款的正则表达式，用于清理
        self.reference_pattern = r'(引用|参见|根据|见)第[一二三四五六七八九十百千万]+条'

    def clean_text(self, text: str) -> str:
        """
        清理文本：删除页码标记、标记引用条款、换行符和多余空格
        :param text: 输入文本
        :return: 清理后的文本
        """
        # 将引用条款中的“第X条”替换为标记，避免分割
        text = re.sub(self.reference_pattern, lambda m: f"{m.group(1)}REF_ARTICLE_{m.group(0)[len(m.group(1)):]}", text)
        # 删除页码标记
        text = re.sub(self.page_pattern, '', text)
        # 删除换行符
        text = text.replace('\n', ' ')
        # 将多个空格替换为单个空格并去除首尾空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    @staticmethod
    def restore_references(text: str) -> str:
        """
        恢复被标记的引用条款
        :param text: 包含标记的文本
        :return: 恢复后的文本
        """
        return re.sub(r'(引用|参见|根据|见)REF_ARTICLE_第[一二三四五六七八九十百千万]+条',
                      lambda m: m.group(0).replace('REF_ARTICLE_', ''), text)

    def detect_best_method(self, text: str) -> str:
        """
        自动检测最适合的分割模式
        :param text: 输入文本
        :return: 最合适的分割方式名称
        """
        best_method = None
        max_matches = -1
        highest_priority = float('inf')

        # 在检测前清理文本以提高匹配准确性
        cleaned_text = self.clean_text(text)
        for method, config in self.patterns.items():
            pattern = config['pattern']
            priority = config['priority']
            matches = len(list(re.finditer(pattern, cleaned_text)))

            # 如果匹配次数更多，或者匹配次数相同但优先级更高，更新最佳方法
            if matches > max_matches or (matches == max_matches and priority < highest_priority):
                max_matches = matches
                highest_priority = priority
                best_method = method

        # 如果没有匹配到任何模式，默认返回 'by_qa'
        return best_method if best_method else 'by_qa'

    def chunk_text(self, text: str, method: Optional[str] = None) -> List[str]:
        """
        根据指定方法或自动检测分割文本
        :param text: 输入文本
        :param method: 分割方式 ('by_article', 'by_section', 'by_chapter', 'by_clause', 'by_numbered_clause', 'by_qa', None)
        :return: 分割后的文本块列表
        """
        # 清理文本
        cleaned_text = self.clean_text(text)

        # 如果未指定方法，自动检测
        if method is None:
            method = self.detect_best_method(text)

        if method not in self.patterns:
            raise ValueError(f"不支持的分割方式: {method}")

        pattern = self.patterns[method]['pattern']
        # 使用正则表达式查找所有匹配的起点
        matches = [(m.start(), m.group()) for m in re.finditer(pattern, cleaned_text)]

        if not matches:
            return [self.restore_references(cleaned_text)]  # 如果没有匹配，返回清理后的原文

        chunks = []
        for i, (start, marker) in enumerate(matches):
            # 确定当前块的结束位置（下一块的开始或文本末尾）
            end = matches[i + 1][0] if i + 1 < len(matches) else len(cleaned_text)
            # 提取块内容
            chunk = cleaned_text[start:end].strip()
            if chunk:
                # 恢复引用条款中的标记
                chunks.append(self.restore_references(chunk))

        return chunks

    def chunk_all_methods(self, text: str) -> dict:
        """
        使用所有支持的分割方式处理文本
        :param text: 输入文本
        :return: 包含所有分割方式结果的字典
        """
        results = {}
        for method in self.patterns:
            try:
                results[method] = self.chunk_text(text, method)
            except ValueError as e:
                results[method] = [str(e)]
        results['auto'] = self.chunk_text(text, None)
        return results


class LawFileProcessLogic:
    """法规文件处理核心逻辑类"""
    def __init__(self, rdb_client: Any, vdb_client: Any, llm_client: Any, embed_client: Any):
        """
        初始化法规文件处理逻辑

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            llm_client: 大模型客户端
            embed_client: embedding客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.llm_client = llm_client
        self.embed_client = embed_client
        self.converter = converter
        self.doc_ops = DocumentOperation()
        self.chunk_ops = ChunkOperation()
        self.knowledge_id = KNOWLEDGE_ID
        self.chunker = TextChunker()

    @staticmethod
    async def save_ocr_result(content:str, file_id: str) -> Tuple[bool, str]:
        try:
            create_timestamp = str(int(time.time()))
            os.makedirs(OCR_RESULT_SAVE_DIR, exist_ok=True)
            content_path = os.path.join(OCR_RESULT_SAVE_DIR,
                                        f"{file_id}_{create_timestamp}.{OCR_RESULT_SAVE_TYPE}")
            with open(content_path, encoding="utf-8", mode='a+') as f:
                f.write(content)
            return True, content_path
        except Exception as e:
            logger.error(f"save ocr result failure: {e}")
            raise create_ocr_result_save_error(file_id, str(e))

    async def upload_file(self, request: UploadLawRequestModel) -> None:
        request_id = request.request_id
        law_files = request.file_list
        logger.info(f"upload_file input param:{request.to_dict()}")
        law_file_process_results = []
        for law_file in law_files:
            law_file_process_result = UploadLawFileResult(
                request_id=request_id,
                file_id=str(law_file.file_id),
                file_path=law_file.file_path,
                ocr_result_path="",
                ocr_status=OcrStatusEnum.PROCESSING
            )
            law_file_process_results.append(law_file_process_result)

        insert_data = [law_file_process_result.to_dict() for law_file_process_result in law_file_process_results]
        logger.info(f"insert_data:{insert_data}")
        self.rdb_client.insert(
            {
                "table": LAW_FILE_OCR_RESULT_TABLE,
                "data": insert_data
            }
        )
        logger.info(f"Law File Start Processing, total file count:{len(law_file_process_results)}")

        for law_file_process_result in law_file_process_results:
            try:
                # 1.原文件提取内容
                content = await self.read_file(law_file_process_result.file_path)
                logger.info(f"law file process success, file id:{law_file_process_result.file_id}.")
                # 2.将解析内容保存为markdown格式
                _, content_path = await self.save_ocr_result(content, law_file_process_result.file_id)

                # 3.更新ocr_read_data的 ocr_result_path和ocr_status 字段
                self.rdb_client.update(
                    {
                        "table": LAW_FILE_OCR_RESULT_TABLE,
                        "data": {
                            LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH:content_path,
                            LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS: OcrStatusEnum.SUCCESS.value
                        },
                        "filters": {
                            LAW_FILE_OCR_RESULT_TABLE_REQUEST_ID:request_id,
                            LAW_FILE_OCR_RESULT_TABLE_FILE_ID:law_file_process_result.file_id
                        },
                    }
                )
                logger.info(f"read law file success, file path:{law_file_process_result.file_path}")

            except Exception as e:
                error_msg = f"read law file failure, file path:{law_file_process_result.file_path}, reason:{str(e)}"
                logger.error(error_msg)
                # 解释失败更新ocr_read_data的 ocr_result_path和ocr_status 字段
                self.rdb_client.update(
                    {
                        "table": LAW_FILE_OCR_RESULT_TABLE,
                        "data": {
                            LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH: "",
                            LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS: OcrStatusEnum.FAILURE.value
                        },
                        "filters": {
                            LAW_FILE_OCR_RESULT_TABLE_REQUEST_ID: request_id,
                            LAW_FILE_OCR_RESULT_TABLE_FILE_ID: law_file_process_result.file_id
                        },
                    }
                )

    async def query_file_status(self, request: QueryLawFileOcrStatusRequestModel) -> QueryLawFileOcrStatusResult:
        request_data = self.rdb_client.query({
            "table": LAW_FILE_OCR_RESULT_TABLE,
            "filters": {LAW_FILE_OCR_RESULT_TABLE_REQUEST_ID: request.request_id},
        }).data
        logger.info(request_data)
        if not request_data:
            result = QueryLawFileOcrStatusResult(
                percentage=100.0,
                message="请求失败，请检查网络连接是否有问题",
                ocr_status=OcrStatusEnum.FAILURE
            )
            return result
        else:
            doc_nums = len(request_data)
            i = 0
            message = ""
            ocr_status = OcrStatusEnum.PROCESSING
            for doc_data in request_data:
                if doc_data[LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS] == OcrStatusEnum.SUCCESS.value:
                    i += 1
                    message += f"{doc_data[LAW_FILE_OCR_RESULT_TABLE_FILE_PATH].split('/')[-1]}ocr识别成功\n"
                    ocr_status = OcrStatusEnum.SUCCESS
                elif doc_data[LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS] == OcrStatusEnum.FAILURE.value:
                    i += 1
                    message += f"{doc_data[LAW_FILE_OCR_RESULT_TABLE_FILE_PATH].split('/')[-1]}ocr识别失败\n"
                    ocr_status = OcrStatusEnum.FAILURE
                else:
                    message += f"{doc_data[LAW_FILE_OCR_RESULT_TABLE_FILE_PATH].split('/')[-1]}ocr正在识别中\n"
                    ocr_status = OcrStatusEnum.PROCESSING
        percentage = i / doc_nums * 100
        result = QueryLawFileOcrStatusResult(
            percentage=percentage,
            message=message,
            ocr_status=ocr_status
        )
        return result

    async def read_file(self, file_path: str) -> str:
        file_extension = get_file_extension(file_path)
        if file_extension in [".tiff", ".tif"]:
            output_pdf_path = os.path.splitext(file_path)[0] + "_output.pdf"
            convert_tiff_to_pdf(file_path, output_pdf_path)
            logger.info(f"Converted TIFF to PDF: {output_pdf_path}")
            return self.converter(output_pdf_path).markdown
        elif file_extension in [".doc", ".wps"]:
            output_pdf_path = os.path.splitext(file_path)[0] + "_output.pdf"
            convert_doc_to_docx(file_path, output_pdf_path)
            logger.info(f"Converted doc to docx: {output_pdf_path}")
            return self.converter(output_pdf_path).markdown
        elif file_extension == ".pdf":
            return self.converter(file_path).markdown
        elif file_extension == ".docx":
            secs, _ = DocxParser()(file_path)
            content = "\n".join(sec[0] for sec in secs)
            return convert_to_md(content)
        elif file_extension == ".txt":
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            return convert_to_md(content)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")

    @staticmethod
    def read_md_file(file_path: str) -> str:
        """
        读取指定路径的 md 文件内容，并以字符串形式返回。

        :param file_path: str, .md 文件的完整路径
        :return: str, 文件内容
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件 {file_path} 不存在")

        if not file_path.lower().endswith('.md'):
            raise ValueError(f"文件 {file_path} 不是 .md 格式")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()  # 一次性读取全部内容
        return content

    async def get_ocr_result(self, file_id: str) -> str:
        """
        根据法规文件id来获取文件中的内容
        1.从ocr结果中获取
        2.重新进行ocr获取

        :param file_id: 法规文件id：
        :return: 法规文件内容
        """
        content = ""
        law_file_ocr_result_path = self.rdb_client.query({
            "table": LAW_FILE_OCR_RESULT_TABLE,
            "filters": {LAW_FILE_OCR_RESULT_TABLE_FILE_ID: file_id},
            "columns":[LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH, LAW_FILE_OCR_RESULT_TABLE_FILE_PATH]
        }).data

        if len(law_file_ocr_result_path) < 1:
            return ''
        if law_file_ocr_result_path[0][LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH]:
            ocr_result_path = law_file_ocr_result_path[0][LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH]
            # 读取一个txt文件
            content = self.read_md_file(ocr_result_path)
            return content
        else:
            file_path = law_file_ocr_result_path[0][LAW_FILE_OCR_RESULT_TABLE_FILE_PATH]
            try:
                content = await self.read_file(file_path)
                _, content_path = await self.save_ocr_result(content, file_id)
                self.rdb_client.update(
                    {
                        "table": LAW_FILE_OCR_RESULT_TABLE,
                        "data": {
                            LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH: content_path,
                            LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS: OcrStatusEnum.SUCCESS.value
                        },
                        "filters": {
                            LAW_FILE_OCR_RESULT_TABLE_FILE_ID: file_id
                        },
                    }
                )
            except Exception as e:
                self.rdb_client.update(
                    {
                        "table": LAW_FILE_OCR_RESULT_TABLE,
                        "data": {
                            LAW_FILE_OCR_RESULT_TABLE_OCR_RESULT_PATH: "",
                            LAW_FILE_OCR_RESULT_TABLE_OCR_STATUS: OcrStatusEnum.FAILURE.value
                        },
                        "filters": {
                            LAW_FILE_OCR_RESULT_TABLE_FILE_ID: file_id
                        },
                    }
                )

            logger.error('传递的file_id不存在，再次执行ocr识别')
            return content

    @staticmethod
    def split_content(content, max_length=5000):
        """ Split content into chunks"""
        if len(content) <= max_length:
            return [content]

        parts = []
        start = 0
        while start < len(content):
            end = min(start + max_length, len(content))
            while end > start and content[end - 1] not in ['\n', '。']:
                end -= 1

            if end == start:
                end = min(start + max_length, len(content))

            parts.append(content[start:end])
            start = end

        return parts

    async def law_info_extraction(self, content):
        """
        使用大模型从法规内容中提取法规相关的信息

        :param content: 法规内容：
        :return: 法规相关信息
        """
        prompt = law_info_extraction_prompt.format(content=content)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }
        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False, model_parameters=model_parameters)
        law_info = llm_result.message.content
        logger.info(f"law info extraction llm result: {law_info}")
        law_info = extract_json_from_response(law_info)
        logger.info(f"law info extraction extract json from response: {law_info}")
        return law_info

    async def llm_judge_main_file(self,
                                  file_law_id: str,
                                  title: str,
                                  file_name: str,
                                  law_number: str
    ) -> IsMainLawFile:
        prompt = is_main_file_prompt.format(file_name=file_name, title=title, law_number=law_number)
        messages = [
            PromptMessage(role="system", content=prompt),
            PromptMessage(role="user", content="Output: "),
        ]
        model_parameters = {
            "max_tokens": 500,
            "temperature": 0.7
        }
        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                   model_parameters=model_parameters)
        response_info = llm_result.message.content

        logger.info(f"judge main file llm result: {response_info}")
        is_main_result = extract_json_from_response(response_info)
        logger.info(f"judge main file extract json from response: {response_info}")

        main_label = is_main_result.get("main_label", False)
        return IsMainLawFile(
            file_id=file_law_id,
            main_label=main_label
        )

    async def is_main_file(self, law_file_list) -> List[IsMainLawFile]:
        """
        根据文件的标题、文件名、法规号来判断该文件是否为主文件。
        是否为附件由大模型根据文件名判断。

        :param law_file_list: 文件信息的字典列表，每个字典包含以下字段：
                         - file_id: 文件ID
                         - file_path: 文件路径
        :return: 包含文件ID和是否为主文件标志的字典列表
        """
        results = []
        # 如果只有一个文件，直接返回该文件是主文件
        if len(law_file_list) == 1:
            file_info = law_file_list[0]
            file_id = file_info.get("file_id")
            results.append(
                IsMainLawFile(file_id=file_id, main_label=True)
            )
            return results

        for file_info in law_file_list:
            file_id = file_info.get("file_id")
            file_path = file_info.get("file_path")
            try:
                # 读取文件内容
                # 1.文件读取
                try:
                    # 处理文件并打印结果
                    content = await self.get_ocr_result(file_id)
                except Exception as e:
                    logger.error(f"An error occurred while processing file {file_path}: {e}")
                    continue
                content = self.split_content(content)[0]
                # 3.内容解析标题，清洗 入库别名表
                law_info = await self.law_info_extraction(content)
                logger.info(law_info)
                title = law_info.get("law_title", "")
                law_number = law_info.get("law_number", "")
                file_name = os.path.basename(file_path)
                logger.info(f"title:{title}")
                logger.info(f"law_number:{law_number}")
                logger.info(f"file_name:{file_name}")

                # 调用 llm_judge_main_file 方法
                result = await self.llm_judge_main_file(file_id, title, file_name, law_number)
                results.append(result)

            except Exception as e:
                logger.error(f"解析文件 {file_path} 失败: {str(e)}")
                results.append(
                    IsMainLawFile(
                        file_id=file_id,
                        main_label=False
                    )
                )

        return results

    async def law_info_extraction_chunk(self, content):
        logger.info("开始分块进入大模型检索！")
        content_parts = self.split_content(content)
        logger.info(f"文件内容被切分为{len(content_parts)}个！")

        all_law_info = {}
        logger.info("进入大模型信息提取！")
        for part in content_parts:
            info = await self.law_info_extraction(part)
            if info:
                for key, value in info.items():
                    if value:
                        if key == "analyze_res":
                            if all_law_info.get(key):
                                all_law_info[key] += " " + value
                            else:
                                all_law_info[key] = value
                        else:
                            if not all_law_info.get(key):
                                all_law_info[key] = value
        logger.info(f"该文件最终提取内容为：{all_law_info}")

        return all_law_info

    async def law_info_classification(self, title: str, summary: str):
        prompt = law_info_classification_prompt.format(title=title, summary=summary)
        prompt_messages = [
            PromptMessage(role="system", content=prompt),
            PromptMessage(role="user", content="Output: "),
        ]

        model_parameters = {
            "max_tokens": 500,
            "temperature": 0.7
        }
        llm_result = await self.llm_client.ainvoke(prompt_messages=prompt_messages, stream=False,
                                                   model_parameters=model_parameters)
        llm_classification_result = llm_result.message.content
        logger.info(f"law info classification llm result: {llm_classification_result}")
        classification_result = extract_json_from_response(llm_classification_result)
        logger.info(f"law info classification extract json from response: {classification_result}")

        return classification_result.get("categories", [])

    async def parse_law(self, request: LawParseRequestModel) ->  AsyncGenerator[str, Any]:
        request_id = str(uuid.uuid4())
        status = "started"
        messages = []
        logger.info(f"start parse law, input parameter:{request.model_dump()}")

        try:
            message_content = f"# step0 加载文件 \n## 现在我们将对文件{request.file_name}处理，请耐心等待。\n"
            # todo优化
            for i in range(0, len(message_content), 3):
                chunk = message_content[i:i + 3]
                partial_message = {
                    "request_id": request_id,
                    "status": status,
                    "message": chunk,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                yield f"data: {json.dumps({'content': partial_message})}\n\n"
                # time.sleep(0.1)  # 每次输出后暂停1秒
                await asyncio.sleep(0.2)
            await asyncio.sleep(0.2)

            content = await self.get_ocr_result(str(request.law_file_id))
            if not content:
                logger.error(f"An error occurred while loading the file content.")
                message_content = f"# 错误: 获取文件内容失败。\n"
                message = {
                    "request_id": request_id,
                    "status": "failed",
                    "message": message_content,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                yield f"data: {json.dumps({'content': message})}\n\n"
                return

            message_content = f"# step1 载入文件 \n##发现文件信息, 内容识别完毕。\n"
            message = {
                "request_id": request_id,
                "status": "Load file",
                "message": message_content,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            yield f"data: {json.dumps({'content': message})}\n\n"
            messages.append(message)
            status = "in_progress"

            # 内容解析标题，清洗入库别名表
            law_info = await self.law_info_extraction_chunk(content)
            if not law_info:
                logger.warning("No law information extracted.")
            logger.info(f"Extracted law info: {law_info}")

            title = law_info.get("law_title", "")
            law_number = law_info.get("law_number", "")
            agency = "" if request.file_type == 'inside' else law_info.get("agency", "")
            release_date = law_info.get("release_date", "")
            summary = law_info.get("analyze_res", "")

            law_type = []
            if request.file_type == 'outside':
                law_type = await self.law_info_classification(title=title, summary=summary)
            logger.info(f"law categories :{law_type}")

            message_content = f"# step2 信息提取 \n## 法规标题：{title}, 法规文号：{law_number}, 发布日期：{release_date}\n"
            # todo优化
            for i in range(0, len(message_content), 3):
                chunk = message_content[i:i + 3]
                partial_message = {
                    "request_id": request_id,
                    "status": "In information extraction",
                    "message": chunk,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                yield f"data: {json.dumps({'content': partial_message})}\n\n"
                await asyncio.sleep(0.2)

            message_content = f"# step3 法规总结 \n## 总结内容信息:{summary}\n"
            # todo优化
            for i in range(0, len(message_content), 3):
                chunk = message_content[i:i + 3]
                partial_message = {
                    "request_id": request_id,
                    "status": status,
                    "message": chunk,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                yield f"data: {json.dumps({'content': partial_message})}\n\n"
                # time.sleep(0.1)  # 每次输出后暂停1秒
                await asyncio.sleep(0.2)

            # 最终结果
            processed_result = {
                "law_title": title,
                "law_number": law_number,
                "agency": agency,
                "release_date": release_date,
                "analyze_res": summary,
                "law_type": law_type  # 法规分类(内规为空)
            }

            law_type_result = ""
            if processed_result.get("law_type"):
                law_type_result = processed_result.get("law_type")
            final_message_content = f"# step4 法规分类推荐 {law_type_result}\n"  # 大模型本身输出内容:{processed_result['analyze_res']}\n
            final_message = {
                "request_id": request_id,
                "status": "In category",
                "message": final_message_content,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            final_response = {
                "content": final_message,
                "result": processed_result
            }
            logger.info(f"Final response: {final_response}")
            yield f"data: {json.dumps(final_response)}\n\n"
            messages.append(final_message)


        except Exception as e:
            logger.error(f"文件{request.file_name}信息提取失败: {str(e)}")
            message_content = f"# 错误: 处理失败 ({str(e)})\n"
            message = {
                "request_id": request_id,
                "status": "failed",
                "message": message_content,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            yield f"data: {json.dumps({'content': message})}\n\n"

    async def _create_law_file_chunk_structure(self, doc_id: str, law_file: FileInfo) -> List[Chunk]:
        # 1. 读取法规文件内容，根据法规文件id
        law_file_content = await self.get_ocr_result(str(law_file.file_id))

        # 2. 法规文件内容处理
        # 法规内容切分结果
        content_parse_result = self.chunker.chunk_text(law_file_content, None) # 自动检测chunk切分方案

        # 3. 构建chunks并入库存储
        chunks_created = []
        for idx, law_file_chunk in enumerate(content_parse_result):
            chunk = {
                "chapter_layer": f"{get_file_name(law_file.file_path)}-{idx}", # {切分文件名}-{切块id}
                "parent_id": None,  # 顶级分块
                "chunk_infos": [
                    {
                        "info_type": "content",
                        "info_value": law_file_chunk
                    },
                ]
            }
            # chunk入库操作
            chunk_layer_id = await self.chunk_ops.create_chunk_with_info_and_vector(
                knowledge_id=self.knowledge_id,
                doc_id=doc_id,
                chapter_layer=chunk["chapter_layer"],
                parent_id=chunk["parent_id"],
                chunk_infos=chunk["chunk_infos"]
            )
            # 返回chunk实体
            chunk_entity = await self.chunk_ops.get_chunk_entity(chunk_layer_id)
            if chunk_entity:
                chunks_created.append(chunk_entity)

        return chunks_created

    async def save_law(self, law: LawModel):
        law_id = law.law_id
        law_name = law.law_name
        law_number = law.law_number
        law_type = law.law_type
        file_list = law.file_list
        law_release_date = law.law_release_date
        law_summary = law.law_summary

        result = self.rdb_client.query({
            "table": LAW_TABLE,
            "columns": ['law_name','law_type'],
            "filters": {"law_id": law_id},
        }).data
        if len(result) > 0:
            return

        # 1. 插入法规表信息
        law_insert_data = {
            LAW_TABLE_LAW_ID: law_id,
            LAW_TABLE_LAW_TYPE: law_type,
            LAW_TABLE_LAW_NAME: law_name,
            LAW_TABLE_LAW_NUMBER: law_number,
            LAW_TABLE_LAW_RELEASE_DATE: law_release_date,
            LAW_TABLE_LAW_SUMMARY: law_summary
        }
        self.rdb_client.insert(
            {
                "table": LAW_TABLE,
                "data": law_insert_data
            }
        )
        # 2. 插入法规文件表
        for law_file in file_list:
            law_file_insert_data = {
                LAW_FILES_TABLE_FILE_ID: law_file.file_id,
                LAW_FILES_TABLE_FILE_LAW_ID: law_file.file_law_id,
                LAW_FILES_TABLE_IS_MAIN: law_file.is_main,
                LAW_FILES_TABLE_FILE_PATH: law_file.file_path
            }
            self.rdb_client.insert(
                {
                    "table": LAW_FILES_TABLE,
                    "data": law_file_insert_data
                }
            )

    async def law_in_storage(self, request: LawModel):
        """
        法规入库
        """
        law_id = request.law_id
        law_type = request.law_type
        law_name = request.law_name
        law_number = request.law_number
        file_list = request.file_list

        try:
            await self.save_law(request)
            logger.info(f"save law info success,law info:{request.model_dump()}")
        except Exception as e:
            logger.error(f"save law info failure,law info:{request.model_dump()},error:{e}")

        """
        1.law_name 相似性查找
            匹配上：
                - 有law_id
                - 无law_id 设置向量库的law_id + 设置LAW_RELATION_TABLE表law_id 条件是law_name + law_number
        2.入库 
            入向量库 ier_dev_law_name_embeddings
        """
        res = await self.embed_client.ainvoke(texts=[law_name])
        query_vector = res.embeddings[0]

        search_results = self.vdb_client.search(
            collection_name=VS_COLLECTION_NAME,
            data=[query_vector],
            anns_field="embedding",
            param={"metric_type": "cosine"},
            limit=1,
            output_fields=["law_id", "law_name", "law_number"]
        )[0]
        logger.info(f"search_results:{search_results}")
        if len(search_results) > 0:
            for search_result in search_results:
                similarity_score = 1.0 - search_result.distance,  # 转换距离为相似度
                logger.info(f"search_result:{search_result}, similarity_score:{similarity_score}")
                if similarity_score[0] * 100 > 95:
                    extract_law_id = search_result.entity.data['law_id']
                    if not extract_law_id:
                        if search_result.entity.data['law_number']:
                            expr = f"law_name='{search_result.entity.data['law_name']}' and law_number='{search_result.entity.data['law_number']}'"
                        else:
                            expr = f"law_name='{search_result.entity.data['law_name']}'"
                        self.vdb_client.update(
                            VS_COLLECTION_NAME,
                            data={
                                "law_id": law_id
                            },
                            expr=expr
                        )

                        self.rdb_client.update(
                            {
                                "table":LAW_RELATION_TABLE,
                                "filters":{"law_alias": search_result.entity.data['law_name'],
                                        "law_number": search_result.entity.data['law_number']},
                                "data":{"law_id": law_id}
                            }
                        )

        query_result = self.vdb_client.query(
            VS_COLLECTION_NAME,
            expr=f"law_id='{law_id}'"
        )
        if len(query_result) < 1:
            # 法规名embedding表添加记录
            vdb_insert_data = [
                {
                    "law_id": law_id,
                    "law_name": law_name,
                    "law_number": law_number,
                    "embedding": query_vector
                }
            ]
            self.vdb_client.insert(VS_COLLECTION_NAME, vdb_insert_data)

        try:
            for law_file in file_list:
                law_file_id = law_file.file_id
                law_file_path = law_file.file_path

                law_file_id_query_result = self.rdb_client.query({
                    "table": LAW_DOC_RELATION_TABLE,
                    "columns": ['law_id', 'doc_id'],
                    "filters": {LAW_DOC_RELATION_TABLE_LAW_FILE_ID: law_file_id},
                }).data
                if len(law_file_id_query_result) > 1:
                    return

                # 1.创建文件
                logger.info("步骤1: 创建文档基础信息")
                document_data = DocumentCreate(
                    knowledge_id=self.knowledge_id,
                    doc_name=get_file_name(law_file_path),
                    doc_type=detect_document_type(law_type),
                    author=None,
                    vector_similarity_weight=0.8,
                    similarity_threshold=0.75,
                    parse_type=ParseType.AUTO,
                    status=DocumentStatus.PENDING,
                    parse_end_time=None,
                    parse_message=None,
                    doc_format=detect_format(law_file_path),
                    location=law_file_path,
                    metadata='{"category": "technical", "version": "v2.1"}',
                    created_time=None,
                    updated_time=None,
                    is_active=True
                )
                logger.info(document_data)
                doc_id = await self.doc_ops.create_document(document_data)
                logger.info(f"文档创建成功: {doc_id}")
                insert_data = {
                    LAW_DOC_RELATION_TABLE_LAW_ID: law_id,
                    LAW_DOC_RELATION_TABLE_LAW_FILE_ID: law_file_id,
                    LAW_DOC_RELATION_TABLE_DOC_ID: doc_id
                }
                self.rdb_client.insert(
                    {
                        "table": LAW_DOC_RELATION_TABLE,
                        "data": insert_data
                    }
                )

                # 2.开始解析文档
                await self.doc_ops.update_document_status(
                    doc_id, DocumentStatus.PROCESSING, "开始解析文档结构"
                )
                law_file_chunks = await self._create_law_file_chunk_structure(doc_id, law_file)

                # 3.完成文档处理
                logger.info("\n 完成文档处理")
                await self.doc_ops.update_document_status(
                    doc_id, DocumentStatus.COMPLETED,
                    f"解析完成，共生成{len(law_file_chunks)}个分块（层级化结构）"
                )

                # 4.更新文档统计信息
                await self.doc_ops.update_document_progress(
                    doc_id, chunk_nums=len(law_file_chunks), percentage=100.0
                )

                return doc_id, law_file_chunks

        except Exception as e:
            logger.error(f"文档入库流程失败: {e}")
        #     # todo 设计异常
        #     raise

    async def llm_extract_law_name(self, content: str) -> list:
        prompt = law_name_extract_prompt.format(related_keyword=RELATED_KEYWORD, repealed_keyword=REPEALED_KEYWORD, content=content)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }
        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                   model_parameters=model_parameters)
        extract_law_name = llm_result.message.content
        extract_law_name = extract_json_from_response(extract_law_name)
        if extract_law_name.get("result", {}):
            logger.info(extract_law_name)
            return extract_law_name['result']
        else:
            return []

    @staticmethod
    def merge_and_deduplicate(list1, list2):
        # 用元组作为键存储唯一字典（假设law_name和law_number组合唯一）
        unique_items = {}
        for item in list1 + list2:
            key = (item['law_name'], item['law_number'])
            unique_items[key] = item
        return list(unique_items.values())

    async def llm_extract_law_name_chunk(self, law: LawModel):
        data = [
            {
                "law_id": 1,
                "law_name": "商业银行合规风险管理指引",
                "law_number": "银监发〔2006〕76号",
                "embedding": [1.0] * 768
            }
        ]
        self.vdb_client.insert('ier_dev_law_name_embeddings', data)


        file_list = law.file_list
        result = []
        for law_file in file_list:
            content = await self.get_ocr_result(str(law_file.file_id))
            chunks = self.chunker.chunk_text(content, None)
            # 按每组10个或1000字符限制进行拼接
            batches = batch_chunks(chunks)
            for batch in batches:
                child_results = await self.llm_extract_law_name(batch)
                result = self.merge_and_deduplicate(result, child_results)
        logger.info(result)
        return result

    async def llm_law_match_reason(self, chunk1: str, chunk2: str) -> str:
        user_prompt = law_match_reason_prompt.format(chunk1=chunk1, chunk2=chunk2)

        prompt_messages = [
            PromptMessage(role="system",
                          content="你是一名法律条文分析助手，擅长用简短的语言描述两段法规间的关联性，用30-50字准确说明两段法规的匹配点，聚焦核心因素（如适用场景、约束对象等）。"),
            PromptMessage(role="user", content=user_prompt),
        ]
        model_parameters = {
            "max_tokens": 500,
            "temperature": 0.7
        }

        llm_result = await self.llm_client.ainvoke(prompt_messages=prompt_messages, stream=False,
                                                   model_parameters=model_parameters)
        reason = llm_result.message.content
        return reason

    @staticmethod
    def transform_relation_data(raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        将原始数据按in和out的law_id与law_file_id组合分组，转换为目标结构列表

        Args:
            raw_data: 原始关系列表数据

        Returns:
            按in/out标识组合分组后的目标结构列表
        """
        if not raw_data:
            raise ValueError("原始数据不能为空")

        # 按(in_law_id, in_law_file_id, out_law_id, out_law_file_id)组合分组
        grouped_data: DefaultDict[tuple, List[Dict[str, Any]]] = defaultdict(list)

        for item in raw_data:
            # 清洗原始数据中的键名（处理可能的下划线/空格错误）
            clean_item = {}
            for k, v in item.items():
                clean_key = k.strip().replace(" ", "").replace("__", "_")
                clean_item[clean_key] = v

            # 生成分组键（in和out的组合标识）
            group_key = (
                clean_item["in_law_id"],
                clean_item["in_law_file_id"],
                clean_item["out_law_id"],
                clean_item["out_law_file_id"]
            )
            grouped_data[group_key].append(clean_item)

        # 转换每组数据为目标结构
        result = []
        for group_key, items in grouped_data.items():
            # 取组内第一条数据的基础信息
            first_item = items[0]

            # 构建in部分
            in_info = {
                "law_file_id": str(first_item["in_law_file_id"]),
                "law_id": str(first_item["in_law_id"]),
                "law_type": first_item["in_law_type"]
            }

            # 构建out部分
            out_info = {
                "law_file_id": str(first_item["out_law_file_id"]),
                "law_id": str(first_item["out_law_id"]),
                "law_type": first_item["out_law_type"]
            }

            # 构建理由列表
            reason_list = []
            for item in items:
                reason_list.append({
                    "reason": item.get("reason", "").strip().replace("  ", " "),
                    "score": float(item.get("score", 0)),
                    "in_mapping_content": item.get("in_mapping_content", "").strip().replace("  ", " "),
                    "out_mapping_content": item.get("out_mapping_content", "").strip().replace("  ", " ")
                })

            # 添加到结果列表
            result.append({
                "in": in_info,
                "out": out_info,
                "reason_list": reason_list
            })

        return result

    async def match_file(self, match_law_file: LawMatchModel) ->  AsyncGenerator[str, Any]:
        law_id = match_law_file.law_id
        law_name = match_law_file.law_name
        law_type = match_law_file.law_type
        message = {
            "content": "# 开始进行关联性分析，请耐心等候："
                       "...\n"
        }
        yield f"data: {json.dumps(message)}\n\n"

        docs = self.rdb_client.query({
            "table": LAW_DOC_RELATION_TABLE,
            "columns": [LAW_DOC_RELATION_TABLE_DOC_ID, LAW_DOC_RELATION_TABLE_LAW_FILE_ID],
            "filters": {LAW_DOC_RELATION_TABLE_LAW_ID: law_id},
        }).data
        logger.info(f"law_id({law_id})相关的文档id列表:{docs}")

        # 步骤1:获取当前法规的chunks
        yield f"data: {json.dumps({"content": f"## 步骤1:获取当前法规的chunks\n"})}\n\n"
        logger.info(f"步骤1:获取当前法规的chunks")
        yield f"data: {json.dumps({"content": f"### 开始查询法规的chunks\n"})}\n\n"
        chunk_content_list = []
        law_file_id_list = [] # 提取引用法规 和 废止法规会用到
        for doc in docs:
            doc_id = doc[LAW_DOC_RELATION_TABLE_DOC_ID]
            law_file_id = doc[LAW_DOC_RELATION_TABLE_LAW_FILE_ID]
            law_file_id_list.append(law_file_id)
            chunks = self.rdb_client.query({
                "table": DOC_CHUNKS_TABLE,
                "columns": [DOC_CHUNKS_TABLE_CHUNK_ID],
                "filters": {DOC_CHUNKS_TABLE_DOC_ID: doc_id},
            }).data
            for chunk in chunks:
                chunk_id = chunk[DOC_CHUNKS_TABLE_CHUNK_ID]
                chunk_infos = self.rdb_client.query({
                    "table": DOC_CHUNKS_INFO_TABLE,
                    "columns": [DOC_CHUNKS_INFO_TABLE_INFO_VALUE],
                    "filters": {
                        DOC_CHUNKS_INFO_TABLE_CHUNK_ID: chunk_id,
                        DOC_CHUNKS_INFO_TABLE_INFO_TYPE:IER_INFO_TYPE_CONTENT
                    },
                }).data
                for chunk_info in chunk_infos:
                    chunk_content_list.append({
                        "query_content": chunk_info[DOC_CHUNKS_INFO_TABLE_INFO_VALUE],
                        "law_file_id": law_file_id,
                        "chunk_id": chunk_id,
                        "law_id": law_id,
                        "doc_id": doc_id,
                        "law_type": law_type
                    })
        yield f"data: {json.dumps({"content": f"### 完成查询法规的chunks,一共查询到{len(chunk_content_list)}个chunk\n查询内容如下:[{chunk_content_list[:3]}]...\n"})}\n\n"

        # 步骤2:对法规的所有chunk进行语义相似性匹配
        logger.info(f"步骤2:对法规的所有chunk进行语义相似性匹配")
        yield f"data: {json.dumps({"content": f"## 步骤2:对法规的所有chunk进行语义相似性匹配\n"})}\n\n"
        semantic_match_results = []
        for i, query in enumerate(chunk_content_list, 1):
            # 执行语义搜索 (只搜索content类型的信息)
            in_law_file_id = query['law_file_id']
            in_chunk_id = query['chunk_id']
            in_law_id = query['law_id']
            in_doc_id = query['doc_id']
            in_content = query['query_content']
            in_law_type = query['law_type']

            search_results = await self.chunk_ops.search_similar_chunks(
                query_text=in_content,
                knowledge_id=self.knowledge_id,
                info_types=["content"],  # 只搜索内容类型
                top_k=5,
                similarity_threshold=0.05
            )

            if search_results:
                for j, result in enumerate(search_results, 1):
                    out_chunk_info_id = result['chunk_info_id']
                    out_chunk_id = result['chunk_id']
                    out_doc_id = result['doc_id']
                    similarity = result['similarity_score']
                    out_content = result['info_value']
                    document_info = await self.doc_ops.get_document_by_id(out_doc_id)
                    if document_info:
                        out_law_type = document_info.get('doc_type', None)
                    else:
                        out_law_type = None

                    if not out_law_type:
                        continue

                    if in_law_type == LAW_TYPE_INSIDE and out_law_type == LAW_TYPE_INSIDE:
                        continue

                    if (in_chunk_id == out_chunk_id) or (in_doc_id == out_doc_id) or (similarity==1.0):
                        continue

                    law_doc_rel_result = self.rdb_client.query({
                        "table": LAW_DOC_RELATION_TABLE,
                        "columns":[LAW_DOC_RELATION_TABLE_LAW_ID, LAW_DOC_RELATION_TABLE_LAW_FILE_ID],
                        "filters": {LAW_DOC_RELATION_TABLE_DOC_ID: out_doc_id},
                    }).data[0]
                    out_law_file_id = law_doc_rel_result[LAW_DOC_RELATION_TABLE_LAW_FILE_ID]
                    out_law_id = law_doc_rel_result[LAW_DOC_RELATION_TABLE_LAW_ID]
                    match_result = {
                        "in_law_file_id": in_law_file_id,
                        "out_law_file_id": out_law_file_id,
                        "in_law_id": in_law_id,
                        "out_law_id": out_law_id,
                        "in_law_type": in_law_type,
                        "out_law_type": out_law_type,
                        "in_mapping_content": in_content,
                        "out_mapping_content": out_content,
                        "score": similarity * 100,
                        "reason": await self.llm_law_match_reason(in_content, out_content)
                    }
                    semantic_match_results.append(match_result)
        logger.info(f"match_results:{semantic_match_results}")

        # 步骤3:开始分析本法规涉及的 引用法规 和 废止法规
        yield f"data: {json.dumps({"content": f"## 步骤3:开始分析本法规涉及的*引用法规*和*废止法规*\n"})}\n\n"
        logger.info(f"步骤3:开始分析本法规涉及的 引用法规 和 废止法规")
        extract_law_result = []
        for law_file_id in law_file_id_list:
            content = await self.get_ocr_result(str(law_file_id))
            chunks = self.chunker.chunk_text(content, None)
            # 按每组10个或1000字符限制进行拼接
            batches = batch_chunks(chunks)
            for batch in batches:
                child_results = await self.llm_extract_law_name(batch)
                extract_law_result = self.merge_and_deduplicate(extract_law_result, child_results)
        logger.info(f"extract_law_result:{extract_law_result}")

        law_related_query_result = self.rdb_client.query({
            "table": LAW_RELATION_TABLE,
            "columns": ['law_alias', 'law_id', 'law_number'],
            "filters": {"update_law_id": law_id},
        }).data
        # 这个法规没有进行过检测，就进行检测
        if len(law_related_query_result) < 1:
            for extract_law_info in extract_law_result:
                extract_law_id = None
                law_alias = extract_law_info.get('law_name', None)
                law_number = extract_law_info.get('law_number', None)
                law_year = extract_law_info.get('law_year', None)
                law_authority = extract_law_info.get('law_authority', None)
                law_match_type = extract_law_info.get('law_match_type', None)
                if not law_alias:
                    continue

                res = await self.embed_client.ainvoke(texts=[law_alias])
                query_vector = res.embeddings[0]
                logger.info(f"law_alias:{law_alias}")
                logger.info(f"query_vector:{query_vector[:10]}")

                # 查询law_id
                ## 法规名embedding中是否有和别名相同的文件
                query_result = self.vdb_client.query(
                    collection_name=VS_COLLECTION_NAME,
                    expr=f"law_name='{law_alias}'",
                    output_fields=["law_id", "law_name", "law_number"],
                    limit=1,
                    offset=0  # 从第0条记录开始（分页）
                )
                if len(query_result) > 0:
                    extract_law_id = query_result[0]['law_id']
                else:
                    # 法规名相似性匹配
                    search_results = self.vdb_client.search(
                        collection_name=VS_COLLECTION_NAME,
                        data=[query_vector],
                        anns_field="embedding",
                        param={"metric_type": "COSINE"},
                        limit=1,
                        output_fields=["law_id", "law_name", "law_number"]
                    )[0]
                    logger.info(f"search_results:{search_results}")
                    if len(search_results) > 0:
                        for search_result in search_results:
                            logger.info(f"search_result:{search_result}")
                            similarity_score = 1.0 - search_result.distance,  # 转换距离为相似度
                            if similarity_score[0] * 100 > 95:
                                extract_law_id = search_result.entity.data['law_id']

                # 法规关系表添加记录
                rdb_insert_data = {
                    "law_id": extract_law_id,
                    "law_alias": law_alias,
                    "law_authority": law_authority,
                    "law_year": law_year,
                    "law_number": law_number,
                    "is_repealed": 1 if law_match_type == "2" else 0,
                    "is_related": 1 if law_match_type == "1" else 0,
                    "update_law_id": law_id,
                    "update_law_name": law_name,
                }
                self.rdb_client.insert({
                    "table": LAW_RELATION_TABLE,
                    "data": rdb_insert_data
                })

                # 法规名embedding表添加记录
                vdb_insert_data = [
                    {
                        "law_id": extract_law_id,
                        "law_name": law_alias,
                        "law_number": law_number,
                        "embedding": query_vector
                    }
                ]
                self.vdb_client.insert(VS_COLLECTION_NAME, vdb_insert_data)

        repealed_law = self.rdb_client.query({
            "table": LAW_RELATION_TABLE,
            "columns": ['law_alias', 'law_id', 'law_number'],
            "filters": {"update_law_id": law_id, "is_repealed": 1},
        }).data

        related_law = self.rdb_client.query({
            "table": LAW_RELATION_TABLE,
            "columns": ['law_alias', 'law_id', 'law_number'],
            "filters": {"update_law_id": law_id, "is_related": 1},
        }).data

        # 步骤4:开始合并结果
        """
        repealed_law
        related_law
        semantic_match_results
        """
        logger.info(f"repealed_law:{repealed_law}")
        logger.info(f"related_law:{related_law}")
        for rl in related_law:
            if rl.get("law_id", None):
                result = self.rdb_client.query({
                    "table": LAW_TABLE,
                    "columns": ['law_type'],
                    "filters": {"law_id": rl['law_id']},
                }).data
                if len(result) > 0:
                    out_law_type = result[0]['law_type']
                else:
                    out_law_type = ""
                match_result = {
                    "in_law_file_id": "",
                    "out_law_file_id": "",
                    "in_law_id": law_id,
                    "out_law_id": rl['law_id'],
                    "in_law_type": law_type,
                    "out_law_type": out_law_type,
                    "in_mapping_content": "",
                    "out_mapping_content": "",
                    "score": 100,
                    "reason": f"《{law_name}》的制定参考或引用《{rl.get("law_alias", "")}》"
                }
                semantic_match_results.append(match_result)

        logger.info(f"semantic_match_results:{self.transform_relation_data(semantic_match_results)}")
        result = self.transform_relation_data(semantic_match_results)
        law_out_list = []
        for rl in repealed_law:
            if rl.get("law_id", None):
                law_out_list.append(rl)

        for r in result:
            logger.info(r)

        yield f"data: {json.dumps({
            "content": f"## 步骤4:合并结果完成\n",
            "result": result,
            "law_out_list": law_out_list
        })}\n\n"

    async def get_law_knowledge_domain(self, law_info: LawKnowledgeDomainModel):
        repealed_law = []
        related_law = []
        try:
            knowledge_domain_info_list = self.rdb_client.query({
                "table": LAW_RELATION_TABLE,
                "filters": {
                    LAW_RELATION_TABLE_UPDATE_LAW_ID: law_info.law_id
                },
                "columns": [LAW_RELATION_TABLE_LAW_ID, LAW_RELATION_TABLE_LAW_ALIAS, LAW_RELATION_TABLE_LAW_AUTHORITY, LAW_RELATION_TABLE_LAW_YEAR, LAW_RELATION_TABLE_LAW_NUMBER, LAW_RELATION_TABLE_LAW_REPEALED, LAW_RELATION_TABLE_LAW_RELATED],
            }).data

            logger.info(f"query chatbot_history:{knowledge_domain_info_list}.")
            for knowledge_domain_info in knowledge_domain_info_list:
                law_type = ""
                law_id = knowledge_domain_info.get(LAW_RELATION_TABLE_LAW_ID, 0)
                if law_id:
                    law_type_result = self.rdb_client.query({
                        "table": LAW_TABLE,
                        "filters": {
                            LAW_TABLE_LAW_ID: law_id
                        },
                        "columns": [LAW_TABLE_LAW_TYPE],
                    }).data
                    if len(law_type_result) > 0:
                        law_type = law_type_result[0].get(LAW_TABLE_LAW_TYPE, "")

                is_repealed = knowledge_domain_info.get(LAW_RELATION_TABLE_LAW_REPEALED, 0)
                is_related = knowledge_domain_info.get(LAW_RELATION_TABLE_LAW_RELATED, 0)
                knowledge_domain_info[LAW_TABLE_LAW_TYPE] = law_type
                if is_repealed:
                    repealed_law.append(knowledge_domain_info)

                if is_related:
                    related_law.append(knowledge_domain_info)

            return {
                "repealed": repealed_law,
                "related": related_law
            }

            logger.info(f"knowledge domain query success, law_info param:{law_info.model_dump()}.")
        except Exception as e:
            logger.error(f"knowledge domain query failure, law_info param:{law_info.model_dump()}, error:{e}")
            return {
                "repealed": repealed_law,
                "related": related_law
            }









