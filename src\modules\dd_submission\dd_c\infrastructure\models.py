"""
DD-B模块数据模型

定义DD提交流程下一步处理的请求、响应和业务实体模型
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class ProcessingStatusEnum(str, Enum):
    """处理状态枚举"""
    SUCCESS = "success"
    PARTIAL_FILLED = "partial_filled"
    FAILED = "failed"
    VALIDATION_ERROR = "validation_error"


class FieldStatusEnum(str, Enum):
    """字段状态枚举"""
    ORIGINAL = "original"      # 原始值
    FILLED = "filled"          # 已填充
    EMPTY = "empty"           # 空值


class GenerationStrategyEnum(str, Enum):
    """数据生成策略枚举"""
    HIGH_CONFIDENCE = "high_confidence"      # 高置信度：完全使用历史数据
    LOW_CONFIDENCE = "low_confidence"        # 低置信度：需要重新生成
    DIRECT_RETURN = "direct_return"          # 直接返回：主要字段完整


@dataclass
class DDBProcessRequest:
    """DD-B处理请求模型"""
    
    # 必填字段
    report_code: str                        # 报表代码（现在直接是version）
    dept_id: str                           # 部门ID
    
    # 可选字段
    knowledge_id: Optional[str] = None      # 知识库ID
    version: Optional[str] = None           # 版本号（用于兼容性）
    
    # 处理配置
    enable_auto_fill: bool = True           # 启用自动填充
    validate_before_fill: bool = True       # 填充前验证
    return_original_data: bool = False      # 是否返回原始数据
    
    def validate(self) -> None:
        """验证请求参数"""
        if not self.report_code or not self.report_code.strip():
            raise ValueError("report_code不能为空")
        if not self.dept_id or not self.dept_id.strip():
            raise ValueError("dept_id不能为空")


@dataclass
class DDBRecord:
    """DD-B记录模型"""

    # 主键字段
    id: Optional[int] = None

    # 查询条件字段
    report_code: Optional[str] = None
    dept_id: Optional[str] = None

    # 历史信息关联字段（新增）
    pre_distribution_id: Optional[str] = None
    dr01: Optional[str] = None  # 统一使用小写

    # 主要检查字段（统一使用小写）
    bdr09: Optional[str] = None
    bdr10: Optional[str] = None
    bdr11: Optional[str] = None
    bdr16: Optional[str] = None

    # 需要填充的字段组1（统一使用小写）
    bdr05: Optional[str] = None
    bdr06: Optional[str] = None
    bdr07: Optional[str] = None
    bdr08: Optional[str] = None

    # 需要填充的字段组2（统一使用小写）
    bdr12: Optional[str] = None
    bdr13: Optional[str] = None
    bdr14: Optional[str] = None
    bdr15: Optional[str] = None
    bdr17: Optional[str] = None

    # SDR字段（统一使用小写）
    sdr01: Optional[str] = None
    sdr02: Optional[str] = None
    sdr03: Optional[str] = None
    sdr04: Optional[str] = None
    sdr05: Optional[str] = None
    sdr06: Optional[str] = None
    sdr07: Optional[str] = None
    sdr08: Optional[str] = None
    sdr09: Optional[str] = None
    sdr10: Optional[str] = None
    sdr11: Optional[str] = None
    sdr12: Optional[str] = None
    sdr13: Optional[str] = None
    sdr14: Optional[str] = None
    sdr15: Optional[str] = None

    # 元数据字段
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def is_main_fields_complete(self) -> bool:
        """检查主要字段是否完整"""
        return all([
            self.bdr09 and self.bdr09.strip(),
            self.bdr10 and self.bdr10.strip(),
            self.bdr11 and self.bdr11.strip(),
            self.bdr16 and self.bdr16.strip()
        ])
    
    def get_empty_group1_fields(self) -> List[str]:
        """获取空的第一组字段"""
        empty_fields = []
        if not self.bdr06 or not self.bdr06.strip():
            empty_fields.append('bdr06')
        if not self.bdr07 or not self.bdr07.strip():
            empty_fields.append('bdr07')
        if not self.bdr08 or not self.bdr08.strip():
            empty_fields.append('bdr08')
        return empty_fields
    
    def get_empty_group2_fields(self) -> List[str]:
        """获取空的第二组字段"""
        empty_fields = []
        for field in ['bdr12', 'bdr13', 'bdr14', 'bdr15', 'bdr17']:
            value = getattr(self, field)
            if not value or not value.strip():
                empty_fields.append(field)
        return empty_fields

    def get_all_bdr_fields(self) -> List[str]:
        """获取所有BDR字段列表"""
        return ['bdr05', 'bdr06', 'bdr07', 'bdr08', 'bdr09', 'bdr10', 'bdr11', 'bdr12', 'bdr13', 'bdr14', 'bdr15', 'bdr16', 'bdr17']

    def get_main_generation_fields(self) -> List[str]:
        """获取需要重新生成的主要字段"""
        return ['bdr09', 'bdr10', 'bdr11', 'bdr16']


@dataclass
class FieldFillInfo:
    """字段填充信息"""
    field_name: str
    original_value: Optional[str]
    filled_value: str
    status: FieldStatusEnum
    fill_reason: str = ""


@dataclass
class HistorySearchResult:
    """历史搜索结果模型"""

    # 搜索基本信息
    query_dr09: str                           # 查询的dr09值
    query_dr17: str                           # 查询的dr17值
    search_time_ms: float                     # 搜索耗时

    # 搜索结果
    total_results: int                        # 总结果数
    best_match_score: float                   # 最佳匹配分数
    confidence_level: float                   # 置信度级别 (0-1)

    # 可选字段（有默认值）
    best_match_record: Optional[Dict[str, Any]] = None  # 最佳匹配记录
    all_results: List[Dict[str, Any]] = field(default_factory=list)  # 所有搜索结果
    is_high_confidence: bool = False          # 是否高置信度 (≥0.6)
    search_strategy: str = "vector"           # 搜索策略
    search_fields: List[str] = field(default_factory=lambda: ["dr09", "dr17"])  # 搜索字段
    search_notes: List[str] = field(default_factory=list)  # 搜索说明


@dataclass
class GenerationDecision:
    """生成决策结果模型"""

    # 决策基本信息
    record_id: Optional[int]                  # 记录ID
    strategy: GenerationStrategyEnum          # 生成策略
    confidence_score: float                   # 置信度分数

    # 字段处理决策
    fields_to_generate: List[str] = field(default_factory=list)  # 需要生成的字段
    fields_to_fill_from_history: List[str] = field(default_factory=list)  # 从历史数据填充的字段
    fields_to_return_directly: List[str] = field(default_factory=list)  # 直接返回的字段

    # 历史数据信息
    history_search_result: Optional[HistorySearchResult] = None  # 历史搜索结果
    best_match_data: Optional[Dict[str, Any]] = None  # 最佳匹配的数据

    # 决策元数据
    decision_time_ms: float = 0.0             # 决策耗时
    decision_notes: List[str] = field(default_factory=list)  # 决策说明

    def get_decision_summary(self) -> Dict[str, Any]:
        """获取决策摘要"""
        return {
            "record_id": self.record_id,
            "strategy": self.strategy.value,
            "confidence_score": self.confidence_score,
            "fields_summary": {
                "to_generate": len(self.fields_to_generate),
                "from_history": len(self.fields_to_fill_from_history),
                "direct_return": len(self.fields_to_return_directly)
            },
            "has_history_data": self.best_match_data is not None,
            "decision_time_ms": self.decision_time_ms
        }


@dataclass
class DDBProcessResult:
    """DD-B处理结果模型"""
    
    request: DDBProcessRequest              # 原始请求
    
    # 查询结果
    total_records_found: int                # 找到的记录总数
    records_processed: int                  # 处理的记录数
    
    # 处理结果
    processed_records: List[DDBRecord]      # 处理后的记录
    original_records: List[DDBRecord] = field(default_factory=list)  # 原始记录（可选）
    
    # 填充统计
    records_with_complete_main_fields: int = 0    # 主要字段完整的记录数
    records_requiring_fill: int = 0               # 需要填充的记录数
    total_fields_filled: int = 0                  # 总填充字段数
    
    # 字段填充详情
    fill_details: List[FieldFillInfo] = field(default_factory=list)
    
    # 处理状态
    status: ProcessingStatusEnum = ProcessingStatusEnum.SUCCESS
    message: str = ""
    
    # 处理信息
    processing_time_ms: float = 0.0
    processing_notes: List[str] = field(default_factory=list)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        return {
            "request_info": {
                "report_code": self.request.report_code,
                "dept_id": self.request.dept_id
            },
            "query_results": {
                "total_found": self.total_records_found,
                "processed": self.records_processed
            },
            "processing_summary": {
                "complete_records": self.records_with_complete_main_fields,
                "filled_records": self.records_requiring_fill,
                "total_fields_filled": self.total_fields_filled,
                "status": self.status.value,
                "processing_time_ms": self.processing_time_ms
            },
            "fill_statistics": {
                "group1_fills": len([f for f in self.fill_details if f.field_name in ['brd06', 'brd07', 'brd08']]),
                "group2_fills": len([f for f in self.fill_details if f.field_name in ['BDR12', 'BDR13', 'BDR14', 'BDR15', 'brd17']])
            }
        }


@dataclass
class DDBValidationRequest:
    """DD-B验证请求模型"""

    report_code: str
    dept_id: str
    records: List[DDBRecord]

    # 验证配置
    strict_validation: bool = False         # 严格验证模式
    check_data_consistency: bool = True     # 检查数据一致性

    def validate(self) -> None:
        """验证请求参数"""
        if not self.report_code or not self.report_code.strip():
            raise ValueError("report_code不能为空")
        if not self.dept_id or not self.dept_id.strip():
            raise ValueError("dept_id不能为空")
        if not self.records:
            raise ValueError("records不能为空")


@dataclass
class DDBValidationResult:
    """DD-B验证结果模型"""

    request: DDBValidationRequest

    # 验证结果
    is_valid: bool
    validation_errors: List[str] = field(default_factory=list)
    validation_warnings: List[str] = field(default_factory=list)

    # 记录级验证结果
    valid_records: List[DDBRecord] = field(default_factory=list)
    invalid_records: List[DDBRecord] = field(default_factory=list)

    # 验证统计
    total_records: int = 0
    valid_count: int = 0
    invalid_count: int = 0

    # 验证信息
    validation_time_ms: float = 0.0
    validation_notes: List[str] = field(default_factory=list)

    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        return {
            "overall_result": {
                "is_valid": self.is_valid,
                "total_records": self.total_records,
                "valid_count": self.valid_count,
                "invalid_count": self.invalid_count,
                "validation_time_ms": self.validation_time_ms
            },
            "issues": {
                "errors": self.validation_errors,
                "warnings": self.validation_warnings
            },
            "details": {
                "validation_notes": self.validation_notes
            }
        }
