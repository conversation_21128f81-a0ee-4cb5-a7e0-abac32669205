"""
DD-C处理API路由

DD-C数据处理接口（异步模式）：
- 立即验证数据存在性并返回状态
- 后台异步处理数据并回调前端
- 包含SDR01-SDR15字段和index信息
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from service import get_client

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/dd-c",
    tags=["DD-C处理"],
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)


class DDCProcessRequest(BaseModel):
    """DD-C处理请求"""
    report_code: str
    dept_id: str


class DDCProcessResponse(BaseModel):
    """DD-C处理响应"""
    code: str
    msg: str


class DDCCallbackItem(BaseModel):
    """DD-C回调项目"""
    entry_id: str
    entry_type: str
    SDR01: str = ""
    SDR02: str = ""
    SDR03: str = ""
    SDR04: str = ""
    SDR05: str = ""
    SDR06: str = ""
    SDR07: str = ""
    SDR08: str = ""
    SDR09: str = ""
    SDR10: str = ""
    SDR11: str = ""
    SDR12: str = ""
    SDR13: str = ""
    SDR14: str = ""
    SDR15: str = ""
    index: Dict[str, Any] = {}


class DDCCallbackRequest(BaseModel):
    """DD-C回调请求"""
    report_code: str
    dept_id: str
    item: List[DDCCallbackItem]


async def get_database_clients():
    """获取数据库客户端依赖"""
    try:
        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        return {'rdb_client': rdb_client}

    except Exception as e:
        logger.error(f"数据库客户端获取失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"数据库连接失败: {str(e)}"
        )


async def validate_data_exists(rdb_client, report_code: str, dept_id: str) -> Dict[str, Any]:
    """验证数据是否存在（使用DD CRUD优化查询）"""
    try:
        # 使用DD CRUD进行数据验证
        from modules.knowledge.dd.crud import DDCrud
        from modules.dd_submission.dd_b.infrastructure.constants import DDBUtils

        dd_crud = DDCrud(rdb_client=rdb_client)

        # 构建查询条件（包含字段映射）
        conditions = DDBUtils.build_query_conditions(report_code, dept_id)
        logger.info(f"数据验证查询条件: {conditions}")

        # 使用批量查询方法验证数据存在性
        conditions_list = [conditions]
        records = await dd_crud.batch_query_post_distributions(
            conditions_list=conditions_list,
            batch_size=1,
            max_concurrency=1,
            timeout_per_batch=30.0
        )

        if records and len(records) > 0:
            logger.info(f"数据验证成功: 找到 {len(records)} 条记录")
            return {
                "exists": True,
                "count": len(records),
                "error": None
            }
        else:
            logger.warning(f"数据验证失败: 未找到匹配记录，条件={conditions}")
            return {
                "exists": False,
                "count": 0,
                "error": "无法在数据库里搜索到相关信息"
            }

    except Exception as e:
        logger.error(f"数据验证异常: {e}")
        return {
            "exists": False,
            "count": 0,
            "error": f"数据库查询失败: {str(e)}"
        }


async def process_dd_c_background(
    report_code: str,
    dept_id: str,
    rdb_client
):
    """后台异步处理DD-C数据"""
    try:
        logger.info(f"开始后台处理DD-C: report_code={report_code}, dept_id={dept_id}")

        # 导入DD-C核心处理器
        from modules.dd_submission.dd_c.dd_c_core_processor import process_dd_c

        # 执行核心处理逻辑
        result = await process_dd_c(
            rdb_client=rdb_client,
            report_code=report_code,
            dept_id=dept_id
        )

        # 计算成功率
        success_rate = (result.processed_records / result.total_records * 100) if result.total_records > 0 else 0
        logger.info(f"后台处理完成: 处理{result.processed_records}条记录，总共{result.total_records}条，成功率{success_rate:.1f}%")

        # 构建回调数据
        callback_items = []
        if result.success and result.result_data:
            items = result.result_data.get('item', [])
            for item in items:
                # 构建SDR字段数据（包含SDR01-SDR15）
                callback_item = DDCCallbackItem(
                    entry_id=item.get('entry_id', ''),
                    entry_type=item.get('entry_type', 'ITEM'),
                    # SDR01-SDR15字段
                    SDR01=item.get('sdr01', ''),
                    SDR02=item.get('sdr02', ''),
                    SDR03=item.get('sdr03', ''),
                    SDR04=item.get('sdr04', ''),
                    SDR05=item.get('sdr05', ''),
                    SDR06=item.get('sdr06', ''),
                    SDR07=item.get('sdr07', ''),
                    SDR08=item.get('sdr08', ''),
                    SDR09=item.get('sdr09', ''),
                    SDR10=item.get('sdr10', ''),
                    SDR11=item.get('sdr11', ''),
                    SDR12=item.get('sdr12', ''),
                    SDR13=item.get('sdr13', ''),
                    SDR14=item.get('sdr14', ''),
                    SDR15=item.get('sdr15', ''),
                    index=item.get('index', {})
                )
                callback_items.append(callback_item)

        # 构建回调请求
        callback_request = DDCCallbackRequest(
            report_code=report_code,
            dept_id=dept_id,
            item=callback_items
        )

        # 调用前端回调接口
        await call_frontend_callback(callback_request)

    except Exception as e:
        logger.error(f"后台处理失败: {e}")
        # 即使失败也要通知前端
        try:
            callback_request = DDCCallbackRequest(
                report_code=report_code,
                dept_id=dept_id,
                item=[]
            )
            await call_frontend_callback(callback_request)
        except:
            pass


async def call_frontend_callback(callback_request: DDCCallbackRequest):
    """调用前端回调接口"""
    try:
        import httpx

        # DD-C的前端回调URL
        callback_url = "http://218.78.129.173:30134/regulatory/release/ai/back/ddDataTechBackAI"
        logger.info(f"调用DD-C前端回调: {callback_url}")

        # 使用json.dumps确保正确的JSON序列化（单引号变成双引号）
        json_data = json.dumps(callback_request.model_dump(), ensure_ascii=False)
        logger.info(f"回调数据JSON: {json_data}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                callback_url,
                json=callback_request.model_dump()
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    logger.info(f"DD-C前端回调成功: {result.get('msg')}")
                else:
                    logger.warning(f"DD-C前端回调返回错误: {result}")
            else:
                logger.error(f"DD-C前端回调失败: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"DD-C前端回调异常: {e}")


@router.post(
    "/process",
    response_model=DDCProcessResponse,
    summary="DD-C数据处理（异步模式）",
    description="立即验证数据存在性并返回状态，后台异步处理数据并回调前端"
)
async def process_dd_c_enhanced(
    request: DDCProcessRequest,
    background_tasks: BackgroundTasks,
    clients: Dict[str, Any] = Depends(get_database_clients)
) -> DDCProcessResponse:
    """
    DD-C数据处理接口（异步模式）

    处理流程：
    1. 立即验证数据存在性
    2. 返回处理状态（成功/失败）
    3. 后台异步处理
    4. 处理完成后回调前端接口

    前端回调格式：
    {
        "report_code": "S71_ADS_RELEASE_V0",
        "dept_id": "30239",
        "item": [
            {
                "entry_id": "submission_id",
                "entry_type": "TABLE/ITEM",
                "sdr01": "...",
                "sdr15": "...",
                "index": {...}
            }
        ]
    }
    """
    try:
        logger.info(f"收到DD-C处理请求: report_code={request.report_code}, dept_id={request.dept_id}")

        # 1. 立即验证数据存在性
        validation_result = await validate_data_exists(
            clients['rdb_client'],
            request.report_code,
            request.dept_id
        )

        if not validation_result["exists"]:
            # 数据不存在，立即返回错误
            logger.warning(f"数据验证失败: {validation_result['error']}")
            return DDCProcessResponse(
                code="400",
                msg="无法在数据库里搜索到相关信息"
            )

        logger.info(f"数据验证通过，找到{validation_result['count']}条记录")

        # 2. 启动后台异步处理
        background_tasks.add_task(
            process_dd_c_background,
            request.report_code,
            request.dept_id,
            clients['rdb_client']
        )

        # 3. 立即返回处理中状态
        return DDCProcessResponse(
            code="0",
            msg="业务信息正在处理中"
        )

    except Exception as e:
        logger.error(f"DD-C处理请求失败: {e}")
        return DDCProcessResponse(
            code="400",
            msg=f"处理失败: {str(e)}"
        )
