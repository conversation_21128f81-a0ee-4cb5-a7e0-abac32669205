"""
DD提交数据最终确定使用示例

展示如何使用FinalizationAPI处理前端最终确定的数据。
"""

import asyncio
from typing import Any, Dict, List
from .api import create_finalization_api


async def example_extraction_finalization():
    """
    报表解读阶段数据最终确定示例
    """
    # 模拟前端传入的数据
    frontend_data = {
        "report_code": "G0107_beta_v1.0",
        "dept_id": "DEPT001",  # 注意：报表解读阶段不存在dept_id
        "step": "EXTRACTION",
        "data": [
            {
                "entry_id": "SUB001",
                "entry_type": "范围项",
                "DR22": ["DEPT001", "DEPT002"],
                "BDR01": ["DEPT001"],
                "BDR03": "范围描述内容"
            },
            {
                "entry_id": "SUB002", 
                "entry_type": "填报项",
                "DR22": ["DEPT003"],
                "BDR01": ["DEPT003"],
                "BDR03": "填报项描述内容"
            }
        ]
    }
    
    # 假设已经有dd_crud实例
    # dd_crud = DDCrud(rdb_client, vdb_client, embedding_client)
    # api = create_finalization_api(dd_crud)
    
    print("=== 报表解读阶段数据最终确定示例 ===")
    print(f"报表代码: {frontend_data['report_code']}")
    print(f"解读阶段: {frontend_data['step']}")
    print(f"数据量: {len(frontend_data['data'])}")
    
    # 策略1：删除重建
    print("\n--- 使用删除重建策略 ---")
    # result = await api.finalize_data(
    #     report_code=frontend_data["report_code"],
    #     step=frontend_data["step"],
    #     data=frontend_data["data"],
    #     strategy="delete_insert"
    # )
    # print(f"结果: {result}")
    
    # 策略2：批量更新
    print("\n--- 使用批量更新策略 ---")
    # result = await api.finalize_data(
    #     report_code=frontend_data["report_code"],
    #     step=frontend_data["step"],
    #     data=frontend_data["data"],
    #     strategy="batch_update"
    # )
    # print(f"结果: {result}")


async def example_duty_finalization():
    """
    义务解读阶段数据最终确定示例
    """
    frontend_data = {
        "report_code": "G0107_beta_v1.0",
        "dept_id": "DEPT001",
        "step": "DUTY",
        "data": [
            {
                "entry_id": "SUB001",
                "entry_type": "义务项",
                "DR22": ["DEPT001"],
                "BDR01": ["DEPT001"],
                "BDR03": "义务描述内容"
            }
        ]
    }
    
    print("=== 义务解读阶段数据最终确定示例 ===")
    print(f"报表代码: {frontend_data['report_code']}")
    print(f"部门ID: {frontend_data['dept_id']}")
    print(f"解读阶段: {frontend_data['step']}")
    print("注意：义务解读阶段功能待实现")


def example_field_mapping():
    """
    字段映射示例
    """
    print("=== 字段映射示例 ===")
    
    # 前端字段
    frontend_item = {
        "entry_id": "SUB001",
        "entry_type": "范围项", 
        "DR22": ["DEPT001", "DEPT002"],
        "BDR01": ["DEPT001"],
        "BDR03": "范围描述内容"
    }
    
    print("前端字段:")
    for key, value in frontend_item.items():
        print(f"  {key}: {value}")
    
    # 映射后的数据库字段（示例）
    db_record = {
        "version": "G0107_beta_v1.0",
        "submission_id": "SUB001",
        "submission_type": "范围项",
        "dr22": "DEPT001,DEPT002",
        "bdr01": "DEPT001", 
        "bdr03": "范围描述内容"
    }
    
    print("\n映射后的数据库字段:")
    for key, value in db_record.items():
        print(f"  {key}: {value}")


async def example_batch_operations():
    """
    批量操作示例
    """
    print("=== 批量操作示例 ===")
    
    # 大量数据示例
    large_data = []
    for i in range(100):
        large_data.append({
            "entry_id": f"SUB{i:03d}",
            "entry_type": "范围项" if i % 2 == 0 else "填报项",
            "DR22": [f"DEPT{i:03d}"],
            "BDR01": [f"DEPT{i:03d}"],
            "BDR03": f"描述内容{i}"
        })
    
    print(f"生成了 {len(large_data)} 条测试数据")
    print("前3条数据示例:")
    for i, item in enumerate(large_data[:3]):
        print(f"  {i+1}. {item}")
    
    print("\n批量操作配置建议:")
    print("- 删除重建策略：适用于数据量较大，需要完全替换的场景")
    print("- 批量更新策略：适用于数据量适中，需要精确更新的场景")
    print("- 建议batch_size: 500-1000")
    print("- 建议max_concurrency: 3-5")


if __name__ == "__main__":
    """
    运行示例
    """
    print("DD提交数据最终确定模块使用示例\n")
    
    # 运行同步示例
    example_field_mapping()
    print("\n" + "="*50 + "\n")
    
    # 运行异步示例
    asyncio.run(example_extraction_finalization())
    print("\n" + "="*50 + "\n")
    
    asyncio.run(example_duty_finalization())
    print("\n" + "="*50 + "\n")
    
    asyncio.run(example_batch_operations())
