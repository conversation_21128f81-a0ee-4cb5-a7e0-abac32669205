"""
DD-C推荐API路由

简单的推荐接口：
- 输入：report_code + entry_id
- 输出：entry_id + entry_type + sdr01-sdr15
"""

from fastapi import APIRouter
from pydantic import BaseModel
from service import get_client

# 创建路由器
router = APIRouter(prefix="/dd-c", tags=["DD-C推荐"])


class RecommendRequest(BaseModel):
    """推荐请求"""
    report_code: str
    entry_id: str


@router.post("/recommend")
async def recommend_dd_c_fields(request: RecommendRequest):
    """DD-C字段推荐"""
    try:
        # 获取客户端
        rdb_client = await get_client('database.rdbs.mysql')
        vdb_client = await get_client('database.vdbs.pgvector')
        embedding_client = await get_client('model.embeddings.moka-m3e-base')
        
        # 调用推荐处理器
        from modules.dd_submission.dd_c.dd_c_recommend_processor import recommend_dd_c
        
        result = await recommend_dd_c(
            rdb_client=rdb_client,
            entry_id=request.entry_id,
            report_code=request.report_code,
            vdb_client=vdb_client,
            embedding_client=embedding_client
        )
        
        # 直接返回推荐数据
        if result.success:
            return result.recommended_data
        else:
            return {
                "entry_id": request.entry_id,
                "entry_type": "ITEM",
                **{f"DR{i:02d}": "" for i in range(1, 23)},  # DR01-DR22
                **{f"BDR{i:02d}": "" for i in range(1, 18)},  # BDR01-BDR17
                **{f"SDR{i:02d}": "" for i in range(1, 16)}   # SDR01-SDR15
            }
            
    except Exception:
        # 异常时返回空数据
        return {
            "entry_id": request.entry_id,
            "entry_type": "ITEM",
            **{f"DR{i:02d}": "" for i in range(1, 23)},  # DR01-DR22
            **{f"BDR{i:02d}": "" for i in range(1, 18)},  # BDR01-BDR17
            **{f"SDR{i:02d}": "" for i in range(1, 16)}   # SDR01-SDR15
        }
