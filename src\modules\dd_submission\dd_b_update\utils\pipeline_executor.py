"""
Pipeline执行器 - 执行部分Pipeline流程
"""

import logging
import time
from typing import Dict, List, Any, Optional
from pipeline.nl2sql_pipeline import CompletePipelineExecutor
from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
from modules.knowledge.metadata.crud import MetadataCrud
from modules.dd_submission.dd_b.infrastructure.models import DDBRecord
from ..models.update_models import UpdateStrategy, PipelineExecutionParams, UpdateResult

logger = logging.getLogger(__name__)


class PipelineExecutor:
    """Pipeline执行器"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化Pipeline执行器

        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 嵌入模型客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # 初始化Pipeline（正确的初始化方式）
        self.pipeline = CompletePipelineExecutor(default_db_type="mysql")

        # 初始化字段映射器（复用现有逻辑）
        self.metadata_crud = MetadataCrud(rdb_client)
        self.field_mapper = PipelineFieldMapper(self.metadata_crud)

        logger.debug("Pipeline执行器初始化完成")
    
    async def execute_pipeline_strategy(
        self,
        strategy: UpdateStrategy,
        params: PipelineExecutionParams,
        entry_id: str,
        original_data: Dict[str, Any]
    ) -> UpdateResult:
        """
        根据策略执行Pipeline

        Args:
            strategy: 更新策略
            params: Pipeline执行参数
            entry_id: 记录ID
            original_data: 原始记录数据

        Returns:
            更新结果
        """
        start_time = time.time()

        try:
            logger.debug(f"开始执行Pipeline策略: {strategy.value}, entry_id={entry_id}")

            if strategy == UpdateStrategy.COLUMN_SELECTION:
                result = await self._execute_column_selection_strategy(params, entry_id, original_data)
            elif strategy == UpdateStrategy.SQL_GENERATION:
                result = await self._execute_sql_generation_strategy(params, entry_id, original_data)
            else:
                raise ValueError(f"不支持的Pipeline策略: {strategy}")

            result.execution_time = time.time() - start_time
            logger.debug(f"Pipeline策略执行完成: {strategy.value}, entry_id={entry_id}, 耗时={result.execution_time:.2f}s")

            return result

        except Exception as e:
            error_msg = f"Pipeline策略执行失败: {strategy.value}, entry_id={entry_id}, error={e}"
            logger.error(error_msg)
            logger.info(f"启用兜底机制：直接更新字段 entry_id={entry_id}")

            # 兜底机制：Pipeline失败时直接更新字段
            try:
                fallback_result = await self._execute_fallback_update(params, entry_id, original_data, strategy)
                fallback_result.execution_time = time.time() - start_time
                return fallback_result
            except Exception as fallback_error:
                logger.error(f"兜底机制也失败了: entry_id={entry_id}, error={fallback_error}")
                return UpdateResult(
                    entry_id=entry_id,
                    success=False,
                    strategy=strategy,
                    error_message=f"Pipeline和兜底机制都失败: {error_msg}, 兜底错误: {fallback_error}",
                    execution_time=time.time() - start_time
                )
    
    async def _execute_column_selection_strategy(
        self,
        params: PipelineExecutionParams,
        entry_id: str,
        original_data: Dict[str, Any]
    ) -> UpdateResult:
        """
        执行字段选择策略

        Args:
            params: Pipeline执行参数
            entry_id: 记录ID
            original_data: 原始记录数据

        Returns:
            更新结果
        """
        try:
            logger.debug(f"执行字段选择策略: entry_id={entry_id}")
            logger.debug(f"  candidate_tables: {params.candidate_tables}")
            logger.debug(f"  schema_generation_params: {params.schema_generation_params}")

            # 调用Pipeline的字段选择方法
            pipeline_result = await self.pipeline.execute_column_selection(
                candidate_tables=params.candidate_tables,
                schema_generation_params=params.schema_generation_params,
                user_question=params.user_question,
                hint=params.hint,
                db_type=params.db_type
            )

            logger.debug(f"字段选择Pipeline执行成功: entry_id={entry_id}")
            logger.debug(f"  pipeline_result keys: {list(pipeline_result.keys())}")

            # 使用现有的字段映射器进行映射
            updated_fields = await self._map_pipeline_result_to_fields(
                pipeline_result, original_data, UpdateStrategy.COLUMN_SELECTION
            )

            return UpdateResult(
                entry_id=entry_id,
                success=True,
                strategy=UpdateStrategy.COLUMN_SELECTION,
                updated_fields=updated_fields,
                pipeline_result=pipeline_result
            )

        except Exception as e:
            error_msg = f"字段选择策略执行失败: entry_id={entry_id}, error={e}"
            logger.error(error_msg)

            return UpdateResult(
                entry_id=entry_id,
                success=False,
                strategy=UpdateStrategy.COLUMN_SELECTION,
                error_message=error_msg
            )
    
    async def _execute_sql_generation_strategy(
        self,
        params: PipelineExecutionParams,
        entry_id: str,
        original_data: Dict[str, Any]
    ) -> UpdateResult:
        """
        执行SQL生成策略

        Args:
            params: Pipeline执行参数
            entry_id: 记录ID
            original_data: 原始记录数据

        Returns:
            更新结果
        """
        try:
            logger.debug(f"执行SQL生成策略: entry_id={entry_id}")
            logger.debug(f"  candidate_columns: {params.candidate_columns}")
            logger.debug(f"  schema_generation_params: {params.schema_generation_params}")

            # 调用Pipeline的SQL生成方法
            pipeline_result = await self.pipeline.execute_sql_generation(
                candidate_columns=params.candidate_columns,
                schema_generation_params=params.schema_generation_params,
                user_question=params.user_question,
                hint=params.hint,
                db_type=params.db_type
            )

            logger.debug(f"SQL生成Pipeline执行成功: entry_id={entry_id}")
            logger.debug(f"  pipeline_result keys: {list(pipeline_result.keys())}")

            # 使用现有的字段映射器进行映射
            updated_fields = await self._map_pipeline_result_to_fields(
                pipeline_result, original_data, UpdateStrategy.SQL_GENERATION
            )

            return UpdateResult(
                entry_id=entry_id,
                success=True,
                strategy=UpdateStrategy.SQL_GENERATION,
                updated_fields=updated_fields,
                pipeline_result=pipeline_result
            )

        except Exception as e:
            error_msg = f"SQL生成策略执行失败: entry_id={entry_id}, error={e}"
            logger.error(error_msg)

            return UpdateResult(
                entry_id=entry_id,
                success=False,
                strategy=UpdateStrategy.SQL_GENERATION,
                error_message=error_msg
            )
    
    async def _map_pipeline_result_to_fields(
        self,
        pipeline_result: Dict[str, Any],
        original_data: Dict[str, Any],
        strategy: UpdateStrategy
    ) -> Dict[str, Any]:
        """
        使用现有的字段映射器将Pipeline结果映射为更新字段

        Args:
            pipeline_result: Pipeline执行结果
            original_data: 原始记录数据
            strategy: 更新策略

        Returns:
            需要更新的字段字典
        """
        try:
            # 创建DDBRecord对象（复用现有逻辑）
            original_record = DDBRecord(
                id=int(original_data.get('id', 0)),
                dept_id=original_data.get('dept_id', ''),
                report_code=original_data.get('version', ''),
                dr01=original_data.get('dr01', ''),
                bdr09=original_data.get('bdr09', ''),
                bdr10=original_data.get('bdr10', ''),
                bdr11=original_data.get('bdr11', ''),
                bdr16=original_data.get('bdr16', ''),
                bdr17=original_data.get('bdr17', '')
            )

            # 使用现有的字段映射器
            mapping_result = await self.field_mapper.map_single_record(
                pipeline_result, original_record, keep_raw_format=False
            )

            # 转换为字符串格式（复用现有逻辑）
            string_results = self.field_mapper.convert_to_string_format([mapping_result])
            if not string_results:
                return {}

            string_result = string_results[0]

            # 根据策略过滤需要更新的字段
            updated_fields = {}

            if strategy == UpdateStrategy.COLUMN_SELECTION:
                # Column Selection策略：更新除BDR09之外的所有Pipeline生成字段
                # 包含BDR和对应的SDR字段
                # 注意：BDR16保留用户输入，不使用Pipeline生成的值
                field_mapping = {
                    # BDR字段
                    'bdr10': string_result.get('bdr10'),
                    'bdr11': string_result.get('bdr11'),
                    # bdr16: 保留用户输入，不使用Pipeline生成的值
                    # 对应的SDR字段
                    'sdr06': string_result.get('sdr06'),  # SDR06 = BDR10
                    'sdr08': string_result.get('sdr08'),  # SDR08 = BDR11
                    'sdr09': string_result.get('sdr09'),  # SDR09 = 字段中文名
                    'sdr10': string_result.get('sdr10'),  # SDR10 = SQL语句
                    'sdr12': string_result.get('sdr12'),  # SDR12 = JOIN条件
                }

            elif strategy == UpdateStrategy.SQL_GENERATION:
                # SQL Generation策略：更新SQL生成相关字段
                # 包含BDR和对应的SDR字段
                field_mapping = {
                    # BDR字段
                    'bdr11': string_result.get('bdr11'),  # BDR11需要更新
                    'bdr16': string_result.get('bdr16'),
                    # 对应的SDR字段
                    'sdr08': string_result.get('sdr08'),  # SDR08 = BDR11
                    'sdr09': string_result.get('sdr09'),  # SDR09 = 字段中文名
                    'sdr10': string_result.get('sdr10'),  # SDR10 = SQL语句
                    'sdr12': string_result.get('sdr12'),  # SDR12 = JOIN条件
                }
            else:
                field_mapping = {}

            # 添加非空的BDR和SDR字段
            for field_name, field_value in field_mapping.items():
                if field_value is not None and (field_name.startswith('bdr') or field_name.startswith('sdr')):
                    updated_fields[field_name] = field_value

            logger.debug(f"字段映射完成: strategy={strategy.value}, 更新字段={list(updated_fields.keys())}")

            return updated_fields

        except Exception as e:
            logger.error(f"字段映射失败: strategy={strategy.value}, error={e}")
            return {}

    async def _execute_fallback_update(
        self,
        params: PipelineExecutionParams,
        entry_id: str,
        original_data: Dict[str, Any],
        original_strategy: UpdateStrategy
    ) -> UpdateResult:
        """
        执行兜底更新机制

        当Pipeline执行失败时，直接更新字段作为兜底方案

        Args:
            params: Pipeline执行参数
            entry_id: 记录ID
            original_data: 原始记录数据
            original_strategy: 原始策略

        Returns:
            更新结果
        """
        try:
            logger.debug(f"执行兜底更新: entry_id={entry_id}, 原始策略={original_strategy.value}")

            # 从params中提取要更新的字段
            update_fields = {}

            # 根据不同的策略提取相应的字段
            if original_strategy == UpdateStrategy.COLUMN_SELECTION:
                # BDR09或BDR16策略的兜底
                if params.candidate_tables:
                    # 从candidate_tables提取BDR09
                    source_tables = params.candidate_tables.get("source", [])
                    if source_tables:
                        import json
                        update_fields['bdr09'] = json.dumps(source_tables, ensure_ascii=False)
                        update_fields['sdr05'] = update_fields['bdr09']  # SDR05 = BDR09

                        # 如果是BDR16策略，也更新BDR16
                        if 'BDR16' in str(params.user_question) or 'bdr16' in original_data:
                            update_fields['bdr16'] = f"表范围:\n{source_tables}"

            elif original_strategy == UpdateStrategy.SQL_GENERATION:
                # BDR11策略的兜底
                if params.candidate_columns:
                    import json
                    update_fields['bdr11'] = json.dumps(params.candidate_columns, ensure_ascii=False)
                    update_fields['sdr08'] = update_fields['bdr11']  # SDR08 = BDR11
                    # SDR11保持默认值"不适用"，不更新

            # 如果没有提取到字段，尝试从原始数据中获取
            if not update_fields:
                logger.warning(f"兜底更新无法提取字段，跳过更新: entry_id={entry_id}")
                return UpdateResult(
                    entry_id=entry_id,
                    success=True,
                    strategy=UpdateStrategy.SIMPLE_UPDATE,
                    updated_fields={},
                    error_message="Pipeline失败，兜底更新无字段可更新"
                )

            # 执行数据库更新
            record_id = int(original_data.get('id', 0))
            logger.debug(f"兜底更新字段详情: entry_id={entry_id}, update_fields={update_fields}")
            from ..crud.update_crud import UpdateCrud
            update_crud = UpdateCrud(self.rdb_client)
            update_success = await update_crud.update_record_fields(record_id, update_fields)

            logger.info(f"兜底更新完成: entry_id={entry_id}, 更新字段={list(update_fields.keys())}, 成功={update_success}")

            return UpdateResult(
                entry_id=entry_id,
                success=update_success,
                strategy=UpdateStrategy.SIMPLE_UPDATE,  # 兜底策略标记为简单更新
                updated_fields=update_fields,
                error_message=None if update_success else "兜底更新数据库操作失败"
            )

        except Exception as e:
            logger.error(f"兜底更新执行失败: entry_id={entry_id}, error={e}")
            raise
