"""
Knowledge V2 API - 码值关联管理路由

基于批量操作的高性能码值关联管理API
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends

from ..models.request_models import (
    BatchCreateRequest,
    BatchDeleteRequest,
    UnifiedGetRequest,
    UnifiedListRequest
)
from ..models.response_models import (
    BatchOperationResponse,
    UnifiedResponse,
    UnifiedListResponse,
    ErrorResponse
)
from ..dependencies import (
    get_codes_crud,
    validate_batch_config,
    create_batch_stats,
    log_batch_operation,
    handle_api_errors,
    PerformanceMonitor
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/code-relations", tags=["码值关联管理V2"])


@router.post("/batch", response_model=BatchOperationResponse, summary="批量创建码值关联")
@handle_api_errors
async def batch_create_code_relations(
    request: BatchCreateRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量创建码值关联
    
    - **relations**: 码值关联数据列表
    - **config**: 批量操作配置（可选）
    
    支持源字段(source)和指标字段(index)的码值关联创建
    """
    with PerformanceMonitor("批量创建码值关联") as monitor:
        # 验证配置
        config = validate_batch_config(request.config)
        
        # 转换数据格式
        relations_data = [relation.dict() for relation in request.relations]
        
        try:
            # 执行批量创建
            relation_ids = await codes_crud.batch_create_code_relations(
                relations_data,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            # 创建统计信息
            stats = create_batch_stats(
                total_requested=len(relations_data),
                total_successful=len(relation_ids),
                execution_time=monitor.execution_time,
                batch_count=len(relations_data) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            # 记录日志
            log_batch_operation(
                operation="创建",
                entity_type="码值关联",
                count=len(relation_ids),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return BatchOperationResponse(
                success=True,
                message=f"成功创建 {len(relation_ids)} 个码值关联",
                data=relation_ids,
                stats=stats,
                created_ids=relation_ids,
                affected_rows=len(relation_ids)
            )
            
        except Exception as e:
            logger.error(f"批量创建码值关联失败: {e}")
            stats = create_batch_stats(
                total_requested=len(relations_data),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量创建码值关联失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.delete("/batch", response_model=BatchOperationResponse, summary="批量删除码值关联")
@handle_api_errors
async def batch_delete_code_relations(
    request: BatchDeleteRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量删除码值关联
    
    - **conditions**: 删除条件列表
    - **config**: 批量操作配置（可选）
    
    支持按字段ID、码值集ID、关联类型等条件删除
    """
    with PerformanceMonitor("批量删除码值关联") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量删除
            success = await codes_crud.batch_delete_code_relations(
                conditions=request.conditions,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=len(request.conditions) if success else 0,
                execution_time=monitor.execution_time,
                batch_count=len(request.conditions) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="删除",
                entity_type="码值关联",
                count=len(request.conditions),
                execution_time=monitor.execution_time,
                success=success
            )
            
            return BatchOperationResponse(
                success=success,
                message=f"批量删除码值关联{'成功' if success else '失败'}",
                stats=stats,
                affected_rows=len(request.conditions) if success else 0
            )
            
        except Exception as e:
            logger.error(f"批量删除码值关联失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量删除码值关联失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.post("/batch/query", response_model=UnifiedListResponse, summary="批量查询码值关联")
@handle_api_errors
async def batch_query_code_relations(
    request: UnifiedListRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量查询码值关联
    
    - **queries**: 查询条件列表
    - **config**: 批量操作配置（可选）
    
    支持按知识库ID、字段ID、码值集ID、关联类型等条件查询
    """
    with PerformanceMonitor("批量查询码值关联") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量查询
            results = []
            for query in request.queries:
                # 使用list_code_relations方法进行查询
                result = await codes_crud.list_code_relations(
                    knowledge_id=query.get('knowledge_id'),
                    column_id=query.get('column_id'),
                    code_set_id=query.get('code_set_id'),
                    column_type=query.get('column_type')
                )
                results.append(result)
            
            # 计算总记录数
            total_records = sum(len(result) for result in results)
            
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=len(results),
                execution_time=monitor.execution_time,
                batch_count=len(request.queries) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="查询",
                entity_type="码值关联",
                count=len(request.queries),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return UnifiedListResponse(
                success=True,
                message=f"成功查询 {len(results)} 批码值关联，共 {total_records} 条记录",
                data=results,
                pagination={"total_records": total_records, "page": 1, "page_size": len(results)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"批量查询码值关联失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return UnifiedListResponse(
                success=False,
                message=f"批量查询码值关联失败: {str(e)}",
                data=[],
                pagination={"total_records": 0, "page": 1, "page_size": 0},
                timestamp=datetime.now()
            )


@router.get("/by-column/{column_id}", response_model=UnifiedListResponse, summary="按字段ID查询码值关联")
@handle_api_errors
async def get_code_relations_by_column(
    column_id: int,
    column_type: str = "source",
    knowledge_id: str = None,
    codes_crud = Depends(get_codes_crud)
):
    """
    按字段ID查询码值关联（便捷接口）
    
    - **column_id**: 字段ID
    - **column_type**: 字段类型 (source/index)
    - **knowledge_id**: 知识库ID（可选）
    """
    with PerformanceMonitor("按字段查询码值关联") as monitor:
        try:
            # 查询码值关联
            result = await codes_crud.list_code_relations(
                knowledge_id=knowledge_id,
                column_id=column_id,
                column_type=column_type
            )
            
            stats = create_batch_stats(
                total_requested=1,
                total_successful=1,
                execution_time=monitor.execution_time
            )
            
            return BatchQueryResponse(
                success=True,
                message=f"成功查询字段 {column_id} 的码值关联，共 {len(result)} 条记录",
                data=[result],
                stats=stats,
                total_records=len(result)
            )
            
        except Exception as e:
            logger.error(f"按字段查询码值关联失败: {e}")
            stats = create_batch_stats(
                total_requested=1,
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchQueryResponse(
                success=False,
                message=f"按字段查询码值关联失败: {str(e)}",
                data=[],
                stats=stats,
                total_records=0
            )


@router.get("/by-code-set/{code_set_id}", response_model=UnifiedListResponse, summary="按码值集ID查询码值关联")
@handle_api_errors
async def get_code_relations_by_code_set(
    code_set_id: int,
    knowledge_id: str = None,
    codes_crud = Depends(get_codes_crud)
):
    """
    按码值集ID查询码值关联（便捷接口）
    
    - **code_set_id**: 码值集ID
    - **knowledge_id**: 知识库ID（可选）
    """
    with PerformanceMonitor("按码值集查询码值关联") as monitor:
        try:
            # 查询码值关联
            result = await codes_crud.list_code_relations(
                knowledge_id=knowledge_id,
                code_set_id=code_set_id
            )
            
            stats = create_batch_stats(
                total_requested=1,
                total_successful=1,
                execution_time=monitor.execution_time
            )
            
            return BatchQueryResponse(
                success=True,
                message=f"成功查询码值集 {code_set_id} 的码值关联，共 {len(result)} 条记录",
                data=[result],
                stats=stats,
                total_records=len(result)
            )
            
        except Exception as e:
            logger.error(f"按码值集查询码值关联失败: {e}")
            stats = create_batch_stats(
                total_requested=1,
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchQueryResponse(
                success=False,
                message=f"按码值集查询码值关联失败: {str(e)}",
                data=[],
                stats=stats,
                total_records=0
            )
