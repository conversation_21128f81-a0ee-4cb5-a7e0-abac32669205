#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：chatbot_logic.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/26 21:22 
@Desc    ： 
"""
import asyncio
from datetime import date
import json
from typing import Any, List, Tuple, Dict

from dateutil.relativedelta import relativedelta
from loguru import logger

from app.ier.config.config import (K<PERSON><PERSON>LEDGE_ID,
                                   LAW_TABLE, LAW_TABLE_LAW_ID, LAW_TABLE_LAW_NAME, LAW_TABLE_LAW_TYPE, LAW_TABLE_LAW_NUMBER, LAW_TABLE_LAW_RELEASE_DATE, LAW_TABLE_LAW_SUMMARY,
                                   LAW_DOC_RELATION_TABLE, LAW_DOC_RELATION_TABLE_LAW_ID, LAW_DOC_RELATION_TABLE_LAW_FILE_ID, LAW_DOC_RELATION_TABLE_DOC_ID,
                                   DOC_CHUNKS_TABLE, DOC_CHUNKS_TABLE_DOC_ID, DOC_CHUNKS_TABLE_CHUNK_ID,DOC_CHUNKS_TABLE_CREATE_TIME,
                                   DOC_CHUNKS_INFO_TABLE, DOC_CHUNKS_INFO_TABLE_CHUNK_ID,
                                   DOC_CHUNKS_INFO_TABLE_INFO_VALUE, DOC_CHUNKS_INFO_TABLE_INFO_TYPE,
                                   IER_INFO_TYPE_CONTENT,
                                   CHATBOT_RESULT_TABLE, CHATBOT_RESULT_TABLE_USER_QUERY, CHATBOT_RESULT_TABLE_ANSWER,
                                   CHATBOT_RESULT_TABLE_USER_ID, CHATBOT_RESULT_TABLE_REQUEST_ID,
                                   CHATBOT_RESULT_TABLE_SESSION_ID, CHATBOT_RESULT_TABLE_CREATED_TIME, CHATBOT_HISTORY_NUMBER
                                   )
from app.ier.services.ier.model.ier_chatbot_model import (ChatbotQueryModel,
                                                          ChatBotQuestionType,
                                                          ChatbotSaveModel,
                                                          ChatbotHistoryQueryModel)
from app.ier.services.ier.prompts import (extract_json_from_response,
                                          law_question_type_match_prompt,
                                          chatbot_law_association_type_prompt,
                                          law_question_extract_law_name_prompt,
                                          chatbot_law_difference_type_prompt,
                                          law_question_extract_pos_prompt,
                                          chatbot_law_positioning_type_prompt,
                                          chatbot_law_time_judgment_type_prompt,
                                          law_question_extract_date_prompt,
                                          chatbot_other_type_prompt)
from app.ier.utils.data_utils import get_surrounding_items
from base.model_serve.model_runtime.entities import PromptMessage

class ChatBotLogic:
    """法规文件处理核心逻辑类"""
    def __init__(self, rdb_client: Any, vdb_client: Any, llm_client: Any, embed_client: Any, history_rounds: int = 3):
        """
        初始化法规文件处理逻辑

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            llm_client: 大模型客户端
            embed_client: embedding客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.llm_client = llm_client
        self.embed_client = embed_client
        self.history_rounds = history_rounds

    async def llm_get_question_type(self, question: str) -> ChatBotQuestionType:
        """
        根据用户的问题，判断用户的问题属于哪一个类型
        """
        prompt = law_question_type_match_prompt.format(question=question)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }
        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                   model_parameters=model_parameters)
        q_type_result = llm_result.message.content

        logger.info(f"question type llm result: {q_type_result}")
        extract_q_type_result = extract_json_from_response(q_type_result)
        logger.info(f"question type extract json from response: {extract_q_type_result}")

        q_type_value = extract_q_type_result.get("q_type", 6)
        if isinstance(q_type_value, str):
            try:
                q_type_value = int(q_type_value)
            except Exception as e:
                q_type_value = 6

        q_type = ChatBotQuestionType(q_type_value)
        return q_type

    async def llm_get_law_name_from_question(self, question: str):
        prompt = law_question_extract_law_name_prompt.format(question=question)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                         model_parameters=model_parameters)
        law_names = llm_result.message.content
        logger.info(f"law name extraction llm result: {law_names}")
        law_names = extract_json_from_response(law_names)
        logger.info(f"law info extraction extract json from response: {law_names}")

        return law_names.get("law_names", [])

    async def llm_get_date_from_question(self, question: str):
        today_date = date.today()

        prompt = law_question_extract_date_prompt.format(question=question, today_date=today_date)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                   model_parameters=model_parameters)
        date_info = llm_result.message.content
        logger.info(f"date extraction llm result: {date_info}")
        date_info = extract_json_from_response(date_info)
        logger.info(f"date extract json from response: {date_info}")

        return date_info

    async def llm_get_pos_from_question(self, question: str):
        prompt = law_question_extract_pos_prompt.format(question=question)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        llm_result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False,
                                                   model_parameters=model_parameters)
        pos_info = llm_result.message.content
        logger.info(f"law pos extraction llm result: {pos_info}")
        pos_info = extract_json_from_response(pos_info)
        logger.info(f"law pos extract json from response: {pos_info}")

        pos_result = pos_info['pos']
        if isinstance(pos_result, str):
            try:
                pos_result = int(pos_result)
            except Exception:
                pos_result = 0

        return pos_result

    async def query_summary_by_law_name(self, law_name):
        law_summary = self.rdb_client.query({
            "table": LAW_TABLE,
            "filters": {LAW_TABLE_LAW_NAME: law_name},
            "columns": [LAW_TABLE_LAW_SUMMARY]
        }).data
        logger.info(f"query law({law_name}) summary:{law_summary}.")
        if not law_summary or len(law_summary) < 1:
            return ""
        return law_summary[0].get(LAW_TABLE_LAW_SUMMARY, "")

    async def query_law_id_by_law_name(self, law_name):
        law_id = self.rdb_client.query({
            "table": LAW_TABLE,
            "filters": {LAW_TABLE_LAW_NAME: law_name},
            "columns": [LAW_TABLE_LAW_ID]
        }).data
        logger.info(f"query law({law_name}) id:{law_id}.")
        if not law_id or len(law_id) < 1:
            return None
        return law_id[0].get(LAW_TABLE_LAW_ID, None)

    async def query_law_info_by_date(self, date_start, date_end):
        logger.info(f"时间{date_start} - {date_end}")
        queries = [
            {
                "data": [LAW_TABLE_LAW_NAME, LAW_TABLE_LAW_SUMMARY],
                "filters": {
                    LAW_TABLE_LAW_RELEASE_DATE: {"$between": [date_start, date_end]},
                },
                "limit": 10
            }
        ]
        law_info = await self.rdb_client.abatch_query(LAW_TABLE, queries)
        logger.info(f"query law info(时间{date_start} - {date_end}):{law_info}.")
        if not law_info or len(law_info) < 1:
            return []
        return law_info

    async def query_doc_id_by_law_id(self, law_id):
        doc_id = self.rdb_client.query({
            "table": LAW_DOC_RELATION_TABLE,
            "filters": {LAW_DOC_RELATION_TABLE_LAW_ID: law_id},
            "columns": [LAW_DOC_RELATION_TABLE_DOC_ID]
        }).data
        logger.info(f"query doc(law_id:{law_id}) id:{doc_id}.")
        if not doc_id or len(doc_id) < 1:
            return None
        return doc_id[0].get(LAW_DOC_RELATION_TABLE_DOC_ID, None)

    async def query_chunk_id_by_doc_id(self, doc_id):
        chunk_id_list = []
        chunk_ids = self.rdb_client.query({
            "table": DOC_CHUNKS_TABLE,
            "filters": {DOC_CHUNKS_TABLE_DOC_ID: doc_id},
            "columns": [DOC_CHUNKS_TABLE_CHUNK_ID, DOC_CHUNKS_TABLE_CREATE_TIME],

        }).data
        sorted_chunks = sorted(
            chunk_ids,
            key=lambda x: x[DOC_CHUNKS_TABLE_CREATE_TIME]  # 按照创建时间排序
        )
        logger.info(f"query chunk(doc_id:{doc_id}) ids:{sorted_chunks}.")
        if not sorted_chunks or len(sorted_chunks) < 1:
            return None
        for chunk_id in sorted_chunks:
            temp = chunk_id.get(DOC_CHUNKS_TABLE_CHUNK_ID, None)
            if temp:
                chunk_id_list.append(temp)
        return chunk_id_list

    async def query_chunk_content_by_chunk_id(self, chunk_id):
        chunk_contents = self.rdb_client.query({
            "table": DOC_CHUNKS_INFO_TABLE,
            "filters": {
                DOC_CHUNKS_INFO_TABLE_CHUNK_ID: chunk_id,
                DOC_CHUNKS_INFO_TABLE_INFO_TYPE: IER_INFO_TYPE_CONTENT
            },
            "columns": [DOC_CHUNKS_INFO_TABLE_INFO_VALUE]
        }).data
        logger.info(f"query chunk(chunk_id:{chunk_id}) contents:{chunk_contents}.")
        if not chunk_contents or len(chunk_contents) < 1:
            return ""

        return chunk_contents[0].get(DOC_CHUNKS_INFO_TABLE_INFO_VALUE, "")

    async def query_history(self, query:ChatbotQueryModel):
        query_history = self.rdb_client.query({
            "table": CHATBOT_RESULT_TABLE,
            "filters": {
                CHATBOT_RESULT_TABLE_USER_ID: query.user_id,
                CHATBOT_RESULT_TABLE_SESSION_ID: query.user_id
            },
            "columns": [CHATBOT_RESULT_TABLE_USER_QUERY, CHATBOT_RESULT_TABLE_ANSWER, CHATBOT_RESULT_TABLE_CREATED_TIME]
        }).data
        sorted_history = sorted(
            query_history,
            key=lambda x: x[CHATBOT_RESULT_TABLE_CREATED_TIME]  # 按照创建时间排序
        )
        if not sorted_history or len(sorted_history) < 1:
            return []

        return sorted_history[:3]

    async def handle_law_association_type_question(self, question: str, history:List):
        law_names = await self.llm_get_law_name_from_question(question)
        if len(law_names) >= 2:
            if len(law_names) >= 3:
                yield "当前的法规关联问题包含了多个法规，我只回答其中两个的关联关系，剩下的请分开提问。"
                yield f"选择的法规如下：法规A:{law_names[0]}, 法规B:{law_names[1]}"
            law_names = law_names[:2]

            related_info = [
                {
                    law_names[0]: await self.query_summary_by_law_name(law_names[0]),
                },
                {
                    law_names[1]: await self.query_summary_by_law_name(law_names[1]),
                }
            ]
        else:
            related_info = {}

        logger.info(f"related_info:{related_info}")
        prompt = chatbot_law_association_type_prompt.format(question=question, related_info=related_info, history=history)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        stream_generator = await self.llm_client.ainvoke(prompt_messages=messages, stream=True,
                                                         model_parameters=model_parameters)
        try:
            async for chunk in stream_generator:
                # 检查chunk结构，适配LLMResultChunk的数据结构
                content = ""
                if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                    content = chunk.delta.message.content
                yield content
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            logger.warning(f"Stream processing cancelled/timeout: {e}")
            # 返回已收集的内容
            yield ""

    async def handle_law_difference_question(self, question: str, history:List):
        law_names = await self.llm_get_law_name_from_question(question)
        logger.info(f"law_names :type : {type(law_names)}")
        if len(law_names) >= 2:
            if len(law_names) >= 3:
                yield "当前的法规区别问题包含了多个法规，我只回答其中两个的关联关系，剩下的请分开提问。"
                yield f"选择的法规如下：法规A:{law_names[0]}, 法规B:{law_names[1]}"
            law_names = law_names[:2]

            summary_info = [
                {
                    law_names[0]: await self.query_summary_by_law_name(law_names[0]),
                },
                {
                    law_names[1]: await self.query_summary_by_law_name(law_names[1]),
                }
            ]
        else:
            summary_info = {}

        logger.info(f"summary_info:{summary_info}")
        prompt = chatbot_law_difference_type_prompt.format(question=question, summary_info=summary_info, history=history)
        logger.info(f"chatbot_law_difference_type_prompt:{prompt}")
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        stream_generator = await self.llm_client.ainvoke(prompt_messages=messages, stream=True,
                                                         model_parameters=model_parameters)
        try:
            async for chunk in stream_generator:
                # 检查chunk结构，适配LLMResultChunk的数据结构
                content = ""
                if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                    content = chunk.delta.message.content
                yield content
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            logger.warning(f"Stream processing cancelled/timeout: {e}")
            # 返回已收集的内容
            yield ""

    async def handle_law_overview_question(self, question: str):
        law_names = await self.llm_get_law_name_from_question(question)
        for law_name in law_names:
            summary = await self.query_summary_by_law_name(law_name)
            yield f"《{law_name}》:\n{summary}\n"

    async def handle_law_positioning_question(self, question: str, history:List):
        law_names = await self.llm_get_law_name_from_question(question)
        if len(law_names) >= 1:
            if len(law_names) > 1:
                yield f"当前问题包含多个法规，请分别提问，当前回答以《{law_names[0]}》法规为主。"
            law_id = await self.query_law_id_by_law_name(law_names[0])
            if not law_id:
                yield f"《{law_names[0]}》未在知识库中查询到，可能是法规名输入有误，请重新提问。"
            else:
                doc_id = await self.query_doc_id_by_law_id(law_id)
                pos = await self.llm_get_pos_from_question(question)
                if pos > 0:
                    chunk_ids = await self.query_chunk_id_by_doc_id(doc_id)
                    if pos > len(chunk_ids):
                        yield f"当前的条款不在当前法规中，请重新提问/参考如下回答。"
                        async for item in self.handle_other_type_question(question, history=history):
                            yield item
                    else:
                        chunk_ids = get_surrounding_items(chunk_ids, pos-1)
                        contents = []
                        for chunk_id in chunk_ids:
                            content = await self.query_chunk_content_by_chunk_id(chunk_id)
                            contents.append(content)

                        prompt = chatbot_law_positioning_type_prompt.format(question=question, law_info=contents, history=history)
                        logger.info(f"chatbot_law_positioning_type_prompt:{prompt}")
                        messages = [
                            PromptMessage(role="user", content=prompt),
                        ]
                        model_parameters = {
                            "max_tokens": 10000,
                            "temperature": 0.7
                        }

                        stream_generator = await self.llm_client.ainvoke(prompt_messages=messages, stream=True,
                                                                         model_parameters=model_parameters)
                        try:
                            async for chunk in stream_generator:
                                # 检查chunk结构，适配LLMResultChunk的数据结构
                                content = ""
                                if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                                    content = chunk.delta.message.content
                                yield content
                        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
                            logger.warning(f"Stream processing cancelled/timeout: {e}")
                            # 返回已收集的内容
                            yield ""
                else:
                    yield f"当前的条款不在当前法规中，请重新提问/参考如下回答。"
                    async for item in self.handle_other_type_question(question, history=history):
                        yield item
        else:
            yield f"当前问题没有提及法规，请重新提问/参考如下回答。"
            async for item in self.handle_other_type_question(question, history=history):
                yield item

    async def handle_law_time_type_judgment_question(self, question: str, history:List):
        today_date = date.today()
        date_info = await self.llm_get_date_from_question(question)
        date_start = date_info.get("date_start", None)
        date_end = date_info.get("date_end", None)
        if date_end is None:
            yield f"\n未从问题中提取到结束时间，将结束时间范围设定为今天\n"
            date_end = today_date

        if date_start is None:
            yield f"\n未从问题中提取到开始时间，将开始时间范围设定为3年前\n"
            date_start = today_date - relativedelta(years=3)

        law_infos = await self.query_law_info_by_date(date_start, date_end)
        if law_infos is None:
            # 没提取到内容，让大模型自行回答
            async for item in self.handle_other_type_question(question):
                yield item
        else:
            logger.info(f"handle_law_time_type_judgment_question law infos：{law_infos}")

            prompt = chatbot_law_time_judgment_type_prompt.format(question=question, laws=law_infos, history=history)
            logger.info(f"chatbot_law_time_judgment_type_prompt:{prompt}")
            messages = [
                PromptMessage(role="user", content=prompt),
            ]
            model_parameters = {
                "max_tokens": 10000,
                "temperature": 0.7
            }

            stream_generator = await self.llm_client.ainvoke(prompt_messages=messages, stream=True,
                                                             model_parameters=model_parameters)
            try:
                async for chunk in stream_generator:
                    # 检查chunk结构，适配LLMResultChunk的数据结构
                    content = ""
                    if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                        content = chunk.delta.message.content
                    yield content
            except (asyncio.TimeoutError, asyncio.CancelledError) as e:
                logger.warning(f"Stream processing cancelled/timeout: {e}")
                # 返回已收集的内容
                yield ""

    async def handle_other_type_question(self, question: str, history:List):
        prompt = chatbot_other_type_prompt.format(question=question, history=history)
        messages = [
            PromptMessage(role="user", content=prompt),
        ]
        model_parameters = {
            "max_tokens": 10000,
            "temperature": 0.7
        }

        stream_generator = await self.llm_client.ainvoke(prompt_messages=messages, stream=True,
                                                   model_parameters=model_parameters)
        try:
            async for chunk in stream_generator:
                # 检查chunk结构，适配LLMResultChunk的数据结构
                content = ""
                if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                    content = chunk.delta.message.content
                yield content
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            logger.warning(f"Stream processing cancelled/timeout: {e}")
            # 返回已收集的内容
            yield ""

    async def handle_question(self, question_type: ChatBotQuestionType, question: str, history:List):
        match question_type:
            case ChatBotQuestionType.LAW_ASSOCIATION:
                async for item in self.handle_law_association_type_question(question, history):
                    yield item
            case ChatBotQuestionType.LAW_DIFFERENCE:
                async for item in self.handle_law_difference_question(question, history):
                    yield item
            case ChatBotQuestionType.LAW_OVERVIEW:
                async for item in self.handle_law_overview_question(question):
                    yield item
            case ChatBotQuestionType.LAW_POSITIONING:
                async for item in self.handle_law_positioning_question(question, history):
                    yield item
            case ChatBotQuestionType.LAW_TIME_JUDGMENT:
                async for item in self.handle_law_time_type_judgment_question(question,history):
                    yield item
            case ChatBotQuestionType.OTHER:
                async for item in self.handle_other_type_question(question, history):
                    yield item
            case _:
                raise ValueError("未知问题类型")

    async def query(self, query:ChatbotQueryModel):
        query_param = query.model_dump()
        logger.info(f"query param:{query_param}")
        question = query_param['user_query']

        try:
            q_type = await self.llm_get_question_type(question)

            # 结果类型判断
            if not isinstance(q_type, ChatBotQuestionType):
                q_type = ChatBotQuestionType.OTHER

            logger.info(f"question type result: {q_type}")
        except Exception as e:
            q_type = ChatBotQuestionType.OTHER
            logger.info(f"question type get failure. error: {e}")

        try:
            history = await self.query_history(query)
            logger.info(f"history:{history}")
            async for chunk in self.handle_question(q_type, question, history):
                message = {
                    "content": chunk
                }
                yield f"data: {json.dumps(message)}"
            logger.info(f"query finish, session id:{query_param['session_id']}")
            message = {
                "content": "stop"
            }
            yield f"data: {json.dumps(message)}"
        except Exception as e:
            logger.error(f"query failure, session id:{query_param['session_id']}. error: {e}")
            message = {
                "content": "stop"
            }
            yield f"data: {json.dumps(message)}"

    async def save_history(self, history_info: ChatbotSaveModel):
        try:
            history_insert_data = {
                CHATBOT_RESULT_TABLE_USER_QUERY: history_info.user_query if history_info.user_query else "",
                CHATBOT_RESULT_TABLE_REQUEST_ID: history_info.request_id if history_info.request_id else "",
                CHATBOT_RESULT_TABLE_USER_ID: history_info.user_id if history_info.user_id else "",
                CHATBOT_RESULT_TABLE_SESSION_ID: history_info.session_id if history_info.session_id else "",
                CHATBOT_RESULT_TABLE_ANSWER: history_info.answer if history_info.answer else ""
            }
            self.rdb_client.insert(
                {
                    "table": CHATBOT_RESULT_TABLE,
                    "data": history_insert_data
                }
            )
            logger.info(f"history info insert success.")
        except Exception as e:
            logger.error(f"history info insert failure, error:{e}")

    async def get_chatbot_history(self, request: ChatbotHistoryQueryModel):
        try:
            chatbot_history = self.rdb_client.query({
                "table": CHATBOT_RESULT_TABLE,
                "filters": {
                    CHATBOT_RESULT_TABLE_USER_ID: request.user_id,
                    CHATBOT_RESULT_TABLE_SESSION_ID: request.session_id
                },
                "columns": [CHATBOT_RESULT_TABLE_USER_ID, CHATBOT_RESULT_TABLE_SESSION_ID, CHATBOT_RESULT_TABLE_USER_QUERY, CHATBOT_RESULT_TABLE_ANSWER],
            }).data

            sorted_chunks = sorted(
                chatbot_history,
                key=lambda x: x[CHATBOT_RESULT_TABLE_ANSWER]  # 按照创建时间排序
            )

            page_num = request.page_num or 1
            page_size = request.page_size or 10

            start = (page_num - 1) * page_size
            end = start + page_size

            total_count = len(sorted_chunks)
            sorted_chunks = sorted_chunks[start:end]

            logger.info(f"query chatbot_history:{sorted_chunks}.")

            return {
                "page_num": request.page_num,
                "page_size": request.page_size,
                "total_size":1,
                "total": total_count,
                "pages": (total_count + page_size - 1) // page_size,
                "data":sorted_chunks
            }

            logger.info(f"history info query success, request param:{request.model_dump()}.")
        except Exception as e:
            logger.error(f"history info query failure, request param:{request.model_dump()}, error:{e}")
            return {
                "page_num":1,
                "page_size":10,
                "total_size":1,
                "data":[]
            }
