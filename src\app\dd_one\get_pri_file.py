import json
from pathlib import Path
import re
from typing import Dict, List
import asyncio
from config.config import qwen_llm_config, style_json_path
from app.dd_one.model_serve.model_runtime.model_providers.model_interface import UnifiedLLMInterface
from app.dd_one.model_serve.model_runtime.entities import PromptMessage
from service import get_client

_get_primary_file = '''
你是一名专业的信息提取人员，我需要你对下面的文件名中提取出该文件的发文的时间
注意：
只要提取到年即可
输出要满足json格式{"输出时间":data_dt}
如果没有时间，那就直接输出一个空字典
不要多余的解释，直接输出对应的json即可

# 例如：
需要提取的文件名：银发2025 14号国家金融监督管理总局关于做好2024年银行业非现场监管报表填报工作的通知

输出结果：
{
"输出时间":"2025"
}
这是你需要提取的文件名：{{content}}
'''

# llm_interface = UnifiedLLMInterface()

def get_llm_client():
    # 获取当前正在运行的事件循环
    loop = asyncio.get_event_loop()

    # 将异步函数提交到当前事件循环中执行
    coro = get_client("model.llms.opentrek")
    task = asyncio.run_coroutine_threadsafe(coro, loop)

    # 阻塞等待结果（在同步函数中）
    client = task.result()  # 这里会阻塞，直到 get_client() 返回

    return client


def extract_json_from_response(response: str):
    try:
        # 尝试直接解析整个响应为 JSON
        return json.loads(response)
    except json.JSONDecodeError:
        pass

    try:
        # 提取 ```json 中的内容
        # print('我开始解析咯',response)
        start_idx = response.find("```json") + len("```json")
        end_idx = response.find("```", start_idx)
        if end_idx == -1:
            end_idx = len(response)
        json_str = response[start_idx:end_idx].strip().replace('\'', '"')
        return json.loads(json_str)
    except Exception as e:
        raise ValueError(f"无法从响应中提取有效 JSON: {str(e)}")


def extract_year_from_filename(filename: str) -> str:
    """从文件名中提取发文年份（〔YYYY〕格式）。"""
    match = re.search(r'〔(\d{4})〕', filename)
    output = match.group(1) if match else ""
    if output == "":
        # print('走到这里')
        # 当没有匹配到日期时，我们就过大模型
        primary_query = _get_primary_file.replace("{{content}}", filename.split('.')[0])
        prompt_messages = [PromptMessage(role="user", content=primary_query)]
        qwen_llm_config["prompt_messages"] = prompt_messages

        # llm_interface = get_llm_client()
        # llm_output = llm_interface.invoke(prompt_messages=prompt_messages,stream=False)
        llm_interface = UnifiedLLMInterface()
        llm_output = llm_interface.invoke(**qwen_llm_config)

        # print('我开始解析咯',llm_output)
        llm_output = extract_json_from_response(llm_output.message.content)
        # print('我开始解析咯',llm_output)
        output = llm_output.get("输出时间", "")
    return output


def identify_doc_set(filenames: List[str]) -> str:
    """识别文件集合类型（1104、djz、survey）基于第二级文件夹名称。

    规则：
    - 如果所有第二级文件夹名称以‘A’开头，返回‘djz’。
    - 如果所有第二级文件夹名称以‘G’开头，返回‘1104’。
    - 其他情况（包括空列表或混合开头），返回‘survey’。
    """
    # 提取所有第二级文件夹名称
    second_level_folders = set()
    for filename in filenames:
        path = Path(filename)
        # 假设路径格式如 G01/G01.xlsx，提取第二级文件夹（G01）
        if len(path.parts) >= 2:
            second_level_folders.add(path.parts[-2])
    # 如果没有第二级文件夹，返回 survey
    if not second_level_folders:
        xlsx_count = 0
        excel_name = ""
        if len(filenames) <= 2 and any(
                filename.endswith('.xls') or filename.endswith('.xlsx') for filename in filenames
        ):
            for filename in filenames:
                if filename.endswith('.xls') or filename.endswith('.xlsx'):
                    xlsx_count += 1
                    excel_name = filename
            if xlsx_count == 1:
                if excel_name.startswith('A'):
                    return "djz"
                elif excel_name.startswith('G'):
                    return "1104"

        return "survey"

    # 检查所有文件夹名称的开头
    all_start_with_a = all(folder.startswith('A') for folder in second_level_folders)
    all_start_with_g = all(folder.startswith('G') for folder in second_level_folders)

    if all_start_with_a:
        return "djz"
    elif all_start_with_g:
        return "1104"
    else:
        return "survey"


def identify_main_file(filenames: List[str]) -> str:
    """识别主文件：包含‘非现场监管报表’且为PDF或Word文件。"""
    for filename in filenames:
        if "非现场监管报表" in filename and Path(filename).suffix.lower() in [".pdf", ".doc", ".docx"]:
            return filename
    return ""


def extract_doc_num(filename: str, year: str) -> str:
    """根据文件名和年份构造发文号（金发->国家金融监督管理总局，银发->中国人民银行）。"""
    date_match = re.search(r'\d+号', filename)
    if "金发" in filename:
        if '号' in date_match.group(0):
            return f"金发{year}年{date_match.group(0)}"
        else:
            return f"金发{year}年{date_match.group(0)}号"
    elif "银发" in filename:
        if '号' in date_match.group(0):
            return f"银发{year}年{date_match.group(0)}"
        else:
            return f"银发{year}年{date_match.group(0)}号"
    return ""


def recognize_files(files_path: List[str]) -> Dict:
    """
    识别文件列表，找出主文件并提取相关信息。

    Args:
        files_path (List[str]): 文件路径列表，例如：
            ["金发〔2023〕16号 国家金融监督管理总局关于做好2024年银行业非现场监管报表填报工作的通知.pdf",
             "G01/G01.xlsx", "G01/G01.doc", "G02/G02.xlsx", "G02/G02.doc"]

    Returns:
        Dict: 包含以下键值：
            - doc_set: 文件类型（1104、djz、survey）
            - time: 发文年份
            - doc_title: 主文件名称
            - doc_num: 发文号
    """
    result = {
        "doc_set": "",
        "time": "",
        "doc_title": "",
        "doc_num": ""
    }

    # 识别主文件
    main_file = identify_main_file(files_path)
    if not main_file:
        result["doc_set"] = identify_doc_set(files_path)
        return result  # 如果没有找到主文件，返回空结果

    # 提取主文件信息
    result["doc_title"] = Path(main_file).stem
    result["time"] = extract_year_from_filename(main_file)
    result["doc_set"] = identify_doc_set(files_path)
    result["doc_num"] = extract_doc_num(main_file, result["time"])

    return result

    # 测试代码


if __name__ == "__main__":
    test_files = [
        "金发2023年16号 国家金融监督管理总局关于做好2024年银行业非现场监管报表填报工作的通知.pdf",
        "G01.xlsx",
        "G02.xlsx"
    ]
    result = recognize_files(test_files)
    print(result)
