"""
DD-B增强数据处理器

集成了以下优化和扩展功能：
1. 优化的向量搜索（直接复用现有DD搜索实现）
2. 大模型生成链集成（为低置信度策略生成字段）
3. 并发处理设计（支持LLM最大15并发量）
4. 批量处理支持（支持数千条记录的批量处理）
5. 完整的错误处理和重试机制

并发控制层级分析：
- Level 1: 全局LLM并发控制（15个并发槽位）
- Level 2: Pipeline执行并发（每批15条记录）
- Level 3: 向量搜索并发（10个并发槽位）
- Level 4: 数据库连接并发（20个并发槽位）

推荐消息队列解决方案：
- 轻量级：使用 asyncio.Queue + 文件持久化
- 中等规模：使用 Redis + rq (Redis Queue)
- 大规模：使用 Celery + Redis/RabbitMQ
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Tuple

from modules.dd_submission.dd_b.infrastructure.models import (
    DDBProcessRequest,
    DDBProcessResult,
    DDBRecord,
    ProcessingStatusEnum,
    FieldFillInfo,
    FieldStatusEnum,
    GenerationDecision,
    GenerationStrategyEnum
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBConstants, DDBUtils
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBDatabaseError,
    DDBDataNotFoundError,
    DDBProcessingError,
    handle_async_ddb_errors,
    create_database_error,
    create_data_not_found_error,
    create_processing_error
)
from .field_filler import FieldFiller
from .history_connector import HistoryConnector
from .llm_generator import LLMGenerator, FieldGenerationIntegrator, create_llm_generator
from .concurrent_processor import ConcurrentProcessor, create_concurrent_processor
from .global_concurrency_manager import (
    GlobalConcurrencyManager,
    ConcurrencyConfig,
    ConcurrencyScope,
    get_global_concurrency_manager
)
from .pipeline_integrator import (
    PipelineIntegrator,
    PipelineRequest,
    PipelineResult,
    FieldMappingResult,
    create_pipeline_integrator
)

logger = logging.getLogger(__name__)


class EnhancedDataProcessor:
    """增强的DD-B数据处理器"""
    
    def __init__(
        self,
        rdb_client: Any,
        vdb_client: Any = None,
        embedding_client: Any = None,
        enable_concurrency: bool = True,
        max_llm_concurrent: int = 15,
        concurrency_scope: ConcurrencyScope = ConcurrencyScope.HYBRID,
        instance_id: str = None
    ):
        """
        初始化增强数据处理器

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
            enable_concurrency: 是否启用并发处理
            max_llm_concurrent: 最大LLM并发数
            concurrency_scope: 并发控制作用域
            instance_id: 实例ID
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.enable_concurrency = enable_concurrency
        self.max_llm_concurrent = max_llm_concurrent
        self.concurrency_scope = concurrency_scope
        self.instance_id = instance_id or f"enhanced_processor_{id(self)}"
        self.table_name = DDBConstants.TABLE_NAME
        
        # 初始化核心组件
        self.field_filler = FieldFiller()
        
        # 初始化历史连接器（如果有向量数据库客户端）
        self.history_connector = None
        if vdb_client:
            self.history_connector = HistoryConnector(rdb_client, vdb_client, embedding_client)
            logger.info("历史连接器已启用")
        else:
            logger.info("历史连接器未启用（缺少向量数据库客户端）")
        
        # 初始化全局并发控制管理器
        concurrency_config = ConcurrencyConfig(
            scope=concurrency_scope,
            max_llm_concurrent_global=max_llm_concurrent,
            max_llm_concurrent_instance=max(1, max_llm_concurrent // 3)  # 实例级别为全局的1/3
        )
        self.global_concurrency_manager = get_global_concurrency_manager(concurrency_config)

        # 初始化大模型生成器
        self.llm_generator = create_llm_generator(max_llm_concurrent)
        self.field_generation_integrator = FieldGenerationIntegrator(self.llm_generator)

        # 初始化Pipeline集成器
        self.pipeline_integrator = None
        if vdb_client:
            self.pipeline_integrator = create_pipeline_integrator(rdb_client, vdb_client, embedding_client)
            logger.info("Pipeline集成器已启用")
        else:
            logger.info("Pipeline集成器未启用（缺少向量数据库客户端）")
        
        # 初始化并发处理器
        self.concurrent_processor = None
        if enable_concurrency:
            self.concurrent_processor = create_concurrent_processor(
                max_llm_concurrent=max_llm_concurrent
            )
            logger.info(f"并发处理器已启用: 最大LLM并发={max_llm_concurrent}")
        else:
            logger.info("并发处理器未启用")
        
        # 注册到全局并发管理器
        self.global_concurrency_manager.register_instance(
            self.instance_id,
            {
                "max_llm_concurrent": max_llm_concurrent,
                "enable_concurrency": enable_concurrency,
                "concurrency_scope": concurrency_scope.value
            }
        )

        logger.info(f"增强数据处理器初始化完成: instance_id={self.instance_id}, "
                   f"并发作用域={concurrency_scope.value}")
        logger.info(f"配置详情: LLM并发={max_llm_concurrent}, 启用并发={enable_concurrency}")
        logger.info(f"并发作用域说明: {concurrency_scope.value} - "
                   f"{'全局+实例级别混合控制，支持跨实例资源共享和隔离' if concurrency_scope.value == 'hybrid' else '其他模式'}")
        logger.info(f"实例ID说明: {self.instance_id} - 用于唯一标识此处理器实例，便于并发管理和监控")
    
    async def start(self):
        """启动处理器"""
        if self.concurrent_processor:
            await self.concurrent_processor.start()

        # 启动全局并发监控
        await self.global_concurrency_manager.start_monitoring()

        logger.info(f"增强数据处理器已启动: instance_id={self.instance_id}")
    
    async def stop(self):
        """停止处理器"""
        if self.concurrent_processor:
            await self.concurrent_processor.stop()

        # 停止全局并发监控
        await self.global_concurrency_manager.stop_monitoring()

        # 从全局并发管理器注销
        self.global_concurrency_manager.unregister_instance(self.instance_id)

        logger.info(f"增强数据处理器已停止: instance_id={self.instance_id}")
    
    @handle_async_ddb_errors
    async def query_records(self, request: DDBProcessRequest) -> List[DDBRecord]:
        """
        查询记录（优化的批量查询版本）

        Args:
            request: 处理请求

        Returns:
            查询到的记录列表
        """
        try:
            # 构建查询条件
            conditions = DDBUtils.build_query_conditions(
                request.report_code,
                request.dept_id
            )

            logger.info(f"查询条件: {conditions}")

            # 优先使用DD CRUD的批量查询方法
            try:
                records = await self._query_records_with_dd_crud(conditions)
                if records:
                    logger.info(f"DD CRUD批量查询成功，找到 {len(records)} 条记录")
                    return records
            except Exception as e:
                logger.warning(f"DD CRUD查询失败，降级到通用方法: {e}")

            # 降级到通用批量查询方法
            records = await self._query_records_with_batch_query(conditions)
            logger.info(f"通用批量查询完成，找到 {len(records)} 条记录")

            if not records:
                raise create_data_not_found_error(
                    f"未找到匹配的记录",
                    query_conditions=conditions
                )

            return records

        except Exception as e:
            if isinstance(e, (DDBDatabaseError, DDBDataNotFoundError)):
                raise

            logger.error(f"查询记录时发生错误: {e}")
            raise create_database_error(
                f"查询记录失败: {str(e)}",
                operation="select",
                table_name=self.table_name
            )

    async def _query_records_with_dd_crud(self, conditions: Dict[str, Any]) -> List[DDBRecord]:
        """使用DD CRUD的优化批量查询方法"""
        try:
            # 导入DD CRUD
            from modules.knowledge.dd.crud import DDCrud

            # 创建CRUD实例
            dd_crud = DDCrud(rdb_client=self.rdb_client)

            # 使用新的批量查询方法
            conditions_list = [conditions]  # 将单个条件包装为列表

            logger.info(f"使用DD CRUD批量查询: 条件={conditions}")

            raw_records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions_list,
                batch_size=50,  # 优化的批次大小
                max_concurrency=3,  # 适中的并发数
                timeout_per_batch=120.0  # 2分钟超时
            )

            # 转换为DDBRecord对象
            records = []
            conversion_errors = 0

            for raw_record in raw_records:
                try:
                    record = self._convert_row_to_record(raw_record)
                    records.append(record)
                except Exception as e:
                    conversion_errors += 1
                    logger.warning(f"转换记录失败，跳过: {e}")
                    continue

            if conversion_errors > 0:
                logger.warning(f"记录转换统计: 成功={len(records)}, 失败={conversion_errors}")

            logger.info(f"DD CRUD批量查询完成: 原始记录={len(raw_records)}, 转换成功={len(records)}")
            return records

        except Exception as e:
            logger.error(f"DD CRUD批量查询失败: {e}")
            raise

    async def _query_records_with_batch_query(self, conditions: Dict[str, Any]) -> List[DDBRecord]:
        """使用通用批量查询方法（降级方案）"""
        try:
            # 使用UniversalSQLAlchemyClient的abatch_query方法
            queries = [
                {
                    "data": ["*"],  # 查询所有字段
                    "filters": conditions,
                    "limit": DDBConstants.DEFAULT_QUERY_LIMIT
                }
            ]

            logger.info(f"执行批量查询: table={self.table_name}, 查询数量=1")

            # 执行批量查询，优化参数
            batch_results = await self.rdb_client.abatch_query(
                table=self.table_name,
                queries=queries,
                batch_size=1,  # 单个查询
                max_concurrency=1,  # 单个查询不需要并发
                timeout_per_batch=60.0  # 60秒超时
            )

            # 处理查询结果
            records = []
            if batch_results and len(batch_results) > 0:
                query_response = batch_results[0]
                if hasattr(query_response, 'data') and query_response.data:
                    for row_data in query_response.data:
                        try:
                            record = self._convert_row_to_record(row_data)
                            records.append(record)
                        except Exception as e:
                            logger.warning(f"转换记录失败，跳过: {e}")
                            continue
                elif hasattr(query_response, 'success') and not query_response.success:
                    raise create_database_error(
                        f"批量查询失败: {getattr(query_response, 'error_message', '未知错误')}",
                        operation="abatch_query",
                        table_name=self.table_name
                    )

            logger.info(f"批量查询性能统计: 查询1个批次, 转换{len(records)}条记录")
            return records

        except Exception as e:
            logger.error(f"通用批量查询失败: {e}")
            raise
    
    def _convert_row_to_record(self, row) -> DDBRecord:
        """将数据库行转换为DDBRecord对象"""
        try:
            # 处理不同类型的数据库返回结果
            if hasattr(row, '__dict__'):
                # SQLAlchemy对象，转换为字典
                row_dict = {k: v for k, v in row.__dict__.items() if not k.startswith('_')}
            elif isinstance(row, dict):
                # 已经是字典
                row_dict = row
            else:
                # 尝试转换为字典
                try:
                    row_dict = dict(row)
                except:
                    logger.error(f"无法转换行数据为字典: {type(row)}, {row}")
                    raise ValueError(f"不支持的行数据类型: {type(row)}")

            return DDBRecord(
                id=row_dict.get('id'),
                # 关键修复：数据库使用version字段，需要映射到report_code
                report_code=row_dict.get('version') or row_dict.get('report_code'),
                dept_id=row_dict.get('dept_id'),
                pre_distribution_id=row_dict.get('pre_distribution_id'),
                # 统一使用小写字段名，与数据库字段一致
                dr01=row_dict.get('dr01'),
                bdr09=row_dict.get('bdr09'),
                bdr10=row_dict.get('bdr10'),
                bdr11=row_dict.get('bdr11'),
                bdr16=row_dict.get('bdr16'),
                bdr05=row_dict.get('bdr05'),
                bdr06=row_dict.get('bdr06'),
                bdr07=row_dict.get('bdr07'),
                bdr08=row_dict.get('bdr08'),
                bdr12=row_dict.get('bdr12'),
                bdr13=row_dict.get('bdr13'),
                bdr14=row_dict.get('bdr14'),
                bdr15=row_dict.get('bdr15'),
                bdr17=row_dict.get('bdr17'),
                # SDR字段也使用小写
                sdr01=row_dict.get('sdr01'),
                sdr02=row_dict.get('sdr02'),
                sdr03=row_dict.get('sdr03'),
                sdr04=row_dict.get('sdr04'),
                sdr05=row_dict.get('sdr05'),
                sdr06=row_dict.get('sdr06'),
                sdr07=row_dict.get('sdr07'),
                sdr08=row_dict.get('sdr08'),
                sdr09=row_dict.get('sdr09'),
                sdr10=row_dict.get('sdr10'),
                sdr11=row_dict.get('sdr11'),
                sdr12=row_dict.get('sdr12'),
                sdr13=row_dict.get('sdr13'),
                sdr14=row_dict.get('sdr14'),
                sdr15=row_dict.get('sdr15'),
                create_time=row_dict.get('create_time'),
                update_time=row_dict.get('update_time')
            )
        except Exception as e:
            logger.error(f"转换数据库行失败: {e}, 行数据: {row}")
            raise
    
    @handle_async_ddb_errors
    async def process_records(self, request: DDBProcessRequest) -> DDBProcessResult:
        """
        处理记录（增强版本）
        
        Args:
            request: 处理请求
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始增强处理请求: report_code={request.report_code}, dept_id={request.dept_id}")
            
            # 1. 查询记录
            original_records = await self.query_records(request)
            
            # 2. 分析记录状态
            analysis = self._analyze_records(original_records)
            
            # 3. 历史信息提取和生成决策（增强版本）
            generation_decisions = []
            if self.history_connector:
                logger.info("执行增强的历史信息提取和生成决策")
                
                if self.enable_concurrency and len(original_records) > 1:
                    # 并发处理多条记录
                    generation_decisions = await self._concurrent_extract_and_decide(original_records)
                else:
                    # 串行处理
                    for record in original_records:
                        try:
                            decision = await self.history_connector.extract_and_decide(record)
                            generation_decisions.append(decision)
                            logger.debug(f"记录 {record.id} 决策: {decision.strategy.value}")
                        except Exception as e:
                            logger.error(f"记录 {record.id} 决策失败: {e}")
                            # 默认决策
                            default_decision = GenerationDecision(
                                record_id=record.id,
                                strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                                confidence_score=0.0,
                                fields_to_generate=record.get_main_generation_fields(),
                                decision_notes=[f"决策失败: {str(e)}", "采用默认低置信度策略"]
                            )
                            generation_decisions.append(default_decision)
            else:
                logger.info("历史连接器未启用，跳过历史信息提取")
            
            # 4. 根据决策结果处理数据（增强版本）
            if generation_decisions:
                processed_records, fill_details = await self._enhanced_process_with_generation_decisions(
                    original_records, generation_decisions, request
                )
            elif analysis["all_main_fields_complete"]:
                # 主要字段都完整，直接返回
                logger.info("所有记录的主要字段都完整，直接返回原始数据")
                processed_records = original_records
                fill_details = []
            else:
                # 需要填充处理（传统逻辑）
                logger.info(f"发现 {analysis['records_needing_fill']} 条记录需要填充")
                processed_records, fill_details = await self._process_with_filling(
                    original_records, request
                )
            
            # 5. 构建结果
            processing_time = (time.time() - start_time) * 1000
            
            result = DDBProcessResult(
                request=request,
                total_records_found=len(original_records),
                records_processed=len(processed_records),
                processed_records=processed_records,
                original_records=original_records if request.return_original_data else [],
                records_with_complete_main_fields=analysis["complete_main_fields_count"],
                records_requiring_fill=analysis["records_needing_fill"],
                total_fields_filled=len(fill_details),
                fill_details=fill_details,
                status=ProcessingStatusEnum.SUCCESS,
                message="增强处理完成",
                processing_time_ms=processing_time,
                processing_notes=[
                    f"查询到 {len(original_records)} 条记录",
                    f"主要字段完整的记录: {analysis['complete_main_fields_count']} 条",
                    f"需要填充的记录: {analysis['records_needing_fill']} 条",
                    f"总填充字段数: {len(fill_details)} 个",
                    f"使用增强处理器: {'是' if self.history_connector else '否'}",
                    f"并发处理: {'是' if self.enable_concurrency else '否'}"
                ]
            )
            
            logger.info(f"增强处理完成，耗时 {processing_time:.2f}ms")

            # 详细记录最终处理结果
            if processed_records:
                logger.info(f"📋 最终处理结果详情:")
                for record in processed_records:
                    logger.info(f"  📄 记录 {record.id} 最终状态:")

                    # 检查主要字段
                    main_fields = ['bdr09', 'bdr10', 'bdr11', 'bdr16']
                    for field in main_fields:
                        value = getattr(record, field, None)
                        if value:
                            display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                            logger.info(f"    ✅ {field}: '{display_value}'")
                        else:
                            logger.info(f"    ❌ {field}: 空值")

                    # 统计所有非空字段
                    all_fields = [attr for attr in dir(record) if not attr.startswith('_') and not callable(getattr(record, attr))]
                    non_empty_fields = []
                    for field in all_fields:
                        value = getattr(record, field, None)
                        if value is not None and str(value).strip():
                            non_empty_fields.append(field)

                    logger.info(f"    📊 字段统计: 总字段={len(all_fields)}, 非空字段={len(non_empty_fields)}")

                    # 显示BDR和SDR字段的完整信息
                    import json
                    bdr_sdr_fields = {}

                    # BDR字段 (bdr05-bdr17)
                    for i in range(5, 18):
                        field_name = f"bdr{i:02d}"
                        value = getattr(record, field_name, None)
                        if value is not None:
                            bdr_sdr_fields[field_name] = value

                    # SDR字段 (sdr01-sdr15)
                    for i in range(1, 16):
                        field_name = f"sdr{i:02d}"
                        value = getattr(record, field_name, None)
                        if value is not None:
                            bdr_sdr_fields[field_name] = value

                    logger.info(f"    📋 BDR/SDR字段详情 (record_id={record.id}):")
                    logger.info(f"    {json.dumps(bdr_sdr_fields, ensure_ascii=False, indent=4)}")

                    # 记录完整的记录JSON（用于调试）
                    record_dict = {}
                    for field in all_fields:
                        value = getattr(record, field, None)
                        if hasattr(value, 'isoformat'):  # datetime对象
                            record_dict[field] = value.isoformat()
                        else:
                            record_dict[field] = value

                    logger.debug(f"    🔍 完整记录JSON: {json.dumps(record_dict, ensure_ascii=False, indent=2)}")

            logger.info(f"📊 处理总结: 处理记录数={len(processed_records)}, 总填充字段数={len(fill_details)}")
            
            return result
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            if isinstance(e, (DDBDatabaseError, DDBDataNotFoundError)):
                # 数据库或数据未找到错误
                status = ProcessingStatusEnum.FAILED
                message = str(e)
            else:
                # 其他处理错误
                logger.error(f"增强处理记录时发生错误: {e}")
                status = ProcessingStatusEnum.FAILED
                message = f"增强处理失败: {str(e)}"
            
            # 返回失败结果
            return DDBProcessResult(
                request=request,
                total_records_found=0,
                records_processed=0,
                processed_records=[],
                status=status,
                message=message,
                processing_time_ms=processing_time,
                processing_notes=[f"增强处理失败: {str(e)}"]
            )
    
    async def _concurrent_extract_and_decide(
        self, 
        records: List[DDBRecord]
    ) -> List[GenerationDecision]:
        """
        并发执行历史信息提取和决策
        
        Args:
            records: 记录列表
            
        Returns:
            决策结果列表
        """
        logger.info(f"开始并发历史信息提取和决策: {len(records)} 条记录")
        
        # 创建并发任务
        tasks = []
        for record in records:
            task = self.history_connector.extract_and_decide(record)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        decisions = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"并发决策中第 {i} 个记录失败: {result}")
                # 创建默认决策
                default_decision = GenerationDecision(
                    record_id=records[i].id,
                    strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                    confidence_score=0.0,
                    fields_to_generate=records[i].get_main_generation_fields(),
                    decision_notes=[f"并发决策异常: {str(result)}", "采用默认低置信度策略"]
                )
                decisions.append(default_decision)
            else:
                decisions.append(result)
        
        logger.info(f"并发历史信息提取和决策完成: {len(decisions)} 个决策")
        
        return decisions
    
    async def _enhanced_process_with_generation_decisions(
        self,
        records: List[DDBRecord],
        decisions: List[GenerationDecision],
        request: DDBProcessRequest
    ) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
        """
        基于生成决策处理记录（增强版本，集成大模型生成）
        
        Args:
            records: 原始记录列表
            decisions: 生成决策列表
            request: 处理请求
            
        Returns:
            (处理后的记录列表, 填充详情列表)
        """
        processed_records = []
        all_fill_details = []
        
        # 分类处理不同策略的记录
        llm_generation_tasks = []  # 需要LLM生成的任务
        
        for record, decision in zip(records, decisions):
            try:
                if decision.strategy == GenerationStrategyEnum.DIRECT_RETURN:
                    # 直接返回策略：主要字段完整，无需处理
                    processed_records.append(record)
                    logger.debug(f"记录 {record.id}: 直接返回策略")
                
                elif decision.strategy == GenerationStrategyEnum.HIGH_CONFIDENCE:
                    # 高置信度策略：使用历史数据完全填充
                    filled_record, fill_details = await self._fill_from_history_data(
                        record, decision
                    )
                    processed_records.append(filled_record)
                    all_fill_details.extend(fill_details)
                    logger.debug(f"记录 {record.id}: 高置信度策略，填充 {len(fill_details)} 个字段")
                
                elif decision.strategy == GenerationStrategyEnum.LOW_CONFIDENCE:
                    # 低置信度策略：需要LLM生成或应用默认值
                    if decision.fields_to_generate:
                        llm_generation_tasks.append((record, decision))
                    else:
                        # 无需生成字段，检查是否有历史数据决定是否应用默认值
                        has_historical_data = (decision.history_search_result and
                                             decision.history_search_result.total_results > 0)

                        if has_historical_data:
                            # 有历史数据，按传统方式处理
                            filled_record, fill_details = self.field_filler.fill_record_fields(record)
                        else:
                            # 无历史数据，应用默认值
                            filled_record, default_fill_details = self._apply_default_values_for_low_confidence(
                                record, has_historical_data=False
                            )
                            # 再进行传统填充处理其他字段
                            final_record, traditional_fill_details = self.field_filler.fill_record_fields(filled_record)
                            filled_record = final_record
                            fill_details = default_fill_details + traditional_fill_details

                        processed_records.append(filled_record)
                        all_fill_details.extend(fill_details)
                
                else:
                    # 未知策略，使用默认处理
                    logger.warning(f"记录 {record.id}: 未知策略 {decision.strategy}，使用默认处理")
                    filled_record, fill_details = self.field_filler.fill_record_fields(record)
                    processed_records.append(filled_record)
                    all_fill_details.extend(fill_details)
                
            except Exception as e:
                logger.error(f"处理记录 {record.id} 失败: {e}")
                # 失败时保留原始记录
                processed_records.append(record)
        
        # 处理需要LLM生成的记录
        if llm_generation_tasks:
            logger.info(f"开始LLM生成处理: {len(llm_generation_tasks)} 条记录")
            
            if self.enable_concurrency and len(llm_generation_tasks) > 1:
                # 并发LLM生成
                llm_results = await self._concurrent_llm_generation(llm_generation_tasks, request)
            else:
                # 串行LLM生成
                llm_results = await self._sequential_llm_generation(llm_generation_tasks, request)
            
            processed_records.extend([result[0] for result in llm_results])
            all_fill_details.extend([detail for result in llm_results for detail in result[1]])
        
        logger.info(f"增强处理完成: 处理 {len(records)} 条记录，填充 {len(all_fill_details)} 个字段")
        
        return processed_records, all_fill_details
    
    async def _concurrent_llm_generation(
        self,
        llm_tasks: List[Tuple[DDBRecord, GenerationDecision]],
        request: DDBProcessRequest
    ) -> List[Tuple[DDBRecord, List[FieldFillInfo]]]:
        """并发LLM生成"""
        logger.info(f"开始并发LLM生成: {len(llm_tasks)} 个任务")
        
        # 创建并发任务
        tasks = []
        for record, decision in llm_tasks:
            task = self._process_single_llm_generation(record, decision, request)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"并发LLM生成中第 {i} 个任务失败: {result}")
                # 使用原始记录
                record, _ = llm_tasks[i]
                processed_results.append((record, []))
            else:
                processed_results.append(result)
        
        logger.info(f"并发LLM生成完成: {len(processed_results)} 个结果")
        
        return processed_results
    
    async def _sequential_llm_generation(
        self,
        llm_tasks: List[Tuple[DDBRecord, GenerationDecision]],
        request: DDBProcessRequest
    ) -> List[Tuple[DDBRecord, List[FieldFillInfo]]]:
        """串行LLM生成"""
        logger.info(f"开始串行LLM生成: {len(llm_tasks)} 个任务")
        
        results = []
        for record, decision in llm_tasks:
            try:
                result = await self._process_single_llm_generation(record, decision, request)
                results.append(result)
            except Exception as e:
                logger.error(f"串行LLM生成失败: record_id={record.id}, error={e}")
                results.append((record, []))
        
        logger.info(f"串行LLM生成完成: {len(results)} 个结果")
        
        return results
    
    async def _process_single_llm_generation(
        self,
        record: DDBRecord,
        decision: GenerationDecision,
        request: DDBProcessRequest
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """处理单个LLM生成任务（集成Pipeline调用）"""
        from copy import deepcopy

        # 获取全局并发许可
        llm_permit_acquired = await self.global_concurrency_manager.acquire_llm_permit(self.instance_id)
        if not llm_permit_acquired:
            logger.warning(f"获取LLM并发许可失败: record_id={record.id}")
            # 降级到传统填充
            return await self._fallback_to_traditional_fill(record, request)

        try:
            updated_record = deepcopy(record)
            all_fill_details = []

            # 统一使用Pipeline处理所有情况
            logger.info(f"开始Pipeline处理: record_id={record.id}, strategy={decision.strategy.value}")

            # 执行Pipeline处理
            updated_record, pipeline_fill_details = await self._execute_pipeline_processing(
                updated_record, decision, request
            )
            all_fill_details.extend(pipeline_fill_details)

            # 强制执行字段填充（移除enable_auto_fill条件判断）
            logger.debug(f"开始强制字段填充: record_id={record.id}")

            final_record, default_fill_details = self.field_filler.fill_record_fields(updated_record)

            logger.debug(f"字段填充完成: record_id={record.id}, 填充字段数={len(default_fill_details)}")

            # 过滤掉已生成的字段的填充信息
            generated_field_names = {info.field_name for info in all_fill_details}
            filtered_fill_details = [
                info for info in default_fill_details
                if info.field_name not in generated_field_names
            ]

            logger.debug(f"字段过滤结果: record_id={record.id}, "
                       f"原始填充={len(default_fill_details)}, "
                       f"已生成字段={len(generated_field_names)}, "
                       f"最终填充={len(filtered_fill_details)}")

            all_fill_details.extend(filtered_fill_details)

            # 释放全局并发许可
            self.global_concurrency_manager.release_llm_permit(self.instance_id, success=True)

            return final_record, all_fill_details

        except Exception as e:
            # 释放全局并发许可
            self.global_concurrency_manager.release_llm_permit(self.instance_id, success=False)

            logger.error(f"LLM生成处理失败: record_id={record.id}, error={e}")
            # 降级到传统填充
            return await self._fallback_to_traditional_fill(record, request)
    
    async def _fill_from_history_data(
        self,
        record: DDBRecord,
        decision: GenerationDecision
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """从历史数据填充记录（高置信度场景：直接使用top1向量搜索结果）"""
        from copy import deepcopy

        filled_record = deepcopy(record)
        fill_details = []

        if not decision.history_search_result or not decision.history_search_result.all_results:
            logger.warning(f"记录 {record.id}: 高置信度策略但无向量搜索结果")
            return filled_record, fill_details

        try:
            # 获取top1向量搜索结果
            top1_result = decision.history_search_result.all_results[0]
            data_row_id = top1_result.get("data_row_id")

            if not data_row_id:
                logger.warning(f"记录 {record.id}: 向量搜索结果缺少data_row_id")
                return filled_record, fill_details

            logger.info(f"高置信度场景：使用data_row_id={data_row_id}查询dd_submission_data")

            # 通过data_row_id查询dd_submission_data表获取完整历史数据
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(rdb_client=self.rdb_client)

            # 查询dd_submission_data表（使用主键ID）
            history_data = await dd_crud.get_submission_data(submission_pk=data_row_id)

            if not history_data:
                logger.warning(f"记录 {record.id}: 未找到data_row_id={data_row_id}的历史数据")
                return filled_record, fill_details

            logger.info(f"成功获取历史数据: data_row_id={data_row_id}")

            # 定义需要填充的所有字段（注意：记录对象使用小写字段名）
            all_fields_to_fill = [
                # BDR字段 (bdr05-bdr17)
                "bdr05", "bdr06", "bdr07", "bdr08", "bdr09", "bdr10", "bdr11",
                "bdr12", "bdr13", "bdr14", "bdr15", "bdr16", "bdr17",
                # SDR字段 (sdr01-sdr15)
                "sdr01", "sdr02", "sdr03", "sdr04", "sdr05", "sdr06", "sdr07",
                "sdr08", "sdr09", "sdr10", "sdr11", "sdr12", "sdr13", "sdr14", "sdr15"
            ]

            # 填充历史数据中存在的字段
            for field_name in all_fields_to_fill:
                if hasattr(filled_record, field_name):
                    original_value = getattr(filled_record, field_name)
                    history_value = history_data.get(field_name)  # 数据库字段也是小写

                    if history_value is not None:
                        # 使用历史数据填充
                        setattr(filled_record, field_name, history_value)

                        fill_info = FieldFillInfo(
                            field_name=field_name,
                            original_value=original_value,
                            filled_value=history_value,
                            status=FieldStatusEnum.FILLED,
                            fill_reason=f"高置信度历史数据填充 (置信度: {decision.confidence_score:.3f})"
                        )
                        fill_details.append(fill_info)

                        logger.debug(f"字段 {field_name}: '{original_value}' -> '{history_value}' (历史数据)")

            # 高置信度策略：不检查空字段，不应用默认值
            # 即使历史数据中某些字段为空，也直接使用历史数据的值（包括空值）

            logger.info(f"高置信度历史数据填充完成: record_id={record.id}, "
                       f"填充字段数={len(fill_details)} (包含BDR和SDR字段)")

            return filled_record, fill_details

        except Exception as e:
            logger.error(f"高置信度历史数据填充失败: record_id={record.id}, error={e}")
            return filled_record, fill_details
    
    async def _process_with_filling(
        self, 
        records: List[DDBRecord], 
        request: DDBProcessRequest
    ) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
        """传统填充处理（强制执行字段填充）"""
        logger.info("执行强制字段填充")

        # 执行字段填充（移除enable_auto_fill条件）
        filled_records, fill_details = self.field_filler.batch_fill_records(records)
        
        logger.info(f"传统填充处理完成: 填充了 {len(fill_details)} 个字段")
        
        return filled_records, fill_details
    
    def _analyze_records(self, records: List[DDBRecord]) -> Dict[str, Any]:
        """分析记录状态（复用原有逻辑）"""
        complete_count = 0
        needing_fill_count = 0
        
        for record in records:
            if record.is_main_fields_complete():
                complete_count += 1
            else:
                needing_fill_count += 1
        
        analysis = {
            "total_records": len(records),
            "complete_main_fields_count": complete_count,
            "records_needing_fill": needing_fill_count,
            "all_main_fields_complete": needing_fill_count == 0
        }
        
        logger.debug(f"记录分析结果: {analysis}")
        
        return analysis
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            "processor_type": "enhanced",
            "history_connector_enabled": self.history_connector is not None,
            "concurrency_enabled": self.enable_concurrency,
            "max_llm_concurrent": self.max_llm_concurrent
        }
        
        # 添加LLM生成器统计
        if self.llm_generator:
            stats["llm_stats"] = self.llm_generator.get_concurrency_stats()
        
        # 添加并发处理器统计
        if self.concurrent_processor:
            stats["concurrent_stats"] = self.concurrent_processor.get_stats()
        
        return stats

    async def _get_table_ids_for_dept(self, dept_id: str) -> List[int]:
        """
        获取部门对应的table_ids（真实实现）

        Args:
            dept_id: 部门ID

        Returns:
            List[int]: 表ID列表（整数类型）
        """
        try:
            # 使用DD CRUD查询部门关联关系
            from modules.knowledge.dd.crud import DDCrud

            # 创建DD CRUD实例
            dd_crud = DDCrud(self.rdb_client, self.vdb_client, self.embedding_client)

            # 查询部门关联关系
            relations = await dd_crud.list_department_relations_by_dept(dept_id)

            # 提取table_id列表（确保为整数类型）
            table_ids = []
            for relation in relations:
                table_id = relation.get('table_id')
                if table_id is not None:
                    # 确保转换为整数
                    if isinstance(table_id, str) and table_id.isdigit():
                        table_ids.append(int(table_id))
                    elif isinstance(table_id, int):
                        table_ids.append(table_id)
                    else:
                        logger.warning(f"无效的table_id格式: {table_id}, 类型: {type(table_id)}")

            # 去重并排序
            table_ids = sorted(list(set(table_ids)))

            logger.debug(f"获取部门table_ids: dept_id={dept_id}, table_ids={table_ids}")

            if not table_ids:
                logger.warning(f"部门 {dept_id} 没有关联的表，使用默认表ID")
                # 返回默认表ID（如果没有关联关系）
                return [1]  # 默认表ID为1

            return table_ids

        except Exception as e:
            logger.error(f"查询部门关联关系失败: dept_id={dept_id}, error={e}")
            # 降级到默认表ID
            return [1]

    def _apply_pipeline_field_mapping(
        self,
        record: DDBRecord,
        field_mapping: FieldMappingResult,
        decision: GenerationDecision
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """应用Pipeline字段映射结果"""
        from copy import deepcopy

        updated_record = deepcopy(record)
        fill_details = []

        # 映射BDR字段（注意字段名大小写转换）
        bdr_mappings = {
            "bdr09": field_mapping.BDR09,  # 记录对象使用小写字段名
            "bdr10": field_mapping.BDR10,
            "bdr11": field_mapping.BDR11,
            "bdr16": field_mapping.BDR16
        }

        for field_name, new_value in bdr_mappings.items():
            if new_value and hasattr(updated_record, field_name):
                original_value = getattr(updated_record, field_name)
                setattr(updated_record, field_name, new_value)

                fill_info = FieldFillInfo(
                    field_name=field_name,
                    original_value=original_value,
                    filled_value=new_value,
                    status=FieldStatusEnum.FILLED,
                    fill_reason="Pipeline生成"
                )
                fill_details.append(fill_info)

        # 映射SDR字段（如果记录支持，注意字段名大小写转换）
        sdr_mappings = {
            "sdr01": field_mapping.SDR01,  # 记录对象使用小写字段名
            "sdr02": field_mapping.SDR02,
            "sdr03": field_mapping.SDR03,
            "sdr04": field_mapping.SDR04,
            "sdr05": field_mapping.SDR05,
            "sdr06": field_mapping.SDR06,
            "sdr07": field_mapping.SDR07,
            "sdr08": field_mapping.SDR08,
            "sdr09": field_mapping.SDR09,
            "sdr10": field_mapping.SDR10,
            "sdr11": field_mapping.SDR11,
            "sdr12": field_mapping.SDR12,
            "sdr13": field_mapping.SDR13,
            "sdr14": field_mapping.SDR14,
            "sdr15": field_mapping.SDR15
        }

        for field_name, new_value in sdr_mappings.items():
            if new_value and hasattr(updated_record, field_name):
                original_value = getattr(updated_record, field_name)
                setattr(updated_record, field_name, new_value)

                fill_info = FieldFillInfo(
                    field_name=field_name,
                    original_value=original_value,
                    filled_value=new_value,
                    status=FieldStatusEnum.FILLED,
                    fill_reason="Pipeline生成（SDR字段）"
                )
                fill_details.append(fill_info)

        logger.debug(f"Pipeline字段映射完成: record_id={record.id}, 填充字段数={len(fill_details)}")

        return updated_record, fill_details

    async def _execute_pipeline_processing(
        self,
        record: DDBRecord,
        decision: GenerationDecision,
        request: DDBProcessRequest
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        执行Pipeline处理（统一处理方法）

        Args:
            record: 待处理的记录
            decision: 生成决策
            request: 处理请求

        Returns:
            (更新后的记录, 填充信息列表)
        """
        all_fill_details = []
        updated_record = record

        try:
            # 1. 尝试Pipeline生成（如果有Pipeline集成器和历史信息）
            if (self.pipeline_integrator and
                hasattr(record, 'pre_distribution_id') and
                record.pre_distribution_id and
                decision.history_search_result):

                logger.info(f"执行Pipeline生成: record_id={record.id}")

                # 提取历史信息
                dr09 = decision.history_search_result.query_dr09
                dr17 = decision.history_search_result.query_dr17

                # 获取table_ids
                table_ids = await self._get_table_ids_for_dept(record.dept_id)

                # 执行Pipeline并映射字段
                pipeline_result, field_mapping = await self.pipeline_integrator.execute_pipeline_and_map_fields(
                    record, table_ids, dr09, dr17
                )

                if pipeline_result.execution_success and field_mapping.mapping_success:
                    # 使用Pipeline结果填充字段
                    updated_record, pipeline_fill_details = self._apply_pipeline_field_mapping(
                        updated_record, field_mapping, decision
                    )
                    all_fill_details.extend(pipeline_fill_details)

                    logger.info(f"Pipeline生成成功: record_id={record.id}, "
                               f"填充字段数={len(pipeline_fill_details)}")
                else:
                    logger.warning(f"Pipeline执行失败，使用LLM生成主要字段: record_id={record.id}")
                    # Pipeline失败时，只生成主要字段
                    updated_record, llm_fill_details = await self._generate_main_fields_only(
                        updated_record, decision
                    )
                    all_fill_details.extend(llm_fill_details)
            else:
                # 没有Pipeline条件时，生成主要字段
                logger.info(f"Pipeline条件不满足，生成主要字段: record_id={record.id}")
                updated_record, llm_fill_details = await self._generate_main_fields_only(
                    updated_record, decision
                )
                all_fill_details.extend(llm_fill_details)

        except Exception as e:
            logger.error(f"Pipeline处理异常，生成主要字段: record_id={record.id}, error={e}")
            # 异常时，生成主要字段
            updated_record, llm_fill_details = await self._generate_main_fields_only(
                updated_record, decision
            )
            all_fill_details.extend(llm_fill_details)

        return updated_record, all_fill_details

    async def _generate_main_fields_only(
        self,
        record: DDBRecord,
        decision: GenerationDecision
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        仅生成主要字段（bdr09, bdr10, bdr11, bdr16）

        Args:
            record: 待处理的记录
            decision: 生成决策

        Returns:
            (更新后的记录, 填充信息列表)
        """
        try:
            # 构建上下文数据
            context_data = {
                "report_code": record.report_code,
                "dept_id": record.dept_id,
                "pre_distribution_id": record.pre_distribution_id,
                "confidence_score": decision.confidence_score,
                "strategy": decision.strategy.value
            }

            # 只生成主要字段
            main_fields = ["bdr09", "bdr10", "bdr11", "bdr16"]

            # 执行LLM生成
            generated_record, generation_fill_details = await self.field_generation_integrator.integrate_generated_fields(
                record, main_fields, context_data
            )

            logger.info(f"主要字段生成完成: record_id={record.id}, "
                       f"生成字段数={len(generation_fill_details)}")

            return generated_record, generation_fill_details

        except Exception as e:
            logger.error(f"主要字段生成失败: record_id={record.id}, error={e}")
            return record, []

    async def _fallback_to_traditional_fill(
        self,
        record: DDBRecord,
        request: DDBProcessRequest
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """降级到传统填充方法（强制执行字段填充）"""
        # 强制执行字段填充（移除enable_auto_fill条件）
        filled_record, fill_details = self.field_filler.fill_record_fields(record)
        return filled_record, fill_details

    def _apply_default_values_for_low_confidence(
        self,
        record: DDBRecord,
        has_historical_data: bool = False
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        为低置信度策略且无历史数据的情况应用默认值

        Args:
            record: 记录
            has_historical_data: 是否有历史数据

        Returns:
            (更新后的记录, 填充信息列表)
        """
        from copy import deepcopy

        updated_record = deepcopy(record)
        fill_details = []

        # 只有在低置信度且无历史数据时才应用默认值
        if has_historical_data:
            logger.debug(f"有历史数据，跳过默认值应用: record_id={record.id}")
            return updated_record, fill_details

        # BDR字段默认值（统一使用小写字段名）
        bdr_defaults = {
            "bdr05": "",        # bdr05默认为空
            "bdr06": "不适用",
            "bdr07": "1",
            "bdr08": "CRD",
            "bdr12": "不适用",
            "bdr13": "不适用",
            "bdr14": "不适用",
            "bdr15": "不适用",
            "bdr17": "不适用"
        }

        # SDR字段默认值（统一使用小写字段名）
        sdr_defaults = {
            "sdr02": "1",
            "sdr03": "CRD",
            "sdr04": "不适用",
            "sdr07": "不适用",
            "sdr11": "不适用",
            "sdr13": "不适用",
            "sdr14": "不适用",
            "sdr15": ""
        }

        # 合并所有默认值
        all_defaults = {**bdr_defaults, **sdr_defaults}

        # 应用默认值到空字段
        for field_name, default_value in all_defaults.items():
            if hasattr(updated_record, field_name):
                current_value = getattr(updated_record, field_name)
                if not current_value:  # 如果字段为空，设置默认值
                    setattr(updated_record, field_name, default_value)

                    fill_info = FieldFillInfo(
                        field_name=field_name,
                        original_value=current_value,
                        filled_value=default_value,
                        status=FieldStatusEnum.FILLED,
                        fill_reason="低置信度默认值（无历史数据）"
                    )
                    fill_details.append(fill_info)

                    logger.debug(f"默认值填充 {field_name}: {current_value} -> {default_value}")

        logger.info(f"低置信度默认值填充完成: record_id={record.id}, "
                   f"填充字段数={len(fill_details)}")

        return updated_record, fill_details

    async def process_records_in_batches(
        self,
        request: DDBProcessRequest,
        batch_size: int = 15,
        use_message_queue: bool = False
    ) -> DDBProcessResult:
        """
        批量处理记录（支持数千条记录）

        Args:
            request: 处理请求
            batch_size: 批量大小（默认15）
            use_message_queue: 是否使用消息队列

        Returns:
            处理结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始批量处理: batch_size={batch_size}, use_queue={use_message_queue}")

            # 1. 查询所有记录
            all_records = await self.query_records(request)
            total_records = len(all_records)

            if total_records == 0:
                return DDBProcessResult(
                    request=request,
                    total_records_found=0,
                    records_processed=0,
                    processed_records=[],
                    status=ProcessingStatusEnum.SUCCESS,
                    message="无记录需要处理",
                    processing_time_ms=(time.time() - start_time) * 1000
                )

            logger.info(f"查询到 {total_records} 条记录，将分 {(total_records + batch_size - 1) // batch_size} 批处理")

            # 2. 分批处理
            if use_message_queue and total_records > 100:
                # 大量数据使用消息队列
                return await self._process_with_message_queue(all_records, request, batch_size, start_time)
            else:
                # 中小量数据直接批量处理
                return await self._process_with_direct_batching(all_records, request, batch_size, start_time)

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"批量处理失败: {e}")

            return DDBProcessResult(
                request=request,
                total_records_found=0,
                records_processed=0,
                processed_records=[],
                status=ProcessingStatusEnum.FAILED,
                message=f"批量处理失败: {str(e)}",
                processing_time_ms=processing_time
            )


# 便捷函数
def create_enhanced_data_processor(
    rdb_client: Any,
    vdb_client: Any = None,
    embedding_client: Any = None,
    enable_concurrency: bool = True,
    max_llm_concurrent: int = 15,
    concurrency_scope: ConcurrencyScope = ConcurrencyScope.HYBRID,
    instance_id: str = None
) -> EnhancedDataProcessor:
    """创建增强数据处理器实例"""
    return EnhancedDataProcessor(
        rdb_client,
        vdb_client,
        embedding_client,
        enable_concurrency,
        max_llm_concurrent,
        concurrency_scope,
        instance_id
    )


async def process_dd_b_request_enhanced(
    rdb_client: Any,
    request: DDBProcessRequest,
    vdb_client: Any = None,
    embedding_client: Any = None,
    enable_concurrency: bool = True,
    max_llm_concurrent: int = 15,
    concurrency_scope: ConcurrencyScope = ConcurrencyScope.HYBRID
) -> DDBProcessResult:
    """处理DD-B请求的增强便捷函数"""
    processor = create_enhanced_data_processor(
        rdb_client, vdb_client, embedding_client, enable_concurrency, max_llm_concurrent, concurrency_scope
    )
    
    # 启动处理器
    await processor.start()
    
    try:
        # 执行处理
        result = await processor.process_records(request)
        return result
    finally:
        # 停止处理器
        await processor.stop()
