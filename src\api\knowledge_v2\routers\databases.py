"""
Knowledge V2 API - 源数据库管理路由

统一的源数据库管理API，支持标准的CRUD操作模式
- GET: 根据ID或IDs查询
- POST: 创建（批量支持）
- PUT: 更新（批量支持）
- DELETE: 删除（批量支持）
- LIST: 列表查询（支持分页和过滤）
"""

import logging
from typing import List, Optional, Union
from fastapi import APIRouter, HTTPException, Depends, Query, Path
from datetime import datetime

from ..models.request_models import (
    SourceDatabaseCreate,
    SourceDatabaseUpdate,
    UnifiedGetRequest,
    UnifiedListRequest,
    BatchCreateRequest,
    BatchUpdateRequest,
    BatchDeleteRequest,
    BatchOperationConfig
)
from ..models.response_models import (
    SourceDatabaseResponse,
    UnifiedResponse,
    UnifiedListResponse,
    BatchOperationResponse,
    ErrorResponse
)
from ..dependencies import (
    get_metadata_crud,
    validate_batch_config,
    create_batch_stats,
    log_batch_operation,
    handle_api_errors,
    PerformanceMonitor
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/source-databases", tags=["源数据库管理V2"])


# ==================== GET 操作 ====================

@router.get("/{db_id}", response_model=UnifiedResponse, summary="根据ID获取源数据库")
@handle_api_errors
async def get_source_database_by_id(
    db_id: int = Path(..., description="数据库ID", gt=0, example=1),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤", example="kb_001"),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    根据ID获取源数据库信息

    - **db_id**: 数据库ID
    - **knowledge_id**: 知识库ID过滤（可选）
    """
    with PerformanceMonitor("获取源数据库") as monitor:
        try:
            # 构建查询条件
            conditions = {"db_id": db_id}
            if knowledge_id:
                conditions["knowledge_id"] = knowledge_id

            # 执行查询
            results = await metadata_crud.batch_query_source_databases([conditions])

            if not results or not results[0]:
                raise HTTPException(status_code=404, detail=f"未找到ID为 {db_id} 的源数据库")

            database = results[0][0]  # 取第一个结果的第一条记录

            return UnifiedResponse(
                success=True,
                message="获取源数据库成功",
                data=database,
                timestamp=datetime.now(),
                execution_time=monitor.execution_time
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取源数据库失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取源数据库失败: {str(e)}")


@router.post("/get", response_model=UnifiedResponse, summary="根据ID或IDs获取源数据库")
@handle_api_errors
async def get_source_databases(
    request: UnifiedGetRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    根据ID或IDs获取源数据库信息（支持批量查询）

    - **id**: 单个数据库ID
    - **ids**: 多个数据库ID列表
    - **knowledge_id**: 知识库ID过滤（可选）

    兼容性支持：可以传入单个ID或多个IDs
    """
    with PerformanceMonitor("批量获取源数据库") as monitor:
        try:
            # 构建ID列表
            if request.id is not None:
                db_ids = [request.id]
            else:
                db_ids = request.ids

            # 构建查询条件
            conditions = []
            for db_id in db_ids:
                condition = {"db_id": db_id}
                if request.knowledge_id:
                    condition["knowledge_id"] = request.knowledge_id
                conditions.append(condition)

            # 执行批量查询
            results = await metadata_crud.batch_query_source_databases(conditions)

            # 合并结果
            databases = []
            for result_list in results:
                databases.extend(result_list)

            return UnifiedResponse(
                success=True,
                message=f"获取 {len(databases)} 个源数据库成功",
                data=databases,
                timestamp=datetime.now(),
                execution_time=monitor.execution_time
            )

        except Exception as e:
            logger.error(f"批量获取源数据库失败: {e}")
            raise HTTPException(status_code=500, detail=f"批量获取源数据库失败: {str(e)}")


# ==================== LIST 操作 ====================

@router.post("/list", response_model=UnifiedListResponse, summary="列表查询源数据库")
@handle_api_errors
async def list_source_databases(
    request: UnifiedListRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    列表查询源数据库（支持分页和过滤）

    - **knowledge_id**: 知识库ID过滤（可选）
    - **filters**: 过滤条件（可选）
    - **page**: 页码
    - **page_size**: 每页大小
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向

    支持任意条件查询和分页
    """
    with PerformanceMonitor("列表查询源数据库") as monitor:
        try:
            # 构建查询条件
            conditions = {}
            if request.knowledge_id:
                conditions["knowledge_id"] = request.knowledge_id
            if request.filters:
                conditions.update(request.filters)

            # 计算偏移量
            offset = (request.page - 1) * request.page_size

            # 执行查询（这里需要扩展CRUD方法支持分页）
            # 暂时使用现有方法
            results = await metadata_crud.batch_query_source_databases([conditions])
            databases = results[0] if results else []

            # 模拟分页（实际应该在数据库层面实现）
            total_records = len(databases)
            total_pages = (total_records + request.page_size - 1) // request.page_size

            # 分页数据
            start_idx = offset
            end_idx = offset + request.page_size
            page_data = databases[start_idx:end_idx]

            # 构建分页信息
            from ..models.response_models import PaginationInfo
            pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total_pages=total_pages,
                total_records=total_records,
                has_next=request.page < total_pages,
                has_prev=request.page > 1
            )

            return UnifiedListResponse(
                success=True,
                message=f"查询到 {len(page_data)} 条源数据库记录",
                data=page_data,
                pagination=pagination,
                timestamp=datetime.now(),
                execution_time=monitor.execution_time
            )

        except Exception as e:
            logger.error(f"列表查询源数据库失败: {e}")
            raise HTTPException(status_code=500, detail=f"列表查询源数据库失败: {str(e)}")


# ==================== POST 操作 ====================

@router.post("/", response_model=BatchOperationResponse, summary="创建源数据库")
@handle_api_errors
async def create_source_databases(
    request: BatchCreateRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    创建源数据库（支持单个和批量）

    - **data**: 创建数据列表
    - **config**: 批量操作配置（可选）

    兼容性支持：可以传入单个对象或多个对象的数组
    """
    with PerformanceMonitor("创建源数据库") as monitor:
        # 验证配置
        config = validate_batch_config(request.config)

        try:
            # 验证数据格式
            for item in request.data:
                # 验证必需字段
                if 'knowledge_id' not in item or 'db_name' not in item:
                    raise ValueError("缺少必需字段: knowledge_id, db_name")

                # 设置默认值
                item.setdefault('data_layer', 'ods')
                item.setdefault('is_active', True)

            # 执行批量创建（使用默认配置）
            db_ids, vector_results = await metadata_crud.batch_create_source_databases(
                request.data,
                batch_size=500,
                max_concurrency=5,
                timeout_per_batch=300.0
            )

            # 记录日志
            log_batch_operation(
                operation="创建",
                entity_type="源数据库",
                count=len(db_ids),
                execution_time=monitor.execution_time,
                success=True
            )

            return BatchOperationResponse(
                success=True,
                message=f"成功创建{len(db_ids)}个源数据库",
                data=db_ids,  # 创建操作时data字段包含创建的ID列表
                affected_rows=len(db_ids)
            )

        except Exception as e:
            logger.error(f"创建源数据库失败: {e}")

            return BatchOperationResponse(
                success=False,
                message=f"创建源数据库失败: {str(e)}",
                data=None,
                affected_rows=0,
                errors=[{"message": str(e), "type": type(e).__name__}]
            )


# ==================== PUT 操作 ====================

@router.put("/", response_model=BatchOperationResponse, summary="更新源数据库")
@handle_api_errors
async def update_source_databases(
    request: BatchUpdateRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    更新源数据库（支持单个和批量）

    - **data**: 更新数据列表（必须包含db_id）
    - **config**: 批量操作配置（可选）

    兼容性支持：可以传入单个对象或多个对象的数组
    每个对象必须包含主键db_id和要更新的字段
    """
    with PerformanceMonitor("更新源数据库") as monitor:
        config = validate_batch_config(request.config)

        try:
            # 验证数据格式
            updates = []
            conditions = []

            for item in request.data:
                if 'db_id' not in item:
                    raise ValueError("更新数据必须包含主键字段: db_id")

                # 分离主键和更新字段
                item_copy = item.copy()
                db_id = item_copy.pop('db_id')
                conditions.append({"db_id": db_id})
                updates.append(item_copy)

            # 执行批量更新（使用默认配置）
            success = await metadata_crud.batch_update_source_databases(
                updates=updates,
                conditions=conditions,
                batch_size=500,
                max_concurrency=5,
                timeout_per_batch=300.0
            )

            log_batch_operation(
                operation="更新",
                entity_type="源数据库",
                count=len(updates),
                execution_time=monitor.execution_time,
                success=success
            )

            return BatchOperationResponse(
                success=success,
                message=f"成功更新{len(updates)}个源数据库" if success else f"更新源数据库失败",
                data=None,
                affected_rows=len(updates) if success else 0,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"更新源数据库失败: {e}")

            return BatchOperationResponse(
                success=False,
                message=f"更新源数据库失败: {str(e)}",
                data=None,
                affected_rows=0,
                errors=[{"message": str(e), "type": type(e).__name__}],
                timestamp=datetime.now()
            )


# ==================== DELETE 操作 ====================

@router.delete("/{db_id}", response_model=UnifiedResponse, summary="根据ID删除源数据库")
@handle_api_errors
async def delete_source_database_by_id(
    db_id: int = Path(..., description="数据库ID", gt=0, example=1),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤", example="kb_001"),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    根据ID删除源数据库

    - **db_id**: 数据库ID
    - **knowledge_id**: 知识库ID过滤（可选）

    ⚠️ 注意：删除数据库会级联删除相关的表和字段数据
    """
    with PerformanceMonitor("删除源数据库") as monitor:
        try:
            # 构建删除条件
            conditions = [{"db_id": db_id}]
            if knowledge_id:
                conditions[0]["knowledge_id"] = knowledge_id

            # 执行删除
            success = await metadata_crud.batch_delete_source_databases(conditions)

            if success:
                return UnifiedResponse(
                    success=True,
                    message=f"成功删除ID为 {db_id} 的源数据库",
                    data={"deleted_id": db_id},
                    timestamp=datetime.now(),
                    execution_time=monitor.execution_time
                )
            else:
                raise HTTPException(status_code=404, detail=f"未找到ID为 {db_id} 的源数据库")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除源数据库失败: {e}")
            raise HTTPException(status_code=500, detail=f"删除源数据库失败: {str(e)}")


@router.delete("/", response_model=BatchOperationResponse, summary="删除源数据库")
@handle_api_errors
async def delete_source_databases(
    request: BatchDeleteRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    删除源数据库（支持单个和批量）

    - **data**: 删除条件列表
    - **config**: 批量操作配置（可选）

    兼容性支持：可以传入单个条件对象或多个条件对象的数组
    每个对象可以包含db_id或其他查询条件

    ⚠️ 注意：删除数据库会级联删除相关的表和字段数据
    """
    with PerformanceMonitor("批量删除源数据库") as monitor:
        config = validate_batch_config(request.config)

        try:
            # 验证删除条件
            for condition in request.data:
                if not condition:
                    raise ValueError("删除条件不能为空")

            # 执行批量删除（使用默认配置）
            success = await metadata_crud.batch_delete_source_databases(
                conditions=request.data,
                batch_size=500,
                max_concurrency=5,
                timeout_per_batch=300.0
            )

            log_batch_operation(
                operation="删除",
                entity_type="源数据库",
                count=len(request.data),
                execution_time=monitor.execution_time,
                success=success
            )

            return BatchOperationResponse(
                success=success,
                message=f"成功删除{len(request.data)}个源数据库" if success else f"删除源数据库失败",
                data=None,
                affected_rows=len(request.data) if success else 0,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"批量删除源数据库失败: {e}")

            return BatchOperationResponse(
                success=False,
                message=f"删除源数据库失败: {str(e)}",
                data=None,
                affected_rows=0,
                errors=[{"message": str(e), "type": type(e).__name__}],
                timestamp=datetime.now()
            )



