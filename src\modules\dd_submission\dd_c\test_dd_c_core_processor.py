#!/usr/bin/env python3
"""
DD-C核心处理器测试

简单测试DD-C核心处理器的功能
"""

import asyncio
import json
import logging
import os
import sys
project_root=os.getcwd()
sys.path.insert(0, project_root)
from service import get_client

# 设置日志
# 设置日志
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="dd_b_core_test",
    level="INFO"
)

async def test_dd_c_core_processor():
    """测试DD-C核心处理器"""
    
    try:
        print("🚀 开始测试DD-C核心处理器")
        print("-" * 50)
        
        # 获取数据库客户端
        rdb_client = await get_client('database.rdbs.mysql')
        print("✅ 数据库客户端获取成功")
        
        # 导入DD-C核心处理器
        from modules.dd_submission.dd_c.dd_c_core_processor import process_dd_c
        
        # 测试参数
        test_cases = [
            {
                "name": "测试用例1",
                "report_code": "S71_ADS_RELEASE_V0",
                "dept_id": "30239"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 {test_case['name']}")
            print(f"  report_code: {test_case['report_code']}")
            print(f"  dept_id: {test_case['dept_id']}")
            
            # 执行处理
            result = await process_dd_c(
                rdb_client=rdb_client,
                report_code=test_case['report_code'],
                dept_id=test_case['dept_id']
            )
            
            # 显示结果
            if result.success:
                print(f"  ✅ 处理成功!")
                print(f"  总记录数: {result.total_records}")
                print(f"  处理记录数: {result.processed_records}")
                print(f"  处理时间: {result.processing_time:.2f}s")
                
                # 显示部分结果数据
                result_data = result.result_data
                items = result_data.get('item', [])
                print(f"  输出项目数: {len(items)}")
                
                if items:
                    print(f"\n  📊 第一个项目示例:")
                    first_item = items[0]
                    print(f"    entry_id: {first_item.get('entry_id')}")
                    print(f"    entry_type: {first_item.get('entry_type')}")
                    
                    # 显示SDR字段（只显示非空的）
                    sdr_fields = []
                    for j in range(1, 16):
                        sdr_field = f"sdr{j:02d}"
                        value = first_item.get(sdr_field, '')
                        if value:
                            sdr_fields.append(f"{sdr_field}={value[:30]}...")
                    
                    if sdr_fields:
                        print(f"    SDR字段: {', '.join(sdr_fields[:3])}...")
                    else:
                        print(f"    SDR字段: (全部为空)")
                    
                    # 显示index信息
                    index_info = first_item.get('index', {})
                    if index_info:
                        print(f"    index信息: 包含{len(index_info)}个字段")
                        if 'indexName' in index_info:
                            print(f"    indexName: {index_info.get('indexName')}")
                    else:
                        print(f"    index信息: (空)")
                
            else:
                print(f"  ❌ 处理失败!")
                print(f"  错误信息: {'; '.join(result.errors)}")
                print(f"  处理时间: {result.processing_time:.2f}s")
        
        print("\n" + "=" * 50)
        print("🎉 测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_dd_c_api():
    """测试DD-C API接口"""
    
    try:
        import httpx
        
        print("\n🌐 测试DD-C API接口")
        print("-" * 50)
        
        BASE_URL = "http://localhost:30337"
        
        # 测试参数
        request_data = {
            "report_code": "S71_ADS_RELEASE_V0",
            "dept_id": "30239"
        }
        
        print(f"请求URL: {BASE_URL}/api/dd/dd-c/process")
        print(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                f"{BASE_URL}/api/dd/dd-c/process",
                json=request_data
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ API调用成功!")
                
                items = result.get('item', [])
                print(f"返回项目数: {len(items)}")
                
                if items:
                    first_item = items[0]
                    print(f"第一个项目:")
                    print(f"  entry_id: {first_item.get('entry_id')}")
                    print(f"  entry_type: {first_item.get('entry_type')}")
                    
                    # 统计非空SDR字段
                    sdr_count = 0
                    for j in range(1, 16):
                        sdr_field = f"sdr{j:02d}"
                        if first_item.get(sdr_field):
                            sdr_count += 1
                    print(f"  非空SDR字段数: {sdr_count}/15")
                    
                    index_info = first_item.get('index', {})
                    print(f"  index信息: {'有' if index_info else '无'}")
                
            else:
                print(f"❌ API调用失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ API测试失败: {e}")


async def main():
    """主函数"""
    print("DD-C核心处理器测试")
    print("=" * 80)
    
    # 1. 测试核心处理器
    await test_dd_c_core_processor()
    
    # 2. 测试API接口
    await test_dd_c_api()


if __name__ == "__main__":
    asyncio.run(main())
