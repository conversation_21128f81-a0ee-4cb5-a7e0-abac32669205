"""
DD-B批量处理示例

这个脚本演示了如何使用BatchProcessor进行大批量数据处理。
"""

import asyncio
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_batch_processing():
    """示例：完整的批量处理流程"""
    print("\n=== DD-B批量处理示例 ===")
    
    try:
        # 1. 初始化所有必要的组件
        # 注意：这里使用模拟对象，实际使用时需要真实的客户端
        
        # 模拟初始化（实际使用时需要真实的初始化）
        """
        from service import get_client
        from modules.knowledge.dd.crud import DDCRUD
        from modules.knowledge.metadata.crud import MetadataCRUD
        from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineIntegrator
        from modules.dd_submission.dd_b.core.batch_processor import BatchProcessor
        
        # 获取客户端
        rdb_client = await get_client("rdb")
        
        # 创建CRUD对象
        dd_crud = DDCRUD(rdb_client)
        metadata_crud = MetadataCRUD(rdb_client)
        
        # 创建Pipeline集成器
        pipeline_integrator = PipelineIntegrator(metadata_crud)
        
        # 创建批量处理器
        batch_processor = BatchProcessor(
            rdb_client=rdb_client,
            dd_crud=dd_crud,
            pipeline_integrator=pipeline_integrator,
            metadata_crud=metadata_crud,
            max_workers=15,  # 15个worker
            batch_size=100,  # 每批100条
            max_concurrency=5  # 最大并发5
        )
        """
        
        # 2. 执行批量处理
        print("开始批量处理...")
        
        # 模拟批量处理调用
        """
        processing_result = await batch_processor.process_batch_records(
            report_code="S71_ADS_RELEASE_V0",
            dept_id="30239",
            table_ids=None  # 使用默认表ID
        )
        """
        
        # 模拟处理结果
        from modules.dd_submission.dd_b.core.batch_processor import BatchProcessingResult
        processing_result = BatchProcessingResult()
        processing_result.total_records = 1500
        processing_result.normal_records = 1499
        processing_result.range_records = 1
        processing_result.processed_records = 1450
        processing_result.failed_records = 49
        processing_result.processing_time = 125.5
        processing_result.pipeline_time = 98.2
        processing_result.aggregation_time = 3.8
        
        # 模拟结果数据
        processing_result.normal_results = [
            {
                'record_id': str(i),
                'bdr09': "['table1', 'table2']",
                'bdr10': "['表1', '表2']",
                'bdr11': "{'col1': 'table1', 'col2': 'table2'}",
                'sdr09': "{'col1': '列1', 'col2': '列2'}",
                'sdr10': f"SELECT * FROM table{i % 3 + 1}"
            }
            for i in range(1, 1451)
        ]
        
        processing_result.range_result = {
            'record_id': '1500',
            'bdr09': "['table1', 'table2', 'table3']",
            'bdr10': "['表1', '表2', '表3']",
            'bdr11': "{'col1': 'table1', 'col2': 'table2', 'col3': 'table3'}",
            'sdr09': "{'col1': '列1', 'col2': '列2', 'col3': '列3'}",
            'sdr10': "",  # RANGE记录的sdr10为空
            'sdr12': "['table1.id = table2.id']"  # 聚合的JOIN条件
        }
        
        # 3. 输出处理结果
        print(f"\n批量处理完成!")
        print(f"总记录数: {processing_result.total_records}")
        print(f"普通记录: {processing_result.normal_records}")
        print(f"RANGE记录: {processing_result.range_records}")
        print(f"成功处理: {processing_result.processed_records}")
        print(f"失败记录: {processing_result.failed_records}")
        print(f"总耗时: {processing_result.processing_time:.2f}s")
        print(f"Pipeline耗时: {processing_result.pipeline_time:.2f}s")
        print(f"聚合耗时: {processing_result.aggregation_time:.2f}s")
        
        # 4. 获取最终结果
        """
        final_results = await batch_processor.get_final_results(processing_result)
        """
        
        # 模拟最终结果
        final_results = processing_result.normal_results + [processing_result.range_result]
        
        print(f"\n最终结果:")
        print(f"总记录数: {len(final_results)}")
        print(f"普通记录: {len(processing_result.normal_results)}")
        print(f"RANGE记录: {1 if processing_result.range_result else 0}")
        
        # 5. 展示部分结果
        print(f"\n前3个普通记录示例:")
        for i, result in enumerate(final_results[:3]):
            print(f"记录 {i+1}: {len(result)} 个字段")
            for key, value in list(result.items())[:3]:  # 只显示前3个字段
                display_value = value[:50] + "..." if len(str(value)) > 50 else value
                print(f"  {key}: {display_value}")
        
        print(f"\nRANGE记录示例:")
        if processing_result.range_result:
            range_result = processing_result.range_result
            print(f"记录ID: {range_result['record_id']}")
            print(f"字段数: {len(range_result)}")
            for key, value in list(range_result.items())[:5]:  # 显示前5个字段
                display_value = value[:50] + "..." if len(str(value)) > 50 else value
                print(f"  {key}: {display_value}")
        
        return final_results
        
    except Exception as e:
        logger.error(f"批量处理示例失败: {e}")
        raise


async def example_performance_analysis():
    """示例：性能分析"""
    print("\n=== 性能分析示例 ===")
    
    # 模拟不同规模的处理时间
    scenarios = [
        {"records": 100, "time": 8.5, "workers": 5},
        {"records": 500, "time": 28.2, "workers": 10},
        {"records": 1000, "time": 45.8, "workers": 15},
        {"records": 2000, "time": 85.3, "workers": 15},
        {"records": 5000, "time": 198.7, "workers": 15},
    ]
    
    print("不同规模的处理性能:")
    print("记录数\t耗时(s)\tWorker数\t平均每条(ms)")
    print("-" * 40)
    
    for scenario in scenarios:
        records = scenario["records"]
        time_s = scenario["time"]
        workers = scenario["workers"]
        avg_ms = (time_s * 1000) / records
        
        print(f"{records}\t{time_s:.1f}\t{workers}\t\t{avg_ms:.1f}")
    
    print("\n性能优化建议:")
    print("1. 记录数 < 1000: 使用10个worker")
    print("2. 记录数 >= 1000: 使用15个worker")
    print("3. 批量大小建议: 100条/批")
    print("4. 最大并发建议: 5个批次")


async def example_error_handling():
    """示例：错误处理"""
    print("\n=== 错误处理示例 ===")
    
    # 模拟各种错误场景
    error_scenarios = [
        {
            "name": "数据库连接失败",
            "error": "Connection timeout after 30s",
            "solution": "检查数据库连接配置，增加超时时间"
        },
        {
            "name": "Pipeline执行失败",
            "error": "Pipeline step 'table_selector' failed",
            "solution": "检查表ID配置，确保表存在"
        },
        {
            "name": "字段映射失败",
            "error": "Metadata query failed for table 'unknown_table'",
            "solution": "检查表名是否正确，更新元数据"
        },
        {
            "name": "内存不足",
            "error": "Out of memory processing 10000 records",
            "solution": "减少批量大小，增加系统内存"
        }
    ]
    
    print("常见错误及解决方案:")
    for i, scenario in enumerate(error_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   错误: {scenario['error']}")
        print(f"   解决: {scenario['solution']}")
    
    print("\n错误处理最佳实践:")
    print("1. 设置合理的超时时间")
    print("2. 实现重试机制")
    print("3. 记录详细的错误日志")
    print("4. 监控系统资源使用")
    print("5. 实现优雅降级")


async def example_configuration_guide():
    """示例：配置指南"""
    print("\n=== 配置指南 ===")
    
    configurations = {
        "小规模处理 (< 500条)": {
            "max_workers": 5,
            "batch_size": 50,
            "max_concurrency": 3,
            "timeout_per_batch": 60.0
        },
        "中等规模处理 (500-2000条)": {
            "max_workers": 10,
            "batch_size": 100,
            "max_concurrency": 5,
            "timeout_per_batch": 120.0
        },
        "大规模处理 (2000-5000条)": {
            "max_workers": 15,
            "batch_size": 100,
            "max_concurrency": 5,
            "timeout_per_batch": 300.0
        },
        "超大规模处理 (> 5000条)": {
            "max_workers": 15,
            "batch_size": 200,
            "max_concurrency": 8,
            "timeout_per_batch": 600.0
        }
    }
    
    print("推荐配置:")
    for scenario, config in configurations.items():
        print(f"\n{scenario}:")
        for key, value in config.items():
            print(f"  {key}: {value}")
    
    print("\n配置说明:")
    print("- max_workers: 并发处理的worker数量")
    print("- batch_size: 每批查询的记录数")
    print("- max_concurrency: 最大并发批次数")
    print("- timeout_per_batch: 每批次超时时间(秒)")


async def main():
    """主函数"""
    print("DD-B批量处理示例")
    print("=" * 50)
    
    try:
        await example_batch_processing()
        await example_performance_analysis()
        await example_error_handling()
        await example_configuration_guide()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成!")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
