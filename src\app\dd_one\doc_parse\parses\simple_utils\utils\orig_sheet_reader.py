import re
import openpyxl
import os
from app.dd_one.doc_parse.parses.simple_utils.utils.mulit_tables import detect_tables_by_border, save_tables_to_excel, \
    get_sheetname_nums
from app.dd_one.doc_parse.parses.simple_utils.utils.extract_utils import is_valid_x, get_x_series, get_id, get_detail_series
import subprocess
from dataclasses import dataclass, field
import pandas as pd
import itertools
from config.config import TMP_PATH


@dataclass
class CellInfo:
    name_x: str
    id_x: str
    name_y: str
    id_y: str
    valid: bool
    original_position: tuple


# 可配置的 y 列匹配模式列表
def generate_initial_patterns():
    # A-Z 的大写字母
    patterns = [chr(i) for i in range(ord('A'), ord('Z') + 1)]

    # 添加 B0-B6
    patterns += ['A' + str(i) for i in range(0, 7)]
    patterns += ['B' + str(i) for i in range(0, 7)]

    # 添加C0-C6
    patterns += ['C' + str(i) for i in range(0, 7)]
    patterns += ['D' + str(i) for i in range(0, 7)]
    patterns += ['E' + str(i) for i in range(0, 7)]
    patterns += ['F' + str(i) for i in range(0, 7)]
    patterns += ['G' + str(i) for i in range(0, 7)]

    # 添加 AA - ZZ
    letters = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    patterns += [''.join(pair) for pair in itertools.product(letters, repeat=2)]

    return patterns


VALID_Y_PATTERNS = generate_initial_patterns()


def has_border(cell):
    """检查单元格是否有边框"""
    return any(
        side.style is not None for side in [cell.border.left, cell.border.right, cell.border.top, cell.border.bottom]
    )


def detect_table_ranges(ws):
    """基于边框检测表格范围，返回表格范围列表，并在最后一个表格后添加一行"""
    tables = []
    current_table = []
    in_table = False
    first_row = True
    start_row, start_col, end_row, end_col = None, None, None, None

    max_row = ws.max_row
    max_col = ws.max_column

    for row_idx in range(1, max_row + 1):
        row = ws[row_idx]
        row_has_border = any(has_border(cell) for cell in row[:max_col])

        if first_row and not row_has_border:
            # 第一行无边框，假设为表头
            current_table.append([cell.value for cell in row[:max_col]])
            in_table = True
            start_row = row_idx
            start_col = 1
            first_row = False
        elif row_has_border:
            # 有边框的行，属于表格
            if not in_table:
                in_table = True
                start_row = row_idx
                start_col = 1
            current_table.append([cell.value for cell in row[:max_col]])
            end_row = row_idx
            end_col = max_col
        else:
            # 无边框的行，表格结束
            if in_table:
                if all(x is not None for x in [start_row, end_row, start_col, end_col]):
                    # 在表格数据后添加空行
                    current_table.append([None] * max_col)
                    tables.append({
                        'start_row': start_row,
                        'end_row': end_row + 1,  # 增加一行
                        'start_col': start_col,
                        'end_col': end_col,
                        'data': current_table
                    })
                else:
                    print("跳过无效表格区域：存在 None 的行列边界")
                in_table = False
                current_table = []
                start_row, end_row, start_col, end_col = None, None, None, None
            first_row = False

    # 处理最后一个表格
    if in_table and current_table:
        if all(x is not None for x in [start_row, end_row, start_col, end_col]):
            # 在最后一个表格后添加空行
            current_table.append([None] * max_col)
            tables.append({
                'start_row': start_row,
                'end_row': end_row + 1,  # 增加一行
                'start_col': start_col,
                'end_col': end_col,
                'data': current_table
            })
        else:
            print("跳过无效表格区域：存在 None 的行列边界")

    return tables


def process_merged_cells(ws, df, start_row, start_col, end_row, end_col):
    """处理合并单元格，将左上角值分配到所有单元格"""
    max_rows, max_cols = df.shape

    for merged_range in ws.merged_cells.ranges:
        # 获取合并范围（openpyxl 使用 1-based）
        min_row = merged_range.min_row
        min_col = merged_range.min_col
        max_row = merged_range.max_row
        max_col = merged_range.max_col

        # 跳过不在指定区域内的合并单元格
        if (min_row < start_row or max_row > end_row or
                min_col < start_col or max_col > end_col):
            continue

        # 转换为 DataFrame 中的 0-based 行列索引
        df_min_row = min_row - start_row
        df_max_row = max_row - start_row
        df_min_col = min_col - start_col
        df_max_col = max_col - start_col

        # 确保不越界
        if df_min_row < 0 or df_max_row >= max_rows or df_min_col < 0 or df_max_col >= max_cols:
            print(f"合并单元格 ({min_row},{min_col})-({max_row},{max_col}) 超出 DataFrame 范围，跳过")
            continue

        # 获取左上角单元格的值
        top_left_cell = ws.cell(row=min_row, column=min_col)
        top_left_value = top_left_cell.value

        # 打印调试信息
        # print(f"合并单元格 ({min_row},{min_col})-({max_row},{max_col}): {top_left_value}")

        # 将值填充到所有子单元格
        for r in range(df_min_row, df_max_row + 1):
            for c in range(df_min_col, df_max_col + 1):
                if r < max_rows and c < max_cols:
                    try:
                        df.iloc[r, c] = top_left_value
                    except Exception as e:
                        print(f"无法设置值到位置 ({r}, {c})：{e}")
                else:
                    print(f"跳过超出范围的赋值: row={r}, col={c}")


def is_valid_y(value) -> bool:
    """检查 y 列值是否符合 VALID_Y_PATTERNS 中的模式"""
    if value is None:
        # print(f"无效 y 值: None")
        return False
    value_str = str(value).strip().upper()
    is_valid = value_str in [pattern.upper() for pattern in VALID_Y_PATTERNS]
    if not is_valid:
        # print(f"无效 y 值: {value} (转换为: {value_str})")
        return False
    return is_valid


def get_y_series(df):
    """识别主 y 行：找到包含 VALID_Y_PATTERNS 中值的行"""
    max_rows, max_cols = df.shape
    for row in range(min(max_rows, 50)):
        valid_y_columns = []
        # print(f"检查行 {row}: {df.iloc[row, :].tolist()}")
        for col in range(max_cols):
            value = df.iloc[row, col]
            if is_valid_y(value):
                valid_y_columns.append(col)
        if valid_y_columns:
            y_series_x = row
            y_first_y = min(valid_y_columns)
            y_last_y = max(valid_y_columns)
            return y_series_x, y_first_y, y_last_y
    # print("未找到包含 VALID_Y_PATTERNS 中值的主 y 行")
    return None, None, None


def extract_cells_from_table(ws, table_range):
    """从表格范围中提取单元格信息，包含原始坐标"""
    start_row = table_range['start_row']
    end_row = table_range['end_row']
    start_col = table_range['start_col']
    end_col = table_range['end_col']
    data = table_range['data']

    # 转换为 DataFrame
    df = pd.DataFrame(data)

    # 处理合并单元格
    process_merged_cells(ws, df, start_row, start_col, end_row, end_col)

    x_series_y, x_first_x, x_last_x = get_x_series(df)
    y_series_x, y_first_y, y_last_y = get_y_series(df)

    # 确定输出类型
    output_type = 'index' if all([x_series_y is not None, x_first_x is not None, x_last_x is not None,
                                      y_series_x is not None, y_first_y is not None,
                                      y_last_y is not None]) else 'list'

    if output_type == 'list':
        extracted_cells = []
        first_x, last_x = get_detail_series(df, y_series_x, y_first_y)
        id_y_dict, name_y_dict = {}, {}
        for y in range(y_first_y, y_last_y + 1):
            id_y_dict[y] = get_id(df, (y, y_series_x, last_x + 1), 'y')
            name_y_dict[y] = df.iloc[y_series_x, y]
            # 从单元格的 comment 中提取原始坐标
            cell = ws.cell(row=y_series_x + start_row, column=y + start_col)
            original_position = None
            if cell.comment:
                match = re.match(r"Original Position: \((\d+), (\d+)\)", cell.comment.text)
                if match:
                    original_position = (int(match.group(1)), int(match.group(2)))
            # 如果没有 comment，则使用计算的坐标
            original_position = original_position or (y_series_x + start_row, y + start_col)
            extracted_cells.append(
                CellInfo(
                    name_x='明细表没有维度',
                    id_x='明细id<填充用>',
                    name_y=name_y_dict[y],
                    id_y=id_y_dict[y],
                    valid=True,
                    original_position=original_position
                )
            )
        return extracted_cells, output_type

    # 原有逻辑处理 indicator 类型
    if not all([x_series_y is not None, x_first_x is not None, x_last_x is not None]):
        raise ValueError(
            f"get_x_series 返回无效值: x_series_y={x_series_y}, x_first_x={x_first_x}, x_last_x={x_last_x}")

    if not all([y_series_x is not None, y_first_y is not None, y_last_y is not None]):
        raise ValueError(
            f"get_y_series 返回无效值: y_series_x={y_series_x}, y_first_y={y_first_y}, y_last_y={y_last_y}")

    name_x_dict, name_y_dict = {}, {}
    for x in range(x_first_x, x_last_x + 1):
        name_x_dict[x] = get_id(df, (x, 0, y_first_y), 'x')
    for y in range(y_first_y, y_last_y + 1):
        name_y_dict[y] = get_id(df, (y, y_series_x, x_first_x), 'y')

    extracted_cells = []
    for x in range(x_first_x, x_last_x + 1):
        name_x = df.iloc[x, x_series_y]
        if not is_valid_x(name_x):
            continue
        if name_x is not None:
            name_x = name_x.replace('按交易对手：', '').replace('按类别：', '').replace('按机构：', '')
        id_x = name_x_dict[x]
        for y in range(y_first_y, y_last_y + 1):
            name_y = df.iloc[y_series_x, y]
            if not is_valid_y(name_y):
                continue
            id_y = name_y_dict[y]
            # 从单元格的 comment 中提取原始坐标
            cell = ws.cell(row=x + start_row, column=y + start_col)
            original_position = None
            if cell.comment:
                match = re.match(r"Original Position: \((\d+), (\d+)\)", cell.comment.text)
                if match:
                    original_position = (int(match.group(1)), int(match.group(2)))
            # 如果没有 comment，则使用计算的坐标
            original_position = original_position or (x + start_row, y + start_col)
            fill_color = cell.fill.start_color.index[2:] if isinstance(cell.fill.start_color.index, str) else str(
                cell.fill.start_color.index)
            cell_info = CellInfo(
                name_x=name_x,
                id_x=id_x,
                name_y=name_y,
                id_y=id_y,
                valid=fill_color.lower() not in ('c0c0c0', 'a6a6a6', '22', '55', '969696'),
                original_position=original_position
            )
            if not all([name_x, name_y, id_x, id_y]):
                print(f'错误单元格信息: {cell_info}')
                continue
            extracted_cells.append(cell_info)

    return extracted_cells, output_type


def sanitize_sheet_name(sheet_name):
    """清理工作表名称，替换非法字符并确保长度合法"""
    # Excel 工作表名称非法字符
    invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
    # 替换非法字符为下划线
    clean_name = sheet_name
    for char in invalid_chars:
        clean_name = clean_name.replace(char, '_')
    # 限制长度为 31 个字符（Excel 限制）
    clean_name = clean_name[:31]
    # 去除首尾下划线，确保名称有效
    clean_name = clean_name.strip('_')
    # 如果名称为空，使用默认名称
    return clean_name or 'Sheet'


def extract_cells(file_path, sheet_names, output_path):
    """处理 Excel 文件中的多个工作表并保存结果"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在")

    try:
        wb = openpyxl.load_workbook(file_path)
        # 如果未指定工作表名称，处理所有工作表
        if not sheet_names:
            sheet_names = wb.sheetnames

        at_least_one_table_written = False
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for sheet_name in sheet_names:
                if sheet_name not in wb.sheetnames:
                    print(f"工作表 {sheet_name} 不存在，跳过")
                    continue
                ws = wb[sheet_name]

                # 检测表格范围
                table_ranges = detect_table_ranges(ws)
                if not table_ranges:
                    print(f"工作表 {sheet_name} 未检测到任何表格")
                    continue

                # 清理工作表名称
                base_sheet_name = sanitize_sheet_name(sheet_name)

                for i, table_range in enumerate(table_ranges):
                    try:
                        cells, analyze_type = extract_cells_from_table(ws, table_range)
                        results_df = pd.DataFrame(cells)
                        sheet_name_safe = f"{base_sheet_name}"[:31]
                        results_df.to_excel(writer, sheet_name=sheet_name_safe, index=False)
                        at_least_one_table_written = True
                    except Exception as e:
                        print(f"处理工作表 {sheet_name} 表格 {i + 1} 时出错: {e}")
            if not at_least_one_table_written:
                print("没有成功处理任何表格")
                pd.DataFrame().to_excel(writer, sheet_name='Empty', index=False)
        return output_path, analyze_type
    except Exception as e:
        print(f"处理文件时出错: {e}")


def convert_xls_to_xlsx(input_path):
    """Convert .xls file to .xlsx using LibreOffice in headless mode."""
    output_dir = TMP_PATH
    file_name = os.path.basename(input_path).split('.')[0]
    output_path = os.path.join(output_dir, f"{file_name}.xlsx")

    try:
        # Run LibreOffice conversion command
        subprocess.run([
            'libreoffice',
            '--headless',
            '--convert-to',
            'xlsx',
            '--outdir',
            output_dir,
            input_path
        ], check=True)
        return output_path
    except subprocess.CalledProcessError as e:
        raise Exception(f"Failed to convert .xls to .xlsx: {e}")
    except FileNotFoundError:
        raise Exception("LibreOffice not found. Please ensure it is installed and accessible.")


def process_excel_file(file_path, sheet_names_str):
    file_name = os.path.basename(file_path).split('.')[0]
    file_dir = TMP_PATH
    output_path = os.path.join(TMP_PATH, f'extracted_cells_{file_name}.xlsx')

    if not sheet_names_str:
        sheet_names = []
    else:
        sheet_names = [name.strip() for name in sheet_names_str.split(',')]

    # Ensure output directory exists
    output_dir = os.path.dirname(output_path) or '.'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Check if file is .xls and convert if necessary
    if file_path.lower().endswith('.xls') or file_path.lower().endswith('.et'):
        # print("Detected .xls file, converting to .xlsx...")
        file_path = convert_xls_to_xlsx(file_path)
        # print(f"Converted file saved to: {file_path}")

    # Proceed with cell extraction
    tables_by_sheet = detect_tables_by_border(file_path, max_col='BZ')
    file_path = save_tables_to_excel(tables_by_sheet, os.path.join(file_dir, f'output_border_{file_name}.xlsx'),
                                     file_path)
    tran_type, first_path = extract_cells(file_path, sheet_names, output_path)
    return first_path, tran_type


def get_excel_type(file_path, sheet_names_str=''):
    file_name = os.path.basename(file_path).split('.')[0]
    file_dir = TMP_PATH
    output_path = os.path.join(file_dir, f'extracted_cells_{file_name}.xlsx')

    if not sheet_names_str:
        sheet_names = []
    else:
        sheet_names = [name.strip() for name in sheet_names_str.split(',')]

    # Ensure output directory exists
    output_dir = os.path.dirname(output_path) or '.'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Check if file is .xls and convert if necessary
    if file_path.lower().endswith('.xls') or file_path.lower().endswith('.et'):
        # print("Detected .xls file, converting to .xlsx...")
        file_path = convert_xls_to_xlsx(file_path)
        # print(f"Converted file saved to: {file_path}")

    # Proceed with cell extraction
    tables_by_sheet = detect_tables_by_border(file_path, max_col='N')
    file_path = save_tables_to_excel(tables_by_sheet, os.path.join(file_dir, f'output_border_{file_name}.xlsx'),
                                     file_path)
    first_path, tran_type = extract_cells(file_path, sheet_names, output_path)
    return tran_type


if __name__ == "__main__":
    file_path = r"D:\pythonProject\works\test_dd_file\file\S24\S24_template.xlsx"
    # sheet_names_input = input("请输入工作表名称（多个用逗号分隔，留空处理所有工作表）: ").strip().replace('，', ',')
    process_excel_file(file_path, '')
    # process_excel_file(file_path, '')
