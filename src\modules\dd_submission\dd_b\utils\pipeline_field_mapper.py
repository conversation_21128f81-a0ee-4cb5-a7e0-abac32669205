"""
Pipeline字段映射器 - 可复用模块

这个模块提供了从Pipeline结果到DD-B字段的映射功能，可以在多个地方复用。

主要功能：
1. 将Pipeline执行结果映射到BDR05-BDR17字段
2. 将Pipeline执行结果映射到SDR01-SDR15字段  
3. 支持批量处理和聚合
4. 支持不同的输出格式（原始格式、字符串格式）

使用示例：
```python
from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper

# 创建映射器
mapper = PipelineFieldMapper(metadata_crud)

# 单个记录映射
result = await mapper.map_single_record(pipeline_result, original_record)

# 批量记录映射
results = await mapper.map_batch_records(pipeline_results, original_records)

# 聚合映射结果
aggregated = mapper.aggregate_mapping_results(results)
```
"""

import ast
import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class PipelineFieldMappingResult:
    """Pipeline字段映射结果"""
    
    # 原始记录信息
    record_id: int
    mapping_success: bool = False
    mapping_notes: List[str] = field(default_factory=list)
    original_record: Any = None  # 添加原始记录存储
    
    # BDR字段 (保持原始格式，便于聚合)
    bdr09_raw: Optional[List[str]] = None  # 表英文名列表
    bdr10_raw: Optional[List[str]] = None  # 表中文名列表
    bdr11_raw: Optional[Dict[str, str]] = None  # 字段信息字典
    bdr16_raw: Optional[str] = None  # 业务逻辑描述
    
    # SDR字段 (保持原始格式，便于聚合)
    sdr05_raw: Optional[List[str]] = None  # 与BDR09相同
    sdr06_raw: Optional[List[str]] = None  # 与BDR10相同
    sdr08_raw: Optional[Dict[str, str]] = None  # 与BDR11相同
    sdr09_raw: Optional[Dict[str, str]] = None  # 字段中文名字典
    sdr10_raw: Optional[str] = None  # SQL语句
    sdr12_raw: Optional[List[str]] = None  # JOIN条件列表
    
    def to_string_format(self, original_record=None) -> Dict[str, str]:
        """转换为字符串格式，用于最终存储，包含所有字段的默认值"""
        result = {}

        # BDR字段转字符串（包含默认值）
        result['bdr05'] = ""
        result['bdr06'] = "不适用"
        result['bdr07'] = "1"  # BDR07默认为1
        result['bdr08'] = "CRD"
        result['bdr09'] = json.dumps(self.bdr09_raw, ensure_ascii=False) if self.bdr09_raw else ""
        result['bdr10'] = json.dumps(self.bdr10_raw, ensure_ascii=False) if self.bdr10_raw else ""
        result['bdr11'] = json.dumps(self.bdr11_raw, ensure_ascii=False) if self.bdr11_raw else ""
        result['bdr12'] = "不适用"  # BDR12默认为空
        result['bdr13'] = "不适用"  # BDR13默认为空
        result['bdr14'] = "不适用"  # BDR14默认为空
        result['bdr15'] = "不适用"  # BDR15默认为空
        result['bdr16'] = self.bdr16_raw if self.bdr16_raw else ""
        result['bdr17'] = "不适用"  # BDR17默认为空

        # SDR字段转字符串（包含默认值）
        # 使用传入的original_record参数，如果没有则使用存储的original_record
        source_record = original_record or self.original_record
        if source_record:
            if hasattr(source_record, 'get'):
                result['sdr01'] = source_record.get('dr01', '')  # 字典类型
            else:
                result['sdr01'] = getattr(source_record, 'dr01', '')  # 对象类型
        else:
            result['sdr01'] = ""  # 没有原始记录时为空
        result['sdr02'] = "1"  # SDR02默认为空
        result['sdr03'] = "CRD"  # SDR03默认为空
        result['sdr04'] = "不适用"  # SDR04默认为空
        result['sdr05'] = json.dumps(self.sdr05_raw, ensure_ascii=False) if self.sdr05_raw else ""
        result['sdr06'] = json.dumps(self.sdr06_raw, ensure_ascii=False) if self.sdr06_raw else ""
        result['sdr07'] = "不适用"  # SDR07默认为空
        result['sdr08'] = json.dumps(self.sdr08_raw, ensure_ascii=False) if self.sdr08_raw else ""
        result['sdr09'] = json.dumps(self.sdr09_raw, ensure_ascii=False) if self.sdr09_raw else ""
        result['sdr10'] = self.sdr10_raw if self.sdr10_raw else ""
        result['sdr11'] = "不适用"  # SDR11默认为空
        result['sdr12'] = json.dumps(self.sdr12_raw, ensure_ascii=False) if self.sdr12_raw else ""
        result['sdr13'] = "不适用"  # SDR13默认为空
        result['sdr14'] = "不适用"  # SDR14默认为空
        result['sdr15'] = ""  # SDR15默认为空

        return result


@dataclass
class AggregatedFieldResult:
    """聚合后的字段结果"""
    
    # 聚合后的字段 (原始格式)
    bdr09_aggregated: List[str] = field(default_factory=list)  # 所有表英文名
    bdr10_aggregated: List[str] = field(default_factory=list)  # 所有表中文名
    bdr11_aggregated: Dict[str, str] = field(default_factory=dict)  # 所有字段信息
    
    sdr05_aggregated: List[str] = field(default_factory=list)  # 与bdr09相同
    sdr06_aggregated: List[str] = field(default_factory=list)  # 与bdr10相同
    sdr08_aggregated: Dict[str, str] = field(default_factory=dict)  # 与bdr11相同
    sdr09_aggregated: Dict[str, str] = field(default_factory=dict)  # 所有字段中文名
    
    # RANGE项目特殊处理
    sdr12_candidates: List[str] = field(default_factory=list)  # sdr12候选值
    
    def to_range_record(self) -> Dict[str, str]:
        """生成RANGE记录的字段值"""
        result = {}
        
        # BDR字段
        if self.bdr09_aggregated:
            result['bdr09'] = str(self.bdr09_aggregated)
            result['bdr16'] = f"表范围：{self.bdr09_aggregated}"
        if self.bdr10_aggregated:
            result['bdr10'] = str(self.bdr10_aggregated)
        if self.bdr11_aggregated:
            result['bdr11'] = str(self.bdr11_aggregated)
            
        # SDR字段
        if self.sdr05_aggregated:
            result['sdr05'] = str(self.sdr05_aggregated)
        if self.sdr06_aggregated:
            result['sdr06'] = str(self.sdr06_aggregated)
        if self.sdr08_aggregated:
            result['sdr08'] = str(self.sdr08_aggregated)
        if self.sdr09_aggregated:
            result['sdr09'] = str(self.sdr09_aggregated)
            
        # RANGE特殊处理
        result['sdr10'] = ""  # 直接置空
        
        # sdr12: 统计出现次数>1/2的值 - 特殊处理：空列表时返回空字符串
        if self.sdr12_candidates:
            total_count = len(self.sdr12_candidates)
            from collections import Counter
            counter = Counter(self.sdr12_candidates)
            frequent_values = [value for value, count in counter.items()
                             if count > total_count / 2]
            # SDR12特殊处理：如果没有频繁值，返回空字符串而不是"[]"
            if frequent_values:
                import json
                result['sdr12'] = json.dumps(frequent_values, ensure_ascii=False)
            else:
                result['sdr12'] = ""
        else:
            result['sdr12'] = ""
            
        return result


class PipelineFieldMapper:
    """Pipeline字段映射器"""
    
    def __init__(self, metadata_crud):
        """
        初始化映射器
        
        Args:
            metadata_crud: 元数据CRUD对象，用于查询表和字段信息
        """
        self.metadata_crud = metadata_crud
        
    async def map_single_record(
        self, 
        pipeline_result: Dict[str, Any], 
        original_record: Any,
        keep_raw_format: bool = True
    ) -> PipelineFieldMappingResult:
        """
        映射单个记录的Pipeline结果到字段
        
        Args:
            pipeline_result: Pipeline执行结果
            original_record: 原始记录对象
            keep_raw_format: 是否保持原始格式（用于聚合）
            
        Returns:
            PipelineFieldMappingResult: 映射结果
        """
        try:
            logger.debug(f"开始字段映射: record_id={original_record.id}")
            
            result = PipelineFieldMappingResult(
                record_id=original_record.id,
                original_record=original_record  # 设置原始记录
            )
            mapping_notes = []
            
            # 提取Pipeline输出
            business_logic = pipeline_result.get("business_logic", {})
            parser_info = pipeline_result.get("parser_info", {})
            sql_candidates = pipeline_result.get("sql_candidates", [])

            # 提取candidate字段
            candidate_tables = pipeline_result.get("candidate_tables", [])
            candidate_columns = pipeline_result.get("candidate_columns", {})
            candidate_column_ids = pipeline_result.get("candidate_column_ids", {})

            # 详细日志：检查candidate字段
            logger.debug(f"🔍 字段映射调试 (record_id={original_record.id}):")
            logger.debug(f"  pipeline_result keys: {list(pipeline_result.keys())}")
            logger.debug(f"  candidate_tables: {candidate_tables}")
            logger.debug(f"  candidate_columns: {candidate_columns}")
            logger.debug(f"  candidate_column_ids: {candidate_column_ids}")

            # 🚨 断点标记：字段映射入口
            logger.debug(f"🚨 BREAKPOINT: 字段映射开始 - record_id={original_record.id}")
            logger.debug(f"🚨 candidate_column_ids = {candidate_column_ids}")
            # 🚨 断点标记结束
            
            # 最安全的Pipeline结果检查：只检查pipeline_result本身是否有意义
            # 排除一些明显的无效情况，但不限制具体的输出字段

            # 1. 检查pipeline_result是否为空字典
            if not pipeline_result:
                logger.warning(f"⚠️ Pipeline结果为空字典! (record_id={original_record.id})")
                result.mapping_success = False
                result.mapping_notes = ["Pipeline结果为空字典"]
                return result

            # 2. 检查是否只包含无意义的系统字段
            meaningful_keys = [k for k in pipeline_result.keys()
                             if not k.startswith('_') and k not in ['status', 'timestamp', 'execution_time']]

            if not meaningful_keys:
                logger.warning(f"⚠️ Pipeline结果只包含系统字段! (record_id={original_record.id})")
                logger.debug(f"  所有keys: {list(pipeline_result.keys())}")
                result.mapping_success = False
                result.mapping_notes = ["Pipeline结果只包含系统字段"]
                return result

            # 3. 检查有意义的字段是否都为空
            has_meaningful_data = any(
                pipeline_result[k] for k in meaningful_keys
                if pipeline_result[k] is not None and pipeline_result[k] != {} and pipeline_result[k] != []
            )

            if not has_meaningful_data:
                logger.warning(f"⚠️ Pipeline结果的有意义字段都为空! (record_id={original_record.id})")
                logger.debug(f"  有意义的keys: {meaningful_keys}")
                result.mapping_success = False
                result.mapping_notes = ["Pipeline结果的有意义字段都为空"]
                return result

            logger.debug(f"✅ Pipeline结果有效 (有意义字段: {meaningful_keys}), 开始字段映射 (record_id={original_record.id})")
            
            # 映射各个字段
            await self._map_bdr09(result, parser_info, mapping_notes)
            await self._map_bdr10(result, mapping_notes)
            await self._map_bdr11(result, candidate_columns, mapping_notes)
            await self._map_bdr16(result, business_logic, mapping_notes)

            await self._map_sdr_fields(result, parser_info, sql_candidates, candidate_columns, candidate_column_ids, mapping_notes)
            
            result.mapping_success = True
            result.mapping_notes = mapping_notes
            
            logger.debug(f"字段映射完成: record_id={original_record.id}, 成功={result.mapping_success}")
            
            return result
            
        except Exception as e:
            logger.error(f"字段映射失败: record_id={original_record.id}, error={e}")
            
            return PipelineFieldMappingResult(
                record_id=original_record.id,
                mapping_success=False,
                mapping_notes=[f"字段映射异常: {str(e)}"]
            )

    async def _map_bdr09(self, result: PipelineFieldMappingResult, parser_info: Dict, mapping_notes: List[str]):
        """映射BDR09: 表英文名列表"""
        if parser_info.get("tables"):
            tables = parser_info["tables"]
            if isinstance(tables, list):
                result.bdr09_raw = tables
                mapping_notes.append(f"BDR09映射成功: {len(tables)}个表")
            else:
                result.bdr09_raw = [tables]
                mapping_notes.append("BDR09映射成功: 单个表")

    async def _map_bdr10(self, result: PipelineFieldMappingResult, mapping_notes: List[str]):
        """映射BDR10: 表中文名列表"""
        if result.bdr09_raw:
            table_cn_names = []

            for table_name in result.bdr09_raw:
                try:
                    # 使用source_table方法查询表中文名
                    table_info = await self.metadata_crud.get_source_table(table_name=table_name.strip())
                    if table_info and table_info.get("table_name_cn"):
                        table_cn_names.append(table_info["table_name_cn"])
                    else:
                        table_cn_names.append(table_name.strip())  # 使用原名作为备选
                except Exception as e:
                    logger.warning(f"查询表中文名失败: {table_name}, error={e}")
                    table_cn_names.append(table_name.strip())

            result.bdr10_raw = table_cn_names
            mapping_notes.append(f"BDR10映射成功: {len(table_cn_names)}个表中文名")

    async def _map_bdr11(self, result: PipelineFieldMappingResult, candidate_columns: Dict, mapping_notes: List[str]):
        """映射BDR11: 使用candidate_columns，格式为{table_1:[col_1,col_21]...}"""
        logger.debug(f"🔍 BDR11映射调试:")
        logger.debug(f"  candidate_columns类型: {type(candidate_columns)}")
        logger.debug(f"  candidate_columns内容: {candidate_columns}")

        if candidate_columns:
            result.bdr11_raw = candidate_columns
            mapping_notes.append(f"BDR11映射成功: 使用candidate_columns，{len(candidate_columns)}个表")
            logger.debug(f"✅ BDR11映射成功: {len(candidate_columns)}个表")
        else:
            result.bdr11_raw = {}
            mapping_notes.append("BDR11映射: candidate_columns为空")
            logger.warning(f"⚠️ BDR11映射失败: candidate_columns为空")

    async def _map_bdr16(self, result: PipelineFieldMappingResult, business_logic: Dict, mapping_notes: List[str]):
        """映射BDR16: 业务逻辑描述"""
        if business_logic:
            # 构建业务逻辑描述
            description_parts = []

            if business_logic.get("表范围"):
                tables = business_logic["表范围"]
                if isinstance(tables, list):
                    description_parts.append(f"表范围：{tables}")
                else:
                    description_parts.append(f"表范围：{tables}")

            if business_logic.get("条件"):
                conditions = business_logic["条件"]
                description_parts.append(f"条件：{conditions}")

            if business_logic.get("维度"):
                dimensions = business_logic["维度"]
                description_parts.append(f"维度：{dimensions}")

            if business_logic.get("整体逻辑描述"):
                overall_desc = business_logic["整体逻辑描述"]
                description_parts.append(f"整体逻辑描述：{overall_desc}")

            result.bdr16_raw = "\n".join(description_parts)
            mapping_notes.append("BDR16映射成功: 业务逻辑描述")

    async def _map_sdr_fields(self, result: PipelineFieldMappingResult, parser_info: Dict, sql_candidates: List, candidate_columns: Dict, candidate_column_ids: Dict, mapping_notes: List[str]):
        """映射SDR字段"""
        # SDR05: 与BDR09相同
        result.sdr05_raw = result.bdr09_raw

        # SDR06: 与BDR10相同
        result.sdr06_raw = result.bdr10_raw

        # SDR08: 与BDR11相同，使用candidate_columns
        result.sdr08_raw = candidate_columns

        # SDR09: 使用candidate_column_ids生成字段中文名映射
        logger.debug(f"🔍 SDR09映射调试:")
        logger.debug(f"  candidate_column_ids类型: {type(candidate_column_ids)}")
        logger.debug(f"  candidate_column_ids内容: {candidate_column_ids}")

        # 🚨 断点标记：SDR09映射开始
        logger.debug(f"🚨 BREAKPOINT: SDR09映射开始 - 在这里设置断点")
        logger.debug(f"🚨 candidate_column_ids = {candidate_column_ids}")
        logger.debug(f"🚨 metadata_crud = {self.metadata_crud}")
        # 🚨 断点标记结束

        if candidate_column_ids:
            logger.debug(f"🚨 BREAKPOINT: candidate_column_ids不为空，开始处理")
            column_cn_dict = {}

            # 遍历candidate_column_ids: {table_id: [col_id, ...]}
            for table_id, col_ids in candidate_column_ids.items():
                logger.debug(f"🚨 BREAKPOINT: 处理table_id={table_id}, col_ids={col_ids}")
                logger.debug(f"  处理table_id={table_id}, col_ids={col_ids}")
                for col_id in col_ids:
                    logger.debug(f"🚨 BREAKPOINT: 查询col_id={col_id}")
                    try:
                        # 根据col_id查询字段信息
                        column_info = await self.metadata_crud.get_source_column(column_id=col_id)
                        logger.debug(f"🚨 BREAKPOINT: col_id={col_id}查询结果={column_info}")
                        if column_info:
                            column_name = column_info.get("column_name", "")
                            column_name_cn = column_info.get("column_name_cn", column_name)
                            if column_name:
                                column_cn_dict[column_name] = column_name_cn
                                logger.debug(f"🚨 BREAKPOINT: 添加映射 {column_name}:{column_name_cn}")
                                logger.debug(f"    col_id={col_id} → {column_name}:{column_name_cn}")
                    except Exception as e:
                        logger.debug(f"🚨 BREAKPOINT: 查询异常 col_id={col_id}, error={e}")
                        logger.warning(f"查询字段中文名失败: col_id={col_id}, error={e}")

            result.sdr09_raw = column_cn_dict
            mapping_notes.append(f"SDR09映射成功: 使用candidate_column_ids，{len(column_cn_dict)}个字段中文名")
            logger.debug(f"🚨 BREAKPOINT: SDR09映射完成，最终结果={column_cn_dict}")
            logger.debug(f"✅ SDR09映射成功: {len(column_cn_dict)}个字段中文名")
        else:
            logger.debug(f"🚨 BREAKPOINT: candidate_column_ids为空，跳过处理")
            result.sdr09_raw = {}
            mapping_notes.append("SDR09映射: candidate_column_ids为空")
            logger.warning(f"⚠️ SDR09映射失败: candidate_column_ids为空")

        # SDR10: SQL候选
        if sql_candidates:
            result.sdr10_raw = sql_candidates[0] if sql_candidates else ""
            mapping_notes.append("SDR10映射成功: SQL候选")

        # SDR12: JOIN条件（保持列表格式）
        logger.debug(f"🔍 SDR12映射调试 - record_id={result.record_id}")
        logger.debug(f"  parser_info类型: {type(parser_info)}")
        logger.debug(f"  parser_info内容: {parser_info}")
        join_info = parser_info.get("join")
        logger.debug(f"  parser_info.get('join'): {join_info} (类型: {type(join_info)})")
        logger.debug(f"  条件判断结果: {bool(parser_info.get('join'))}")

        if parser_info.get("join"):
            join_info = parser_info["join"]
            logger.debug(f"  ✅ 进入SDR12映射逻辑: join_info={join_info}")
            if isinstance(join_info, list):
                result.sdr12_raw = join_info  # 保持列表格式
                logger.debug(f"  ✅ SDR12设置为列表: {result.sdr12_raw}")
            else:
                result.sdr12_raw = [str(join_info)]  # 转换为单元素列表
                logger.debug(f"  ✅ SDR12转换为列表: {result.sdr12_raw}")
            mapping_notes.append("SDR12映射成功: join信息")
        else:
            logger.debug(f"  ❌ SDR12映射跳过: parser_info.get('join')为空或False")
            logger.debug(f"  parser_info所有键: {list(parser_info.keys()) if isinstance(parser_info, dict) else 'Not a dict'}")

    async def map_batch_records(
        self,
        pipeline_results: List[Dict[str, Any]],
        original_records: List[Any],
        keep_raw_format: bool = True
    ) -> List[PipelineFieldMappingResult]:
        """
        批量映射多个记录的Pipeline结果

        Args:
            pipeline_results: Pipeline执行结果列表
            original_records: 原始记录对象列表
            keep_raw_format: 是否保持原始格式（用于聚合）

        Returns:
            List[PipelineFieldMappingResult]: 映射结果列表
        """
        results = []

        for pipeline_result, original_record in zip(pipeline_results, original_records):
            mapping_result = await self.map_single_record(
                pipeline_result,
                original_record,
                keep_raw_format
            )
            results.append(mapping_result)

        logger.debug(f"批量字段映射完成: 处理了{len(results)}个记录")
        return results

    def aggregate_mapping_results(
        self,
        mapping_results: List[PipelineFieldMappingResult]
    ) -> AggregatedFieldResult:
        """
        聚合多个映射结果

        Args:
            mapping_results: 映射结果列表

        Returns:
            AggregatedFieldResult: 聚合后的结果
        """
        aggregated = AggregatedFieldResult()

        # 聚合各个字段
        for result in mapping_results:
            if not result.mapping_success:
                continue

            # 聚合BDR09: 表英文名
            if result.bdr09_raw:
                aggregated.bdr09_aggregated.extend(result.bdr09_raw)

            # 聚合BDR10: 表中文名
            if result.bdr10_raw:
                aggregated.bdr10_aggregated.extend(result.bdr10_raw)

            # 聚合BDR11: 字段信息
            if result.bdr11_raw:
                aggregated.bdr11_aggregated.update(result.bdr11_raw)

            # 聚合SDR字段
            if result.sdr05_raw:
                aggregated.sdr05_aggregated.extend(result.sdr05_raw)
            if result.sdr06_raw:
                aggregated.sdr06_aggregated.extend(result.sdr06_raw)
            if result.sdr08_raw:
                aggregated.sdr08_aggregated.update(result.sdr08_raw)
            if result.sdr09_raw:
                aggregated.sdr09_aggregated.update(result.sdr09_raw)

            # 收集SDR12候选值
            if result.sdr12_raw:
                aggregated.sdr12_candidates.append(result.sdr12_raw)

        # 去重处理
        aggregated.bdr09_aggregated = list(set(aggregated.bdr09_aggregated))
        aggregated.bdr10_aggregated = list(set(aggregated.bdr10_aggregated))
        aggregated.sdr05_aggregated = list(set(aggregated.sdr05_aggregated))
        aggregated.sdr06_aggregated = list(set(aggregated.sdr06_aggregated))

        logger.debug(f"字段聚合完成: bdr09={len(aggregated.bdr09_aggregated)}个表, "
                   f"bdr11={len(aggregated.bdr11_aggregated)}个字段, "
                   f"sdr12={len(aggregated.sdr12_candidates)}个候选值")

        return aggregated

    def convert_to_string_format(
        self,
        mapping_results: List[PipelineFieldMappingResult]
    ) -> List[Dict[str, str]]:
        """
        将映射结果转换为字符串格式

        Args:
            mapping_results: 映射结果列表

        Returns:
            List[Dict[str, str]]: 字符串格式的结果列表
        """
        string_results = []

        for result in mapping_results:
            if result.mapping_success:
                string_result = result.to_string_format()
                string_result['record_id'] = str(result.record_id)
                string_results.append(string_result)

        logger.debug(f"格式转换完成: {len(string_results)}个记录转换为字符串格式")
        return string_results
