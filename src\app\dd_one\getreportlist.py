import os
import re
import docx
from typing import List, Dict, Optional
from app.dd_one.doc_parse.parses.simple_utils.utils.orig_sheet_reader import get_excel_type
from app.dd_one.doc_parse.parses.simple_utils.dd_utils.convert_doc import convert_doc_to_docx, accept_all_revisions
from app.dd_one.config.config import BASE_PATH, TMP_PATH
from pathlib import Path

# 定义数据结构
FileInfo = Dict[str, str]
ReportInfo = Dict[str, str]
FolderFiles = Dict[str, List[Dict[str, str]]]


def upper_part(text: str) -> dict:
    upper_dict = {}
    match = re.match(r'^[^\u4e00-\u9fa5]+', text)
    requirement_doc_name = match.group(0).strip() if match else ''
    requirement_doc_name = requirement_doc_name.replace('《', "")
    upper_dict['表名ID'] = requirement_doc_name.strip()
    upper_dict['需求文件名称'] = text.strip()
    return upper_dict


def determine_folder_type(file_path: str) -> Optional[str]:
    """根据文件路径判断文件夹类型"""
    folder = os.path.dirname(file_path)
    if folder.startswith('A'):
        return 'djz'
    elif folder.startswith('G'):
        return '1104'
    return None  # 不含文件夹的文件将在主逻辑中处理为 'survey'


def group_files_by_folder(file_info: List[FileInfo]) -> FolderFiles:
    """将文件按文件夹分组，并添加文件信息"""
    folder_files: FolderFiles = {}
    for file in file_info:
        full_path = os.path.join(BASE_PATH, file['file_path'])
        folder = os.path.dirname(file['file_path'])
        folder_type = determine_folder_type(file['file_path'])
        if not folder_type:
            continue
        if folder not in folder_files:
            folder_files[folder] = []
        folder_files[folder].append({
            'file_id': file['file_id'],
            'full_path': full_path,
            'extension': os.path.splitext(file['file_path'])[1].lower(),
            'folder_type': folder_type
        })
    return folder_files


def extract_report_name(doc_path: str) -> str:
    """从文档中提取报告名称"""
    try:
        converted_doc_path = convert_doc_to_docx(doc_path, os.path.join(TMP_PATH, 'converted'))
        doc_data = accept_all_revisions(converted_doc_path).strip()
        return doc_data.split('\n')[0].strip().replace('填报说明', '') if doc_data else "未知表"
    except Exception as e:
        print(f"处理文档 {doc_path} 时出错: {str(e)}")
        return "未知表"


def determine_report_type(excel_path: str, folder_type: str) -> str:
    """根据文件路径和文件夹类型确定报告类型"""
    if folder_type == '1104':
        return get_excel_type(os.path.join(BASE_PATH, excel_path), '')
    elif folder_type == 'djz':
        return 'index'
    return 'detail'


def upper_part(text: str) -> dict:
    upper_dict = {}
    match = re.match(r'^[^\u4e00-\u9fa5]+', text)
    requirement_doc_name = match.group(0).strip() if match else ''
    requirement_doc_name = requirement_doc_name.replace('《', "")
    upper_dict['表名ID'] = requirement_doc_name.split('（')[0].strip()
    upper_dict['需求文件名称'] = text.strip()
    return upper_dict


def create_report_info(excel_file: Dict[str, str], version: str, version_scene: str, level: str,
                       doc_file: Optional[Dict[str, str]] = None) -> ReportInfo:
    print(excel_file)
    """创建单个报告信息"""
    filename_without_ext = upper_part(Path(excel_file['full_path']).stem)["表名ID"]
    print(filename_without_ext)
    folder_type = excel_file.get('folder_type', 'survey')  # 默认 survey 用于无文件夹情况
    report_type = determine_report_type(
        excel_file['full_path'] if 'full_path' in excel_file else excel_file['file_path'], folder_type)
    report_name = extract_report_name(doc_file['full_path']) if doc_file else "未知表"

    # 新增 report_code 拼接逻辑
    report_code_parts = [filename_without_ext, level, version_scene]
    if version:
        report_code_parts.append(version)
    report_code = '_'.join(report_code_parts)

    return {
        "report_code": report_code,
        "report_name": report_name,
        "report_freq": "month",
        "report_type": report_type,
        "table_id": excel_file['file_id'],
        "comments_id": doc_file['file_id'] if doc_file else ""
    }


def process_two_file_case(file_info: List[FileInfo], version: str, version_scene: str, level: str) -> Optional[
    List[ReportInfo]]:
    """处理两个文件的情况：一个 Excel 文件，一个文档文件"""
    if len(file_info) != 2:
        return None

    excel_file = next((f for f in file_info if f["file_path"].endswith(('.xls', '.xlsx', '.et'))), None)
    doc_file = next((f for f in file_info if f["file_path"].endswith(('.doc', '.docx', '.wps'))), None)

    if excel_file and doc_file:
        return [create_report_info(
            {'file_id': excel_file['file_id'], 'full_path': excel_file['file_path'], 'folder_type': 'survey'},
            version, version_scene, level,
            {'file_id': doc_file['file_id'], 'full_path': os.path.join(BASE_PATH, doc_file['file_path'])}
        )]
    return None


def process_no_folder_files(file_info: List[FileInfo], version: str, version_scene: str, level: str) -> List[
    ReportInfo]:
    """处理不含文件夹路径的 Excel 文件"""
    report_list = []
    excel_files = [
        f for f in file_info
        if f["file_path"].endswith(('.xls', '.xlsx', '.et'))
           and os.path.dirname(f["file_path"]) == ''
    ]

    for excel_file in excel_files:
        report_list.append(create_report_info(
            {'file_id': excel_file['file_id'], 'file_path': excel_file['file_path'],
             'full_path': os.path.join(BASE_PATH, excel_file['file_path']), 'folder_type': 'survey'},
            version, version_scene, level
        ))

    return report_list


def getreportlist(file_info: List[FileInfo], version: str, version_scene: str, level: str) -> List[ReportInfo]:
    """
    根据文件列表生成报告信息列表
    :param file_info: 包含文件信息的字典列表 [{"file_id": "", "file_path": ""}]
    :param version: 版本号
    :param version_scene: 版本场景
    :param level: 层级
    :return: 报告信息列表
    """
    report_list = []

    # 优先处理两个文件的情况（一个 Excel，一个文档）
    two_file_result = process_two_file_case(file_info, version, version_scene, level)
    if two_file_result is not None:
        return two_file_result

    # 检查是否包含文件夹路径的文件
    has_folder = any(os.path.dirname(f["file_path"]) != '' for f in file_info)

    if has_folder:
        # 仅处理含文件夹的文件
        folder_files = group_files_by_folder(file_info)
        for folder, files in folder_files.items():
            doc_file = next((f for f in files if f['extension'] in ['.doc', '.docx', '.wps']), None)
            excel_file = next((f for f in files if f['extension'] in ['.xlsx', '.xls', '.et']), None)

            if excel_file:
                try:
                    report_list.append(create_report_info(excel_file, version, version_scene, level, doc_file))
                except Exception as e:
                    print(f"处理文件夹 {folder} 时出错: {str(e)}")
                    continue
    else:
        # 处理不含文件夹的 Excel 文件
        report_list.extend(process_no_folder_files(file_info, version, version_scene, level))

    return report_list