"""
Metadata系统关联关系CRUD操作

包含以下实体的CRUD操作：
- 源关联键信息 (md_source_key_relation_info)
- 指标关联键信息 (md_index_key_relation_info)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudRelations(MetadataCrudBase):
    """Metadata系统关联关系CRUD操作"""

    # ==================== 源关联键信息操作 ====================

    async def create_source_key_relation(self, relation_data: Dict[str, Any]) -> <PERSON><PERSON>[int, List[Dict[str, Any]]]:
        """
        创建源关联键信息

        Args:
            relation_data: 关联键数据，必须包含：
                - source_column_id: 源字段ID
                - target_column_id: 目标字段ID
                - relation_type: 关联类型（可选，默认'FK'）
                - comment: 备注说明（可选）

        Returns:
            (关联键ID, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataConflictError: 关联键已存在
            MetadataError: 创建失败
        """
        try:
            # 数据验证
            MetadataUtils.validate_source_key_relation_data(relation_data)

            # 检查是否已存在相同的关联键（根据业务唯一键）
            existing = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'source_column_id': relation_data['source_column_id'],
                    'target_column_id': relation_data['target_column_id'],
                    'relation_type': relation_data.get('relation_type', 'FK')
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"源关联键信息已存在: 知识库{relation_data['knowledge_id']} 源字段{relation_data['source_column_id']} -> 目标字段{relation_data['target_column_id']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(relation_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建源关联键信息失败: {error_msg}")

            # 获取插入的ID（使用主键字段 relation_id）
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'source_column_id': relation_data['source_column_id'],
                    'target_column_id': relation_data['target_column_id'],
                    'relation_type': relation_data.get('relation_type', 'FK')
                },
                limit=1
            )

            relation_id = inserted_records[0].get('relation_id', 0) if inserted_records else 0

            # 源关联键信息通常不需要向量化，但保留接口以备扩展
            vector_results = []
            # 如果需要向量化，可以取消注释以下代码：
            # if self.vdb_client and self.embedding_client and relation_id:
            #     try:
            #         vector_results = await self._create_vectors('source_key_relation', relation_id, relation_data)
            #     except Exception as e:
            #         logger.warning(f"源关联键信息向量化失败: {e}")

            logger.info(f"源关联键信息创建成功: 源字段{relation_data['source_column_id']} -> 目标字段{relation_data['target_column_id']} (ID: {relation_id})")
            return relation_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建源关联键信息失败: {e}")
            raise MetadataError(f"创建源关联键信息失败: {e}")

    async def get_source_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_source_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(relation_data, is_update=True)

        try:
            updates = [{"data": relation_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新源关联键信息失败: {e}")
            raise MetadataError(f"更新源关联键信息失败: {e}")

    async def delete_source_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除源关联键信息失败: {e}")
            raise MetadataError(f"删除源关联键信息失败: {e}")

    async def list_source_key_relations(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询源关联键信息列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    async def batch_create_source_key_relations(self, relations_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源关联键信息

        Args:
            relations_data: 关联键数据列表

        Returns:
            (关联键ID列表, 向量创建结果列表的列表)

        Raises:
            MetadataError: 批量创建失败
        """
        try:
            # 数据验证
            for relation_data in relations_data:
                MetadataUtils.validate_source_key_relation_data(relation_data)
                MetadataUtils.add_timestamps(relation_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                data=relations_data,
                batch_size=len(relations_data) if len(relations_data) <= 100 else 100,
                max_concurrency=1 if len(relations_data) <= 10 else 3
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建源关联键信息失败: {error_msg}")

            # 获取插入的ID（使用正确的字段结构）
            relation_ids = []
            vector_results_list = []

            for relation_data in relations_data:
                inserted_records = await self._aselect(
                    table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                    where={
                        'source_column_id': relation_data['source_column_id'],
                        'target_column_id': relation_data['target_column_id'],
                        'relation_type': relation_data.get('relation_type', 'FK')
                    },
                    limit=1
                )
                relation_id = inserted_records[0].get('relation_id', 0) if inserted_records else 0
                relation_ids.append(relation_id)
                vector_results_list.append([])  # 暂时不进行向量化

            logger.info(f"批量创建源关联键信息成功: {len(relation_ids)} 个")
            return relation_ids, vector_results_list

        except Exception as e:
            logger.error(f"批量创建源关联键信息失败: {e}")
            raise MetadataError(f"批量创建源关联键信息失败: {e}")

    async def search_source_key_relations(
        self,
        search_text: str,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        limit: Optional[int] = 10
    ) -> List[Dict[str, Any]]:
        """
        搜索源关联键信息

        Args:
            search_text: 搜索文本
            knowledge_id: 知识库ID（可选）
            db_id: 数据库ID（可选，但表中没有此字段，忽略）
            limit: 限制数量（可选）

        Returns:
            匹配的关联键信息列表
        """
        where = {}

        # 现在表中有 knowledge_id 字段了
        if knowledge_id:
            where['knowledge_id'] = knowledge_id

        # 按照 comment 字段进行搜索
        if search_text:
            where['comment'] = f"%{search_text}%"

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit
        )

    # ==================== 指标关联键信息操作 ====================

    async def create_index_key_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标关联键信息"""
        try:
            # 数据验证
            MetadataUtils.validate_index_key_relation_data(relation_data)

            # 添加时间戳
            MetadataUtils.add_timestamps(relation_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"创建指标关联键信息失败: {error_msg}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'db_id': relation_data['db_id'],
                    'relation_name': relation_data['relation_name']
                },
                limit=1
            )

            relation_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 关联键信息通常不需要向量化，但保留接口以备扩展
            vector_results = []

            logger.info(f"指标关联键信息创建成功: {relation_data['relation_name']} (ID: {relation_id})")
            return relation_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建指标关联键信息失败: {e}")
            raise MetadataError(f"创建指标关联键信息失败: {e}")

    async def get_index_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_index_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(relation_data, is_update=True)

        try:
            updates = [{"data": relation_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新指标关联键信息失败: {e}")
            raise MetadataError(f"更新指标关联键信息失败: {e}")

    async def delete_index_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除指标关联键信息失败: {e}")
            raise MetadataError(f"删除指标关联键信息失败: {e}")

    # ==================== 批量操作方法 ====================

    async def batch_create_source_key_relations(
        self,
        relations_data: List[Dict[str, Any]],
        batch_size: int = 500,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源关联键信息

        Args:
            relations_data: 关联关系数据列表
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数
            timeout_per_batch: 每批次超时时间

        Returns:
            (关联ID列表, 向量创建结果列表)
        """
        if not relations_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for relation_data in relations_data:
                MetadataUtils.validate_source_key_relation_data(relation_data)
                MetadataUtils.add_timestamps(relation_data)
                processed_data.append(relation_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                data=processed_data,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建源关联键信息失败: {error_msg}")

            # 获取插入的ID
            relation_ids = []
            if processed_data:
                # 构建批量查询条件
                where_conditions = []
                for relation_data in processed_data:
                    where_conditions.append({
                        'knowledge_id': relation_data['knowledge_id'],
                        'source_column_id': relation_data['source_column_id'],
                        'target_column_id': relation_data['target_column_id'],
                        'relation_type': relation_data.get('relation_type', 'FK')
                    })

                # 批量查询获取ID
                batch_results = await self.rdb_client.abatch_query(
                    table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                    queries=[{
                        "table": MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                        "data": ["relation_id"],
                        "filters": condition,
                        "limit": 1
                    } for condition in where_conditions],
                    batch_size=batch_size,
                    max_concurrency=max_concurrency,
                    timeout_per_batch=timeout_per_batch
                )

                for query_response in batch_results:
                    if hasattr(query_response, 'data') and query_response.data:
                        relation_ids.append(query_response.data[0]['relation_id'])
                    else:
                        relation_ids.append(0)

            # 关联关系表不需要向量化
            vector_results = [[] for _ in relation_ids]

            logger.info(f"批量创建源关联键信息成功: {len(relation_ids)} 个关联关系")
            return relation_ids, vector_results

        except Exception as e:
            logger.error(f"批量创建源关联键信息失败: {e}")
            raise MetadataError(f"批量创建源关联键信息失败: {e}")

    async def batch_create_index_key_relations(
        self,
        relations_data: List[Dict[str, Any]],
        batch_size: int = 500,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建指标关联键信息"""
        if not relations_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for relation_data in relations_data:
                MetadataUtils.validate_index_key_relation_data(relation_data)
                MetadataUtils.add_timestamps(relation_data)
                processed_data.append(relation_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                data=processed_data,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建指标关联键信息失败: {error_msg}")

            # 获取插入的ID
            relation_ids = []
            if processed_data:
                # 构建批量查询条件
                where_conditions = []
                for relation_data in processed_data:
                    where_conditions.append({
                        'knowledge_id': relation_data['knowledge_id'],
                        'source_column_id': relation_data['source_column_id'],
                        'target_column_id': relation_data['target_column_id'],
                        'relation_type': relation_data.get('relation_type', 'FK')
                    })

                # 批量查询获取ID
                batch_results = await self.rdb_client.abatch_query(
                    table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                    queries=[{
                        "table": MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                        "data": ["relation_id"],
                        "filters": condition,
                        "limit": 1
                    } for condition in where_conditions],
                    batch_size=batch_size,
                    max_concurrency=max_concurrency,
                    timeout_per_batch=timeout_per_batch
                )

                for query_response in batch_results:
                    if hasattr(query_response, 'data') and query_response.data:
                        relation_ids.append(query_response.data[0]['relation_id'])
                    else:
                        relation_ids.append(0)

            # 关联关系表不需要向量化
            vector_results = [[] for _ in relation_ids]

            logger.info(f"批量创建指标关联键信息成功: {len(relation_ids)} 个关联关系")
            return relation_ids, vector_results

        except Exception as e:
            logger.error(f"批量创建指标关联键信息失败: {e}")
            raise MetadataError(f"批量创建指标关联键信息失败: {e}")

    # ==================== 统一的批量操作辅助方法 ====================

    async def _unified_update(
        self,
        table: str,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """统一的更新方法 - 关联关系表不需要向量处理"""
        try:
            # 标准化输入参数
            if isinstance(updates, dict) and conditions is not None:
                # 单条更新模式
                if isinstance(conditions, dict):
                    update_list = [{"data": updates, "filters": conditions}]
                elif isinstance(conditions, list):
                    # 多条件单数据更新
                    update_list = [{"data": updates, "filters": cond} for cond in conditions]
                else:
                    raise MetadataValidationError("conditions 必须是字典或字典列表")

                # 单条或少量更新使用较小的并发参数
                if len(update_list) == 1:
                    batch_size = 1
                    max_concurrency = 1
                    timeout_per_batch = 60.0

            elif isinstance(updates, list):
                # 批量更新模式
                update_list = []
                for update in updates:
                    if isinstance(update, dict) and 'data' in update and 'filters' in update:
                        update_list.append({"data": update['data'], "filters": update['filters']})
                    else:
                        raise MetadataValidationError("批量更新格式错误，应为 [{'data': {...}, 'filters': {...}}, ...]")
            else:
                raise MetadataValidationError("updates 参数格式错误")

            # 执行批量更新
            result = await self.rdb_client.abatch_update(
                table=table,
                updates=update_list,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"统一更新操作失败: table={table}, error={e}")
            raise MetadataError(f"统一更新操作失败: {e}")

    async def _unified_delete(
        self,
        table: str,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """统一的删除方法 - 关联关系表不需要向量处理"""
        try:
            # 标准化输入参数
            if isinstance(conditions, dict):
                # 单条删除
                condition_list = [conditions]
                # 单条删除使用较小的并发参数
                batch_size = 1
                max_concurrency = 1
                timeout_per_batch = 60.0
            elif isinstance(conditions, list):
                # 批量删除
                condition_list = conditions
            else:
                raise MetadataValidationError("conditions 必须是字典或字典列表")

            if not condition_list:
                raise MetadataValidationError("删除条件不能为空")

            # 执行批量删除
            result = await self.rdb_client.abatch_delete(
                table=table,
                conditions=condition_list,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"统一删除操作失败: table={table}, error={e}")
            raise MetadataError(f"统一删除操作失败: {e}")

    async def _unified_query(
        self,
        table: str,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """统一的查询方法 - 高性能批量查询"""
        if not conditions_list:
            return []

        try:
            # 构建查询列表
            queries = []
            for condition in conditions_list:
                query = {
                    "table": table,
                    "data": ["*"],  # 选择所有字段
                    "filters": condition,
                    "limit": 1000  # 每个查询的最大记录数
                }
                queries.append(query)

            logger.info(f"开始批量查询: table={table}, 查询数量={len(queries)}, "
                       f"批次大小={batch_size}, 最大并发={max_concurrency}")

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=table,
                queries=queries,
                batch_size=batch_size,
                max_concurrency=max_concurrency,
                timeout_per_batch=timeout_per_batch
            )

            # 合并所有查询结果
            all_records = []
            successful_queries = 0
            total_records = 0

            for i, query_response in enumerate(batch_results):
                try:
                    if hasattr(query_response, 'data') and query_response.data:
                        records = query_response.data
                        all_records.extend(records)
                        total_records += len(records)
                        successful_queries += 1
                    else:
                        logger.debug(f"查询 {i+1} 无结果: 条件={conditions_list[i]}")
                except Exception as e:
                    logger.warning(f"处理查询结果 {i+1} 失败: {e}")

            logger.info(f"批量查询完成: table={table}, 成功查询={successful_queries}/{len(queries)}, "
                       f"总记录数={total_records}")

            return all_records

        except Exception as e:
            logger.error(f"统一查询操作失败: table={table}, error={e}")
            raise MetadataError(f"统一查询操作失败: {e}")

    # ==================== 源关联键信息批量操作 ====================

    async def batch_update_source_key_relations(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量更新源关联键信息"""
        return await self._unified_update(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def batch_delete_source_key_relations(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量删除源关联键信息"""
        return await self._unified_delete(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def batch_query_source_key_relations(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """批量查询源关联键信息"""
        return await self._unified_query(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            conditions_list=conditions_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    # ==================== 指标关联键信息批量操作 ====================

    async def batch_update_index_key_relations(
        self,
        updates: Union[Dict[str, Any], List[Dict[str, Any]]],
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]] = None,
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量更新指标关联键信息"""
        return await self._unified_update(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            updates=updates,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def batch_delete_index_key_relations(
        self,
        conditions: Union[Dict[str, Any], List[Dict[str, Any]]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> bool:
        """批量删除指标关联键信息"""
        return await self._unified_delete(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            conditions=conditions,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def batch_query_index_key_relations(
        self,
        conditions_list: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[Dict[str, Any]]:
        """批量查询指标关联键信息"""
        return await self._unified_query(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            conditions_list=conditions_list,
            batch_size=batch_size,
            max_concurrency=max_concurrency,
            timeout_per_batch=timeout_per_batch
        )

    async def list_index_key_relations(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询指标关联键信息列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )
