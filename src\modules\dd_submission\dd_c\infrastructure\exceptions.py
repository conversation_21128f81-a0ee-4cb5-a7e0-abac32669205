"""
DD-B模块异常定义

定义DD-B模块的异常类和错误处理机制
"""

import logging
from typing import Any, Dict, List, Optional
from functools import wraps

from .constants import DDBErrorCodes

logger = logging.getLogger(__name__)


class DDBError(Exception):
    """DD-B模块基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or DDBErrorCodes.UNKNOWN_ERROR
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"


class DDBValidationError(DDBError):
    """DD-B验证异常"""
    
    def __init__(self, message: str, field_name: str = None, field_value: Any = None, details: Dict[str, Any] = None):
        self.field_name = field_name
        self.field_value = field_value
        
        error_details = details or {}
        if field_name:
            error_details["field_name"] = field_name
        if field_value is not None:
            error_details["field_value"] = field_value
        
        super().__init__(
            message=message,
            error_code=DDBErrorCodes.VALIDATION_ERROR,
            details=error_details
        )


class DDBDatabaseError(DDBError):
    """DD-B数据库异常"""
    
    def __init__(self, message: str, operation: str = None, table_name: str = None, details: Dict[str, Any] = None):
        self.operation = operation
        self.table_name = table_name
        
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        if table_name:
            error_details["table_name"] = table_name
        
        super().__init__(
            message=message,
            error_code=DDBErrorCodes.DATABASE_ERROR,
            details=error_details
        )


class DDBProcessingError(DDBError):
    """DD-B处理异常"""
    
    def __init__(self, message: str, processing_step: str = None, record_id: Any = None, details: Dict[str, Any] = None):
        self.processing_step = processing_step
        self.record_id = record_id
        
        error_details = details or {}
        if processing_step:
            error_details["processing_step"] = processing_step
        if record_id is not None:
            error_details["record_id"] = record_id
        
        super().__init__(
            message=message,
            error_code=DDBErrorCodes.PROCESSING_FAILED,
            details=error_details
        )


class DDBTimeoutError(DDBError):
    """DD-B超时异常"""
    
    def __init__(self, message: str, timeout_seconds: float = None, operation: str = None, details: Dict[str, Any] = None):
        self.timeout_seconds = timeout_seconds
        self.operation = operation
        
        error_details = details or {}
        if timeout_seconds:
            error_details["timeout_seconds"] = timeout_seconds
        if operation:
            error_details["operation"] = operation
        
        super().__init__(
            message=message,
            error_code=DDBErrorCodes.TIMEOUT_ERROR,
            details=error_details
        )


class DDBDataNotFoundError(DDBError):
    """DD-B数据未找到异常"""
    
    def __init__(self, message: str, query_conditions: Dict[str, Any] = None, details: Dict[str, Any] = None):
        self.query_conditions = query_conditions
        
        error_details = details or {}
        if query_conditions:
            error_details["query_conditions"] = query_conditions
        
        super().__init__(
            message=message,
            error_code=DDBErrorCodes.NO_RECORDS_FOUND,
            details=error_details
        )


# 错误创建工具函数
def create_validation_error(message: str, field_name: str = None, field_value: Any = None) -> DDBValidationError:
    """创建验证错误"""
    return DDBValidationError(message, field_name, field_value)


def create_database_error(message: str, operation: str = None, table_name: str = None) -> DDBDatabaseError:
    """创建数据库错误"""
    return DDBDatabaseError(message, operation, table_name)


def create_processing_error(message: str, processing_step: str = None, record_id: Any = None) -> DDBProcessingError:
    """创建处理错误"""
    return DDBProcessingError(message, processing_step, record_id)


def create_timeout_error(message: str, timeout_seconds: float = None, operation: str = None) -> DDBTimeoutError:
    """创建超时错误"""
    return DDBTimeoutError(message, timeout_seconds, operation)


def create_data_not_found_error(message: str, query_conditions: Dict[str, Any] = None) -> DDBDataNotFoundError:
    """创建数据未找到错误"""
    return DDBDataNotFoundError(message, query_conditions)


# 异常处理装饰器
def handle_ddb_errors(func):
    """DD-B异常处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DDBError:
            # DD-B异常直接重新抛出
            raise
        except ValueError as e:
            # 值错误转换为验证异常
            logger.error(f"值错误: {e}")
            raise create_validation_error(f"参数验证失败: {str(e)}")
        except Exception as e:
            # 其他异常转换为通用DD-B异常
            logger.error(f"未知错误: {e}")
            raise DDBError(f"处理过程中发生未知错误: {str(e)}")
    
    return wrapper


def handle_async_ddb_errors(func):
    """DD-B异步异常处理装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except DDBError:
            # DD-B异常直接重新抛出
            raise
        except ValueError as e:
            # 值错误转换为验证异常
            logger.error(f"值错误: {e}")
            raise create_validation_error(f"参数验证失败: {str(e)}")
        except Exception as e:
            # 其他异常转换为通用DD-B异常
            logger.error(f"未知错误: {e}")
            raise DDBError(f"处理过程中发生未知错误: {str(e)}")
    
    return wrapper


# 错误信息格式化工具
class DDBErrorFormatter:
    """DD-B错误信息格式化工具"""
    
    @staticmethod
    def format_error_response(error: DDBError) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            "success": False,
            "error": {
                "code": error.error_code,
                "message": error.message,
                "details": error.details
            }
        }
    
    @staticmethod
    def format_validation_errors(errors: List[str]) -> Dict[str, Any]:
        """格式化验证错误列表"""
        return {
            "success": False,
            "error": {
                "code": DDBErrorCodes.VALIDATION_ERROR,
                "message": "数据验证失败",
                "details": {
                    "validation_errors": errors,
                    "error_count": len(errors)
                }
            }
        }
    
    @staticmethod
    def format_processing_summary(errors: List[DDBError], warnings: List[str] = None) -> Dict[str, Any]:
        """格式化处理摘要"""
        return {
            "processing_summary": {
                "total_errors": len(errors),
                "total_warnings": len(warnings) if warnings else 0,
                "errors": [error.to_dict() for error in errors],
                "warnings": warnings or []
            }
        }


# 错误恢复策略
class DDBErrorRecovery:
    """DD-B错误恢复策略"""
    
    @staticmethod
    def should_retry(error: DDBError) -> bool:
        """判断是否应该重试"""
        # 数据库连接错误可以重试
        if isinstance(error, DDBDatabaseError):
            return True
        
        # 超时错误可以重试
        if isinstance(error, DDBTimeoutError):
            return True
        
        # 验证错误不应该重试
        if isinstance(error, DDBValidationError):
            return False
        
        # 数据未找到错误不应该重试
        if isinstance(error, DDBDataNotFoundError):
            return False
        
        return False
    
    @staticmethod
    def get_retry_delay(attempt: int) -> float:
        """获取重试延迟时间（指数退避）"""
        return min(2 ** attempt, 30)  # 最大30秒
    
    @staticmethod
    def get_max_retries(error: DDBError) -> int:
        """获取最大重试次数"""
        if isinstance(error, DDBDatabaseError):
            return 3
        elif isinstance(error, DDBTimeoutError):
            return 2
        else:
            return 0
