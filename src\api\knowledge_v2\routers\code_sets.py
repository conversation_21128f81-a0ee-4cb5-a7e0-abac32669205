"""
Knowledge V2 API - 码值集管理路由

基于批量操作的高性能码值集管理API
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends

from ..models.request_models import (
    BatchCreateRequest,
    BatchUpdateRequest,
    BatchDeleteRequest,
    UnifiedGetRequest,
    UnifiedListRequest
)
from ..models.response_models import (
    BatchOperationResponse,
    UnifiedResponse,
    UnifiedListResponse,
    ErrorResponse
)
from ..dependencies import (
    get_codes_crud,
    validate_batch_config,
    create_batch_stats,
    log_batch_operation,
    handle_api_errors,
    PerformanceMonitor
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/code-sets", tags=["码值集管理V2"])


@router.post("/batch", response_model=BatchOperationResponse, summary="批量创建码值集")
@handle_api_errors
async def batch_create_code_sets(
    request: BatchCreateRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量创建码值集
    
    - **code_sets**: 码值集数据列表
    - **config**: 批量操作配置（可选）
    
    支持高并发批量创建，自动处理向量数据同步
    """
    with PerformanceMonitor("批量创建码值集") as monitor:
        # 验证配置
        config = validate_batch_config(request.config)
        
        # 转换数据格式
        code_sets_data = [code_set.dict() for code_set in request.code_sets]
        
        try:
            # 执行批量创建
            code_set_ids, vector_results = await codes_crud.batch_create_code_sets(
                code_sets_data,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            # 创建统计信息
            stats = create_batch_stats(
                total_requested=len(code_sets_data),
                total_successful=len(code_set_ids),
                execution_time=monitor.execution_time,
                batch_count=len(code_sets_data) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            # 记录日志
            log_batch_operation(
                operation="创建",
                entity_type="码值集",
                count=len(code_set_ids),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return BatchOperationResponse(
                success=True,
                message=f"成功创建 {len(code_set_ids)} 个码值集",
                data=code_set_ids,
                stats=stats,
                created_ids=code_set_ids,
                affected_rows=len(code_set_ids)
            )
            
        except Exception as e:
            logger.error(f"批量创建码值集失败: {e}")
            stats = create_batch_stats(
                total_requested=len(code_sets_data),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量创建码值集失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.put("/batch", response_model=BatchOperationResponse, summary="批量更新码值集")
@handle_api_errors
async def batch_update_code_sets(
    request: BatchUpdateRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量更新码值集
    
    - **updates**: 更新数据列表
    - **conditions**: 更新条件列表
    - **config**: 批量操作配置（可选）
    """
    with PerformanceMonitor("批量更新码值集") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量更新
            success = await codes_crud.batch_update_code_sets(
                updates=request.updates,
                conditions=request.conditions,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            stats = create_batch_stats(
                total_requested=len(request.updates),
                total_successful=len(request.updates) if success else 0,
                execution_time=monitor.execution_time,
                batch_count=len(request.updates) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="更新",
                entity_type="码值集",
                count=len(request.updates),
                execution_time=monitor.execution_time,
                success=success
            )
            
            return BatchOperationResponse(
                success=success,
                message=f"批量更新码值集{'成功' if success else '失败'}",
                stats=stats,
                affected_rows=len(request.updates) if success else 0
            )
            
        except Exception as e:
            logger.error(f"批量更新码值集失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.updates),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量更新码值集失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.delete("/batch", response_model=BatchOperationResponse, summary="批量删除码值集")
@handle_api_errors
async def batch_delete_code_sets(
    request: BatchDeleteRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量删除码值集
    
    - **conditions**: 删除条件列表
    - **config**: 批量操作配置（可选）
    
    注意：删除码值集会级联删除相关的码值关联记录
    """
    with PerformanceMonitor("批量删除码值集") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量删除
            success = await codes_crud.batch_delete_code_sets(
                conditions=request.conditions,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=len(request.conditions) if success else 0,
                execution_time=monitor.execution_time,
                batch_count=len(request.conditions) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="删除",
                entity_type="码值集",
                count=len(request.conditions),
                execution_time=monitor.execution_time,
                success=success
            )
            
            return BatchOperationResponse(
                success=success,
                message=f"批量删除码值集{'成功' if success else '失败'}",
                stats=stats,
                affected_rows=len(request.conditions) if success else 0
            )
            
        except Exception as e:
            logger.error(f"批量删除码值集失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.conditions),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return BatchOperationResponse(
                success=False,
                message=f"批量删除码值集失败: {str(e)}",
                stats=stats,
                errors=[{"error": str(e), "type": type(e).__name__}]
            )


@router.post("/batch/query", response_model=UnifiedListResponse, summary="批量查询码值集")
@handle_api_errors
async def batch_query_code_sets(
    request: UnifiedListRequest,
    codes_crud = Depends(get_codes_crud)
):
    """
    批量查询码值集
    
    - **queries**: 查询条件列表
    - **config**: 批量操作配置（可选）
    """
    with PerformanceMonitor("批量查询码值集") as monitor:
        config = validate_batch_config(request.config)
        
        try:
            # 执行批量查询
            results = await codes_crud.batch_query_code_sets(
                conditions_list=request.queries,
                batch_size=config.batch_size,
                max_concurrency=config.max_concurrency,
                timeout_per_batch=config.timeout_per_batch
            )
            
            # 计算总记录数
            total_records = sum(len(result) for result in results)
            
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=len(results),
                execution_time=monitor.execution_time,
                batch_count=len(request.queries) // config.batch_size + 1,
                concurrency_used=config.max_concurrency
            )
            
            log_batch_operation(
                operation="查询",
                entity_type="码值集",
                count=len(request.queries),
                execution_time=monitor.execution_time,
                success=True
            )
            
            return UnifiedListResponse(
                success=True,
                message=f"成功查询 {len(results)} 批码值集，共 {total_records} 条记录",
                data=results,
                pagination={"total_records": total_records, "page": 1, "page_size": len(results)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"批量查询码值集失败: {e}")
            stats = create_batch_stats(
                total_requested=len(request.queries),
                total_successful=0,
                execution_time=monitor.execution_time
            )
            
            return UnifiedListResponse(
                success=False,
                message=f"批量查询码值集失败: {str(e)}",
                data=[],
                pagination={"total_records": 0, "page": 1, "page_size": 0},
                timestamp=datetime.now()
            )
