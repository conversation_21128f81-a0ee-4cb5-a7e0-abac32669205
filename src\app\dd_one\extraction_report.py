import json
import os
from app.dd_one.config.config import BASE_PATH, TMP_PATH, NULL_DOC_BASH
from app.dd_one.doc_parse.document_parse_interface import DocumentParserInterface
from app.dd_one.doc_parse.parses.simple_parse import SimpleExcelParser
from app.dd_one.doc_parse.parses.gc_parse import GCExcelParser
from itertools import count

# 创建接口实例
interface = DocumentParserInterface()


def get_report_data(csv_path: str, type: str, file_path: str = ""):
    if file_path == "":
        file_path = NULL_DOC_BASH
    file_path = os.path.join(BASE_PATH, file_path)
    csv_path = os.path.join(BASE_PATH, csv_path)
    # 根据不同类型，选择不同的解析器
    if type == '1104':
        interface.register_parser("simple", SimpleExcelParser)
        result = interface.extraction_report("simple", csv_path, file_path, '')
    elif type == 'djz':
        # todo 大集中的解析方式
        interface.register_parser("gc", GCExcelParser)
        result = interface.extraction_report("gc", csv_path, file_path, '')
    elif type == 'survey':
        # todo  survey的解析方式
        result = {}
    else:
        raise ValueError("Invalid type")
    return result


def get_report_data_list(data_dict: dict):
    merged_list = []
    entry_id = count(1)  # 从 1 开始计数

    for key in data_dict:
        for item in data_dict[key]:
            merged_list.append({
                **item,
                "entry_id": str(next(entry_id))
            })

    return merged_list


if __name__ == '__main__':
    # file_path = '/data/ideal/code/hr_code/hsbc_1_data/test_file/G1402/2、G14_II填报说明-mk-1119.doc'
    # csv_path = '/data/ideal/code/hr_code/hsbc_1_data/test_file/G1402/G14_II_template.xls'
    # print(get_report_data_list(get_report_data(file_path, csv_path, '1104')))

    file_path = 'A3304/金融统计制度汇编（2023版）印刷.pdf'
    csv_path = 'A3304/A3304.xls'
    print(get_report_data_list(get_report_data(file_path, csv_path, 'djz')))
