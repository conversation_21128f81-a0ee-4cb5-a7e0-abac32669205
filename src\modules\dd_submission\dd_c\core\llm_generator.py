"""
DD-B大模型生成器

为低置信度策略中需要重新生成的字段集成大模型生成功能
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field

from pipeline.factory import PipelineFactory
from pipeline.core.context import PipelineContext
from modules.dd_submission.dd_b.infrastructure.models import (
    DDBRecord,
    FieldFillInfo,
    FieldStatusEnum
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBConstants
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBProcessingError,
    handle_async_ddb_errors,
    create_processing_error
)

logger = logging.getLogger(__name__)


@dataclass
class LLMGenerationRequest:
    """大模型生成请求"""
    
    record_id: Optional[int]                    # 记录ID
    fields_to_generate: List[str]               # 需要生成的字段
    context_data: Dict[str, Any]                # 上下文数据
    
    # 生成配置
    temperature: float = 0.7                    # 生成温度
    max_tokens: int = 512                       # 最大token数
    timeout_seconds: float = 30.0               # 超时时间
    
    # 重试配置
    max_retries: int = 3                        # 最大重试次数
    retry_delay: float = 1.0                    # 重试延迟


@dataclass
class LLMGenerationResult:
    """大模型生成结果"""
    
    request: LLMGenerationRequest               # 原始请求
    
    # 生成结果
    generated_fields: Dict[str, str] = field(default_factory=dict)  # 生成的字段值
    generation_success: bool = False            # 生成是否成功
    
    # 生成信息
    generation_time_ms: float = 0.0             # 生成耗时
    tokens_used: int = 0                        # 使用的token数
    model_name: str = ""                        # 使用的模型名称
    
    # 错误信息
    error_message: str = ""                     # 错误消息
    retry_count: int = 0                        # 重试次数
    
    # 生成说明
    generation_notes: List[str] = field(default_factory=list)  # 生成说明


class ConcurrencyManager:
    """并发管理器 - 支持LLM最大15并发量"""
    
    def __init__(self, max_concurrent: int = 15):
        """
        初始化并发管理器
        
        Args:
            max_concurrent: 最大并发数量
        """
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_tasks = 0
        self.total_requests = 0
        self.completed_requests = 0
        self.failed_requests = 0
        
        logger.info(f"并发管理器初始化: 最大并发量={max_concurrent}")
    
    async def acquire(self):
        """获取并发许可"""
        await self.semaphore.acquire()
        self.active_tasks += 1
        self.total_requests += 1
        logger.debug(f"获取并发许可: 当前活跃任务={self.active_tasks}")
    
    def release(self, success: bool = True):
        """释放并发许可"""
        self.semaphore.release()
        self.active_tasks -= 1
        
        if success:
            self.completed_requests += 1
        else:
            self.failed_requests += 1
        
        logger.debug(f"释放并发许可: 当前活跃任务={self.active_tasks}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取并发统计信息"""
        return {
            "max_concurrent": self.max_concurrent,
            "active_tasks": self.active_tasks,
            "total_requests": self.total_requests,
            "completed_requests": self.completed_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.completed_requests / max(self.total_requests, 1) * 100
        }


class LLMGenerator:
    """大模型生成器"""
    
    def __init__(self, concurrency_manager: ConcurrencyManager = None):
        """
        初始化大模型生成器
        
        Args:
            concurrency_manager: 并发管理器
        """
        self.concurrency_manager = concurrency_manager or ConcurrencyManager()
        
        # 初始化Pipeline工厂（准备集成大模型框架）
        self.pipeline_factory = PipelineFactory()
        
        # 生成配置
        self.default_model = "opentrek"
        self.generation_fields = DDBConstants.MAIN_GENERATION_FIELDS
        
        logger.info("大模型生成器初始化完成")
    
    @handle_async_ddb_errors
    async def generate_fields(self, request: LLMGenerationRequest) -> LLMGenerationResult:
        """
        生成字段内容
        
        Args:
            request: 生成请求
            
        Returns:
            生成结果
        """
        start_time = time.time()
        
        # 获取并发许可
        await self.concurrency_manager.acquire()
        
        try:
            logger.info(f"开始生成字段: record_id={request.record_id}, fields={request.fields_to_generate}")
            
            # 构建生成结果
            result = LLMGenerationResult(
                request=request,
                model_name=self.default_model
            )
            
            # 执行字段生成（当前为模拟实现，后续集成真实Pipeline）
            generated_fields = await self._generate_fields_with_pipeline(request)
            
            # 更新结果
            result.generated_fields = generated_fields
            result.generation_success = len(generated_fields) > 0
            result.generation_time_ms = (time.time() - start_time) * 1000
            result.tokens_used = self._estimate_tokens_used(generated_fields)
            result.generation_notes = [
                f"生成字段数: {len(generated_fields)}",
                f"使用模型: {self.default_model}",
                f"生成耗时: {result.generation_time_ms:.2f}ms"
            ]
            
            logger.info(f"字段生成完成: record_id={request.record_id}, "
                       f"成功={result.generation_success}, "
                       f"耗时={result.generation_time_ms:.2f}ms")
            
            # 释放并发许可
            self.concurrency_manager.release(success=True)
            
            return result
            
        except Exception as e:
            # 释放并发许可
            self.concurrency_manager.release(success=False)
            
            generation_time = (time.time() - start_time) * 1000
            logger.error(f"字段生成失败: record_id={request.record_id}, error={e}")
            
            return LLMGenerationResult(
                request=request,
                generation_success=False,
                generation_time_ms=generation_time,
                error_message=str(e),
                generation_notes=[f"生成失败: {str(e)}"]
            )
    
    async def _generate_fields_with_pipeline(self, request: LLMGenerationRequest) -> Dict[str, str]:
        """
        使用Pipeline框架生成字段（准备集成点）
        
        Args:
            request: 生成请求
            
        Returns:
            生成的字段值字典
        """
        # TODO: 集成真实的Pipeline框架
        # 当前为模拟实现，展示集成架构
        
        generated_fields = {}
        
        # 模拟生成逻辑（注意字段名大小写）
        for field_name in request.fields_to_generate:
            if field_name.lower() == "bdr09":
                generated_fields[field_name] = "生成的数据项名称"
            elif field_name.lower() == "bdr10":
                generated_fields[field_name] = "生成的数据项描述"
            elif field_name.lower() == "bdr11":
                generated_fields[field_name] = "生成的数据项详情"
            elif field_name.lower() == "bdr16":
                generated_fields[field_name] = "生成的数据项备注"
            else:
                generated_fields[field_name] = f"生成的{field_name}内容"
        
        # 模拟生成延迟
        await asyncio.sleep(0.1)
        
        return generated_fields
    
    def _estimate_tokens_used(self, generated_fields: Dict[str, str]) -> int:
        """估算使用的token数量"""
        total_chars = sum(len(value) for value in generated_fields.values())
        # 简单估算：中文字符数 * 1.5
        return int(total_chars * 1.5)
    
    @handle_async_ddb_errors
    async def batch_generate_fields(
        self, 
        requests: List[LLMGenerationRequest]
    ) -> List[LLMGenerationResult]:
        """
        批量生成字段
        
        Args:
            requests: 生成请求列表
            
        Returns:
            生成结果列表
        """
        logger.info(f"开始批量生成字段: {len(requests)} 个请求")
        
        # 创建并发任务
        tasks = [
            self.generate_fields(request)
            for request in requests
        ]
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"批量生成中第 {i} 个请求失败: {result}")
                # 创建失败结果
                failed_result = LLMGenerationResult(
                    request=requests[i],
                    generation_success=False,
                    error_message=str(result),
                    generation_notes=[f"批量生成异常: {str(result)}"]
                )
                processed_results.append(failed_result)
            else:
                processed_results.append(result)
        
        logger.info(f"批量生成完成: {len(processed_results)} 个结果")
        
        return processed_results
    
    def get_concurrency_stats(self) -> Dict[str, Any]:
        """获取并发统计信息"""
        return self.concurrency_manager.get_stats()


class FieldGenerationIntegrator:
    """字段生成集成器 - 将生成结果集成到记录中"""
    
    def __init__(self, llm_generator: LLMGenerator):
        """
        初始化字段生成集成器
        
        Args:
            llm_generator: 大模型生成器
        """
        self.llm_generator = llm_generator
    
    @handle_async_ddb_errors
    async def integrate_generated_fields(
        self,
        record: DDBRecord,
        fields_to_generate: List[str],
        context_data: Dict[str, Any] = None
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        集成生成的字段到记录中
        
        Args:
            record: 原始记录
            fields_to_generate: 需要生成的字段
            context_data: 上下文数据
            
        Returns:
            (更新后的记录, 填充信息列表)
        """
        from copy import deepcopy
        
        updated_record = deepcopy(record)
        fill_details = []
        
        if not fields_to_generate:
            return updated_record, fill_details
        
        try:
            # 构建生成请求
            generation_request = LLMGenerationRequest(
                record_id=record.id,
                fields_to_generate=fields_to_generate,
                context_data=context_data or {}
            )
            
            # 执行字段生成
            generation_result = await self.llm_generator.generate_fields(generation_request)
            
            # 集成生成结果
            if generation_result.generation_success:
                for field_name, generated_value in generation_result.generated_fields.items():
                    if hasattr(updated_record, field_name):
                        original_value = getattr(updated_record, field_name)
                        setattr(updated_record, field_name, generated_value)
                        
                        # 创建填充信息
                        fill_info = FieldFillInfo(
                            field_name=field_name,
                            original_value=original_value,
                            filled_value=generated_value,
                            status=FieldStatusEnum.FILLED,
                            fill_reason=f"大模型生成 (模型: {generation_result.model_name}, "
                                      f"耗时: {generation_result.generation_time_ms:.2f}ms)"
                        )
                        fill_details.append(fill_info)
                        
                        logger.debug(f"字段生成集成: {field_name} = {generated_value}")
            else:
                logger.error(f"字段生成失败: {generation_result.error_message}")
                raise create_processing_error(
                    f"字段生成失败: {generation_result.error_message}",
                    processing_step="llm_generation",
                    record_id=record.id
                )
            
            logger.info(f"字段生成集成完成: record_id={record.id}, "
                       f"生成字段数={len(generation_result.generated_fields)}")
            
            return updated_record, fill_details
            
        except Exception as e:
            logger.error(f"字段生成集成失败: record_id={record.id}, error={e}")
            raise create_processing_error(
                f"字段生成集成失败: {str(e)}",
                processing_step="llm_integration",
                record_id=record.id
            )


# 便捷函数
def create_llm_generator(max_concurrent: int = 15) -> LLMGenerator:
    """创建大模型生成器实例"""
    concurrency_manager = ConcurrencyManager(max_concurrent)
    return LLMGenerator(concurrency_manager)


def create_field_generation_integrator(llm_generator: LLMGenerator = None) -> FieldGenerationIntegrator:
    """创建字段生成集成器实例"""
    if llm_generator is None:
        llm_generator = create_llm_generator()
    return FieldGenerationIntegrator(llm_generator)


async def generate_fields_for_record(
    record: DDBRecord,
    fields_to_generate: List[str],
    context_data: Dict[str, Any] = None,
    max_concurrent: int = 15
) -> Tuple[DDBRecord, List[FieldFillInfo]]:
    """为记录生成字段的便捷函数"""
    llm_generator = create_llm_generator(max_concurrent)
    integrator = create_field_generation_integrator(llm_generator)
    return await integrator.integrate_generated_fields(record, fields_to_generate, context_data)
