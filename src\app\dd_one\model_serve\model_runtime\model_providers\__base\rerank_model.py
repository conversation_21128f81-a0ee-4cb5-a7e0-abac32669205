from typing import Optional
import time

from app.dd_one.model_serve.model_runtime.entities.model_entities import ModelType
from app.dd_one.model_serve.model_runtime.entities.rerank_entities import RerankResult
from app.dd_one.model_serve.model_runtime.model_providers.__base.ai_model import AIModel


class RerankModel(AIModel):
    """
    Base Model class for rerank model.
    """

    model_type: ModelType = ModelType.RERANK

    def invoke(
            self,
            model: str,
            credentials: dict,
            query: str,
            docs: list[str],
            score_threshold: Optional[float] = None,
            top_n: Optional[int] = None,
            user: Optional[str] = None,
    ) -> RerankResult:
        """
        Invoke rerank model

        :param model: model name
        :param credentials: model credentials
        :param query: search query
        :param docs: docs for reranking
        :param score_threshold: score threshold
        :param top_n: top n
        :param user: unique user id
        :return: rerank result
        """
        try:
            result = self.invoke_rerank(
                model=model,
                credentials=credentials,
                query=query,
                docs=docs,
                score_threshold=score_threshold,
                top_n=top_n,
                user=user
            )

            return result
        except Exception as e:
            raise self._transform_invoke_error(e)
        raise NotImplementedError()

    def invoke_rerank(
            self,
            model: str,
            credentials: dict,
            query: str,
            docs: list[str],
            score_threshold: Optional[float] = None,
            top_n: Optional[int] = None,
            user: Optional[str] = None,
    ):
        raise NotImplementedError

    async def ainvoke(
            self,
            model: str,
            credentials: dict,
            query: str,
            docs: list[str],
            score_threshold: Optional[float] = None,
            top_n: Optional[int] = None,
            user: Optional[str] = None,
    ) -> RerankResult:
        """
        异步调用重排序模型

        :param model: 模型名称
        :param credentials: 模型凭证
        :param query: 搜索查询
        :param docs: 需要重排序的文档列表
        :param score_threshold: 分数阈值
        :param top_n: 返回前 N 个结果
        :param user: 用户ID
        :return: 重排序结果
        """

        try:
            result = await self.ainvoke_rerank(
                tenant_id=self.tenant_id,
                user_id=user or "unknown",
                provider=self.provider_name,
                model=model,
                credentials=credentials,
                query=query,
                docs=docs,
                score_threshold=score_threshold,
                top_n=top_n,
            )
            return result
        except Exception as e:
            raise self._transform_invoke_error(e)

    async def ainvoke_rerank(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            query: str,
            docs: list[str],
            score_threshold: Optional[float] = None,
            top_n: Optional[int] = None,
    ) -> RerankResult:
        """
        异步调用重排序模型进行推理（需子类实现）

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 模型提供者名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param query: 搜索查询
        :param docs: 需要重排序的文档列表
        :param score_threshold: 分数阈值
        :param top_n: 返回前 N 个结果
        :return: 重排序结果
        """
        raise NotImplementedError()