#!/usr/bin/env python3
"""
DD-B推荐处理器测试

专注于测试推荐逻辑，输出推荐的bdr01-bdr17字段数据用于调试：
1. 简化的输入参数（report_code、dept_id、entry_id、entry_type）
2. 完整的推荐流程可视化
3. 详细的调试信息输出
4. 推荐结果格式验证
"""

import asyncio
import os
import sys
import time
import json
from typing import Dict, Any

# 设置项目根目录
project_root = os.getcwd()
sys.path.insert(0, project_root)

# 设置日志
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="dd_b_recommend_test",
    level="INFO"
)


async def get_database_clients():
    """获取数据库客户端"""
    try:
        from service import get_client

        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        logger.info("✅ RDB客户端获取成功")

        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None

        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            logger.info("✅ VDB客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ VDB客户端获取失败: {e}")

        try:
            embedding_client = await get_client('model.embeddings.moka-m3e-base')
            logger.info("✅ Embedding客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ Embedding客户端获取失败: {e}")

        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }

    except Exception as e:
        logger.error(f"❌ 数据库客户端获取失败: {e}")
        raise


async def test_dd_b_recommend_processor():
    """测试DD-B推荐处理器"""
    print("\n🚀 开始DD-B推荐处理器测试")
    print("=" * 60)

    try:
        # 1. 获取数据库客户端
        logger.info("1️⃣ 获取数据库客户端...")
        clients = await get_database_clients()

        # 2. 导入推荐处理器
        logger.info("2️⃣ 导入推荐处理器...")
        from modules.dd_submission.dd_b.dd_b_recommend_processor import (
            recommend_dd_b,
            DDBRecommendProcessor,
            RecommendResult
        )

        # 3. 设置测试参数
        logger.info("3️⃣ 设置测试参数...")
        test_cases = [
            {
                "name": "标准推荐测试用例",
                "entry_id": "1004",
                "report_code": "S71_ADS_RELEASE_V0"
            }
        ]

        # 4. 执行测试
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print(f"  entry_id: {test_case['entry_id']}")
            print(f"  report_code: {test_case['report_code']}")
            print("-" * 50)

            # 执行推荐处理
            start_time = time.time()

            result = await recommend_dd_b(
                rdb_client=clients['rdb_client'],
                entry_id=test_case['entry_id'],
                report_code=test_case['report_code'],
                vdb_client=clients['vdb_client'],
                embedding_client=clients['embedding_client']
            )

            processing_time = time.time() - start_time

            # 输出测试结果
            await display_test_results(result, processing_time)

            # 保存调试数据
            await save_debug_data(result, test_case)

        return True

    except Exception as e:
        logger.error(f"推荐处理器测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")
        return False


async def display_test_results(result: Any, processing_time: float):
    """显示测试结果"""
    print(f"\n📊 处理结果:")
    print(f"  状态: {'✅ 成功' if result.success else '❌ 失败'}")
    print(f"  entry_id: {result.entry_id}")
    print(f"  entry_type: {result.entry_type}")
    print(f"  处理耗时: {result.processing_time:.2f}s")
    print(f"  实际耗时: {processing_time:.2f}s")

    # 显示调试信息
    if result.debug_info:
        print(f"\n🔍 调试信息:")
        for key, value in result.debug_info.items():
            print(f"  {key}: {value}")

    # 显示错误信息
    if result.errors:
        print(f"\n⚠️ 错误信息:")
        for i, error in enumerate(result.errors[:3], 1):
            print(f"  {i}. {error}")
        if len(result.errors) > 3:
            print(f"  ... 还有{len(result.errors) - 3}个错误")

    # 显示推荐数据
    if result.recommended_data:
        print(f"\n📋 推荐数据:")
        print(f"  entry_id: {result.recommended_data.get('entry_id')}")
        print(f"  entry_type: {result.recommended_data.get('entry_type')}")

        # 显示BDR字段（分组显示）
        bdr_groups = [
            (range(1, 6), "BDR01-BDR05"),
            (range(6, 11), "BDR06-BDR10"),
            (range(11, 16), "BDR11-BDR15"),
            (range(16, 18), "BDR16-BDR17")
        ]

        for bdr_range, group_name in bdr_groups:
            print(f"\n  {group_name}:")
            for i in bdr_range:
                bdr_field = f"bdr{i:02d}"
                value = result.recommended_data.get(bdr_field, '')
                display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {bdr_field}: {display_value}")

        # 验证字段完整性
        expected_fields = ["entry_id", "entry_type"] + [f"bdr{i:02d}" for i in range(1, 18)]
        actual_fields = list(result.recommended_data.keys())
        missing_fields = set(expected_fields) - set(actual_fields)
        extra_fields = set(actual_fields) - set(expected_fields)

        print(f"\n🔍 字段验证:")
        print(f"  期望字段数: {len(expected_fields)}")
        print(f"  实际字段数: {len(actual_fields)}")

        if missing_fields:
            print(f"  ❌ 缺失字段: {missing_fields}")
        if extra_fields:
            print(f"  ℹ️ 额外字段: {extra_fields}")
        if not missing_fields:
            print(f"  ✅ 字段完整性验证通过")
    else:
        print(f"\n❌ 未获取到推荐数据")


async def save_debug_data(result: Any, test_case: Dict[str, str]):
    """保存调试数据到文件"""
    try:
        # 创建调试数据目录
        debug_dir = "debug_output"
        os.makedirs(debug_dir, exist_ok=True)

        # 生成文件名
        timestamp = int(time.time())
        filename = f"dd_b_recommend_debug_{test_case['entry_id']}_{test_case['report_code']}_{timestamp}.json"
        filepath = os.path.join(debug_dir, filename)

        # 准备调试数据
        debug_data = {
            "test_case": test_case,
            "result_summary": {
                "success": result.success,
                "entry_id": result.entry_id,
                "entry_type": result.entry_type,
                "processing_time": result.processing_time,
                "debug_info": result.debug_info,
                "errors": result.errors
            },
            "recommended_data": result.recommended_data
        }

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 调试数据已保存: {filepath}")
        print(f"  文件大小: {os.path.getsize(filepath)} bytes")
        print(f"  推荐字段数: {len(result.recommended_data)}")

    except Exception as e:
        logger.error(f"保存调试数据失败: {e}")


async def test_recommend_performance():
    """测试推荐处理性能"""
    print("\n⚡ 推荐处理性能测试")
    print("=" * 40)

    try:
        clients = await get_database_clients()

        # 测试多个不同的entry_id
        test_cases = [
            {
                "entry_id": "perf_test_001",
                "report_code": "S71_ADS_RELEASE_V0"
            },
            {
                "entry_id": "perf_test_002",
                "report_code": "S71_ADS_RELEASE_V0"
            },
            {
                "entry_id": "perf_test_003",
                "report_code": "S71_ADS_RELEASE_V0"
            }
        ]

        print(f"📋 性能测试参数:")
        print(f"  测试用例数: {len(test_cases)}")
        print(f"  report_code: {test_cases[0]['report_code']}")

        performance_results = []

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔄 测试用例 {i}: {test_case['entry_id']}")

            start_time = time.time()

            from modules.dd_submission.dd_b.dd_b_recommend_processor import recommend_dd_b

            result = await recommend_dd_b(
                rdb_client=clients['rdb_client'],
                entry_id=test_case['entry_id'],
                report_code=test_case['report_code'],
                vdb_client=clients['vdb_client'],
                embedding_client=clients['embedding_client']
            )

            total_time = time.time() - start_time

            performance_results.append({
                'test_case': test_case['entry_id'],
                'entry_id': result.entry_id,
                'entry_type': result.entry_type,
                'total_time': total_time,
                'processing_time': result.processing_time,
                'success': result.success,
                'has_data': bool(result.recommended_data)
            })

            print(f"  耗时: {total_time:.2f}s")
            print(f"  成功: {'✅' if result.success else '❌'}")
            print(f"  推荐entry_id: {result.entry_id}")
            print(f"  推荐entry_type: {result.entry_type}")
            print(f"  有数据: {'✅' if result.recommended_data else '❌'}")

        # 显示性能对比
        print(f"\n📊 性能对比:")
        print(f"{'测试用例':<20} {'Entry ID':<15} {'Entry Type':<12} {'总耗时(s)':<12} {'成功':<6} {'有数据':<8}")
        print("-" * 80)

        for perf in performance_results:
            success_mark = '✅' if perf['success'] else '❌'
            data_mark = '✅' if perf['has_data'] else '❌'
            print(f"{perf['test_case']:<20} {perf['entry_id']:<15} {perf['entry_type']:<12} {perf['total_time']:<12.2f} "
                  f"{success_mark:<6} {data_mark:<8}")

        # 计算平均性能
        successful_results = [p for p in performance_results if p['success']]
        if successful_results:
            avg_time = sum(p['total_time'] for p in successful_results) / len(successful_results)
            print(f"\n🏆 平均处理时间: {avg_time:.2f}s (成功用例)")

    except Exception as e:
        logger.error(f"性能测试失败: {e}")


async def test_recommend_flow_visualization():
    """测试推荐流程可视化"""
    print("\n🔍 推荐流程可视化测试")
    print("=" * 40)

    try:
        clients = await get_database_clients()

        from modules.dd_submission.dd_b.dd_b_recommend_processor import DDBRecommendProcessor

        processor = DDBRecommendProcessor(
            rdb_client=clients['rdb_client'],
            vdb_client=clients['vdb_client'],
            embedding_client=clients['embedding_client']
        )

        # 执行处理并观察每个步骤
        print(f"📋 推荐流程步骤:")
        print(f"  1. 查询数据 (query_data)")
        print(f"  2. 向量搜索推荐 (vector_search_recommend)")
        print(f"  3. 格式化推荐输出 (format_recommend_output)")

        result = await processor.recommend_logic(
            entry_id="flow_test_001",
            report_code="S71_ADS_RELEASE_V0"
        )

        print(f"\n📊 推荐流程结果:")
        print(f"  最终成功: {'✅' if result.success else '❌'}")
        print(f"  推荐完整性: {'✅' if result.recommended_data else '❌'}")
        print(f"  字段数量: {len(result.recommended_data) if result.recommended_data else 0}")

    except Exception as e:
        logger.error(f"推荐流程可视化测试失败: {e}")


async def main():
    """主函数"""
    print("DD-B推荐处理器完整测试")
    print("=" * 80)

    try:
        # 推荐功能测试
        success = await test_dd_b_recommend_processor()

        # if success:
        #     # 性能测试
        #     await test_recommend_performance()

        #     # 推荐流程可视化测试
        #     await test_recommend_flow_visualization()

        print("\n" + "=" * 80)
        print("🎉 所有测试完成!")

        if success:
            print("✅ DD-B推荐处理器工作正常")
            print("💡 请查看debug_output目录中的调试数据文件")
        else:
            print("❌ DD-B推荐处理器存在问题")

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
