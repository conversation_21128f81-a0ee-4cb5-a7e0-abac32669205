#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：utils.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/11 15:24 
@Desc    ：工具函数
"""

def save_dict_to_txt(data: dict, file_path: str) -> None:
    """将字典保存为文本文件，格式为 key=value"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for key, value in data.items():
            f.write(f"{key}={value}\n")  # 每行写入 "key=value"


def print_dict(d):
    for k, v in d.items():
        print(f"{k}:{v}")

def print_list(l):
    for item in l:
        print(f"{item}")