"""
DD-B模块常量定义

定义DD-B模块使用的常量、配置和工具函数
"""

from typing import Dict, List, Any
from enum import Enum


class DDBConstants:
    """DD-B模块常量"""
    
    # 数据库表名
    TABLE_NAME = "biz_dd_post_distribution"
    
    # 主要检查字段（统一使用小写）
    MAIN_CHECK_FIELDS = ["bdr09", "bdr10", "bdr11", "bdr16"]

    # 需要填充的字段组（统一使用小写）
    FILL_GROUP_1_FIELDS = ["bdr05", "bdr06", "bdr07", "bdr08"]
    FILL_GROUP_2_FIELDS = ["bdr12", "bdr13", "bdr14", "bdr15", "bdr17"]

    # 所有BDR字段（用于高置信度完全填充）
    ALL_BDR_FIELDS = ["bdr05", "bdr06", "bdr07", "bdr08", "bdr09", "bdr10", "bdr11", "bdr12", "bdr13", "bdr14", "bdr15", "bdr16", "bdr17"]

    # 需要重新生成的主要字段（低置信度时）
    MAIN_GENERATION_FIELDS = ["bdr09", "bdr10", "bdr11", "bdr16"]

    # 历史信息提取相关表名
    PRE_DISTRIBUTION_TABLE = "biz_dd_pre_distribution"
    SUBMISSION_DATA_TABLE = "dd_submission_data"

    # 置信度阈值配置
    HIGH_CONFIDENCE_THRESHOLD = 0.6  # 高置信度阈值 (≥60%)
    MIN_SEARCH_SCORE = 0.1          # 最小搜索分数
    DEFAULT_SEARCH_LIMIT = 10       # 默认搜索限制
    
    # 默认填充值
    DEFAULT_FILL_VALUES = {
        # 第一组默认值（统一使用小写）
        "bdr05": "",  # bdr05默认为空
        "bdr06": "不适用",
        "bdr07": "1",
        "bdr08": "CRD",

        # 第二组默认值（统一使用小写）
        "bdr12": "不适用",
        "bdr13": "不适用",
        "bdr14": "不适用",
        "bdr15": "不适用",
        "bdr17": "不适用"
    }
    
    # 查询配置
    DEFAULT_QUERY_LIMIT = 1000
    MAX_QUERY_LIMIT = 5000
    QUERY_TIMEOUT_SECONDS = 30
    
    # 处理配置
    DEFAULT_BATCH_SIZE = 100
    MAX_BATCH_SIZE = 500
    PROCESSING_TIMEOUT_SECONDS = 120
    
    # 验证配置
    VALIDATION_TIMEOUT_SECONDS = 60
    MAX_VALIDATION_ERRORS = 100
    
    # 缓存配置
    CACHE_TTL_SECONDS = 300  # 5分钟
    MAX_CACHE_SIZE = 1000
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class DDBFieldTypes:
    """DD-B字段类型定义"""
    
    # 字段类型映射（统一使用小写）
    FIELD_TYPES = {
        "id": "int",
        "report_code": "str",
        "dept_id": "str",
        "bdr09": "str",
        "bdr10": "str",
        "bdr11": "str",
        "bdr16": "str",
        "bdr06": "str",
        "bdr07": "str",
        "bdr08": "str",
        "bdr12": "str",
        "bdr13": "str",
        "bdr14": "str",
        "bdr15": "str",
        "bdr17": "str",
        "create_time": "datetime",
        "update_time": "datetime"
    }
    
    # 必填字段
    REQUIRED_FIELDS = ["report_code", "dept_id"]
    
    # 可为空字段（统一使用小写）
    NULLABLE_FIELDS = [
        "bdr09", "bdr10", "bdr11", "bdr16",
        "bdr06", "bdr07", "bdr08",
        "bdr12", "bdr13", "bdr14", "bdr15", "bdr17"
    ]


class DDBErrorCodes:
    """DD-B错误码定义"""
    
    # 通用错误
    UNKNOWN_ERROR = "DDB_E001"
    VALIDATION_ERROR = "DDB_E002"
    DATABASE_ERROR = "DDB_E003"
    TIMEOUT_ERROR = "DDB_E004"
    
    # 参数错误
    INVALID_REPORT_CODE = "DDB_E101"
    INVALID_DEPT_ID = "DDB_E102"
    MISSING_REQUIRED_FIELD = "DDB_E103"
    INVALID_FIELD_TYPE = "DDB_E104"
    
    # 数据错误
    NO_RECORDS_FOUND = "DDB_E201"
    DATA_INCONSISTENCY = "DDB_E202"
    FIELD_VALUE_ERROR = "DDB_E203"
    
    # 处理错误
    PROCESSING_FAILED = "DDB_E301"
    FILL_OPERATION_FAILED = "DDB_E302"
    BATCH_PROCESSING_FAILED = "DDB_E303"


class DDBUtils:
    """DD-B工具函数"""
    
    @staticmethod
    def is_field_empty(value: Any) -> bool:
        """检查字段是否为空"""
        if value is None:
            return True
        if isinstance(value, str):
            return not value.strip()
        return False
    
    @staticmethod
    def normalize_report_code(report_code: str) -> str:
        """标准化报表代码（不修改原始代码）"""
        if not report_code:
            return ""

        # 直接返回原始报表代码，不进行任何修改
        # 移除强制添加前缀的逻辑，保持用户输入的原始格式
        return report_code.strip()
    
    @staticmethod
    def extract_version_from_report_code(report_code: str) -> str:
        """从报表代码中提取版本号"""
        if not report_code:
            return ""
        
        normalized = report_code.lower()
        if normalized.startswith("g0107_beta_"):
            return normalized[11:]  # 去掉 "g0107_beta_" 前缀
        
        return report_code  # 如果不是标准格式，直接返回
    
    @staticmethod
    def validate_dept_id(dept_id: str) -> bool:
        """验证部门ID格式"""
        if not dept_id or not dept_id.strip():
            return False
        
        # 基本格式检查（可以根据实际需求调整）
        dept_id = dept_id.strip()
        if len(dept_id) < 2 or len(dept_id) > 50:
            return False
        
        return True
    
    @staticmethod
    def get_fill_reason(field_name: str, original_value: Any) -> str:
        """获取字段填充原因"""
        if DDBUtils.is_field_empty(original_value):
            if field_name in DDBConstants.FILL_GROUP_1_FIELDS:
                return f"字段 {field_name} 为空，使用第一组默认填充规则"
            elif field_name in DDBConstants.FILL_GROUP_2_FIELDS:
                return f"字段 {field_name} 为空，使用第二组默认填充规则"
            else:
                return f"字段 {field_name} 为空，使用默认填充值"
        else:
            return f"字段 {field_name} 已有值，无需填充"
    
    @staticmethod
    def build_query_conditions(report_code: str, dept_id: str) -> Dict[str, Any]:
        """
        构建查询条件

        重要：数据库表biz_dd_post_distribution使用version字段存储报表代码，
        因此需要将report_code映射到version字段
        """
        conditions = {}

        if report_code:
            # 标准化报表代码
            normalized_code = DDBUtils.normalize_report_code(report_code)
            # 关键修复：将report_code映射到数据库的version字段
            conditions["version"] = normalized_code

        if dept_id:
            conditions["dept_id"] = dept_id.strip()

        return conditions
    
    @staticmethod
    def get_default_fill_value(field_name: str) -> str:
        """获取字段的默认填充值"""
        return DDBConstants.DEFAULT_FILL_VALUES.get(field_name, "不适用")
    
    @staticmethod
    def format_processing_time(time_ms: float) -> str:
        """格式化处理时间"""
        if time_ms < 1000:
            return f"{time_ms:.2f}ms"
        elif time_ms < 60000:
            return f"{time_ms/1000:.2f}s"
        else:
            return f"{time_ms/60000:.2f}min"
    
    @staticmethod
    def get_field_groups() -> Dict[str, List[str]]:
        """获取字段分组"""
        return {
            "main_check": DDBConstants.MAIN_CHECK_FIELDS,
            "fill_group_1": DDBConstants.FILL_GROUP_1_FIELDS,
            "fill_group_2": DDBConstants.FILL_GROUP_2_FIELDS
        }
    
    @staticmethod
    def validate_record_data(record_data: Dict[str, Any]) -> List[str]:
        """验证记录数据"""
        errors = []
        
        # 检查必填字段
        for field in DDBFieldTypes.REQUIRED_FIELDS:
            if field not in record_data or DDBUtils.is_field_empty(record_data[field]):
                errors.append(f"必填字段 {field} 缺失或为空")
        
        # 检查字段类型（简单检查）
        for field, expected_type in DDBFieldTypes.FIELD_TYPES.items():
            if field in record_data and record_data[field] is not None:
                value = record_data[field]
                if expected_type == "str" and not isinstance(value, str):
                    errors.append(f"字段 {field} 类型错误，期望 str，实际 {type(value).__name__}")
                elif expected_type == "int" and not isinstance(value, int):
                    errors.append(f"字段 {field} 类型错误，期望 int，实际 {type(value).__name__}")
        
        return errors
