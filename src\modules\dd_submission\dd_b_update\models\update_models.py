"""
DD-B更新模块的数据模型
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import json


class UpdateStrategy(Enum):
    """更新策略枚举"""
    SIMPLE_UPDATE = "simple_update"           # 简单字段更新
    BDR10_UPDATE = "bdr10_update"            # BDR10更新
    COLUMN_SELECTION = "column_selection"     # 从字段选择开始的Pipeline
    SQL_GENERATION = "sql_generation"        # 从SQL生成开始的Pipeline
    BDR16_ANALYSIS = "bdr16_analysis"        # BDR16解析更新
    RANGE_CALCULATION = "range_calculation"  # Range自动计算


@dataclass
class UpdateRequest:
    """更新请求数据模型"""
    report_code: str  # 对应version
    dept_id: str
    data: List[Dict[str, Any]]  # 包含entry_id和修改字段的列表
    
    def __post_init__(self):
        """验证数据格式"""
        if not self.report_code or not self.dept_id:
            raise ValueError("report_code和dept_id不能为空")
        
        if not self.data or not isinstance(self.data, list):
            raise ValueError("data必须是非空列表")
        
        for item in self.data:
            if not isinstance(item, dict) or 'entry_id' not in item:
                raise ValueError("data中每个项目必须包含entry_id")


@dataclass
class UpdateItem:
    """单个更新项目"""
    entry_id: str
    entry_type: str = "ITEM"  # 条目类型：TABLE/ITEM
    original_data: Dict[str, Any] = field(default_factory=dict)  # 原始数据
    update_fields: Dict[str, Any] = field(default_factory=dict)  # 要更新的字段
    strategy: Optional[UpdateStrategy] = None
    frontend_entry_type: bool = False  # 是否来自前端的entry_type
    
    def get_pipeline_fields(self) -> Dict[str, Any]:
        """获取Pipeline相关字段"""
        pipeline_fields = {}
        for field_name, value in self.update_fields.items():
            if field_name.upper() in ['BDR09', 'BDR10', 'BDR11', 'BDR16']:
                pipeline_fields[field_name.upper()] = value
        return pipeline_fields
    
    def get_simple_fields(self) -> Dict[str, Any]:
        """获取简单更新字段"""
        simple_fields = {}
        for field_name, value in self.update_fields.items():
            if field_name.upper() not in ['BDR09', 'BDR10', 'BDR11', 'BDR16']:
                simple_fields[field_name] = value
        return simple_fields


@dataclass
class PipelineExecutionParams:
    """Pipeline执行参数"""
    user_question: str
    hint: str = ""
    db_type: str = "mysql"
    candidate_tables: Optional[Dict[str, List[str]]] = None
    candidate_columns: Optional[Dict[str, List[str]]] = None
    schema_generation_params: Optional[Dict[str, Any]] = None
    table_ids: Optional[List[str]] = None


@dataclass
class UpdateResult:
    """更新结果"""
    entry_id: str
    success: bool
    strategy: UpdateStrategy
    entry_type: str = "ITEM"  # 条目类型：TABLE/ITEM
    updated_fields: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    pipeline_result: Optional[Dict[str, Any]] = None
    execution_time: float = 0.0


@dataclass
class BatchUpdateResult:
    """批量更新结果"""
    total_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    results: List[UpdateResult] = field(default_factory=list)
    execution_time: float = 0.0
    
    def add_result(self, result: UpdateResult):
        """添加单个结果"""
        self.results.append(result)
        self.total_count += 1
        if result.success:
            self.success_count += 1
        else:
            self.failed_count += 1
    
    def get_summary(self) -> Dict[str, Any]:
        """获取结果摘要"""
        return {
            "total": self.total_count,
            "success": self.success_count,
            "failed": self.failed_count,
            "success_rate": self.success_count / self.total_count if self.total_count > 0 else 0,
            "execution_time": self.execution_time
        }


class BDR16Parser:
    """BDR16字段解析器"""
    
    @staticmethod
    def extract_table_range(bdr16_content: str) -> Optional[List[str]]:
        """
        从BDR16内容中提取表范围

        Args:
            bdr16_content: BDR16字段内容

        Returns:
            表名列表，如果解析失败返回None

        Example:
            输入: "表范围：['adm_lon_accumulative_amt', 'bdm_acc_payment_sched']\\n条件：..."
            输出: ['adm_lon_accumulative_amt', 'bdm_acc_payment_sched']
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.debug(f"🔍 BDR16解析: 输入内容={repr(bdr16_content)}")

        if not bdr16_content or not isinstance(bdr16_content, str):
            logger.debug("  → 内容为空或非字符串，返回None")
            return None

        try:
            # 查找"表范围："后的内容
            if "表范围：" in bdr16_content:
                logger.debug("  → 找到'表范围：'标记")
                # 提取表范围行
                lines = bdr16_content.split('\n')
                logger.debug(f"  → 分割后的行数: {len(lines)}")

                for i, line in enumerate(lines):
                    logger.debug(f"    行{i}: {repr(line)}")
                    if line.strip().startswith("表范围："):
                        logger.debug(f"  → 找到表范围行: {repr(line)}")
                        # 提取方括号内的内容
                        start_idx = line.find('[')
                        end_idx = line.find(']')
                        logger.debug(f"  → 方括号位置: start={start_idx}, end={end_idx}")

                        if start_idx != -1 and end_idx != -1:
                            table_list_str = line[start_idx:end_idx+1]
                            logger.debug(f"  → 提取的JSON字符串: {repr(table_list_str)}")
                            # 使用json.loads解析列表
                            import json
                            table_list = json.loads(table_list_str)
                            logger.debug(f"  → 解析结果: {table_list}")
                            result = table_list if isinstance(table_list, list) else None
                            logger.debug(f"  → 最终返回: {result}")
                            return result
                        else:
                            logger.debug("  → 未找到完整的方括号")
            else:
                logger.debug("  → 未找到'表范围：'标记")

            logger.debug("  → 解析失败，返回None")
            return None
        except Exception as e:
            logger.debug(f"  → 解析异常: {e}")
            return None
    
    @staticmethod
    def compare_table_ranges(old_bdr16: str, new_bdr16: str) -> bool:
        """
        比较两个BDR16的表范围是否相同

        Args:
            old_bdr16: 原始BDR16内容
            new_bdr16: 新的BDR16内容

        Returns:
            True表示表范围相同，False表示不同
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.debug(f"🔍 BDR16表范围比较:")
        logger.debug(f"  原始BDR16: {repr(old_bdr16)}")
        logger.debug(f"  新BDR16: {repr(new_bdr16)}")

        old_tables = BDR16Parser.extract_table_range(old_bdr16)
        new_tables = BDR16Parser.extract_table_range(new_bdr16)

        logger.debug(f"  原始表列表: {old_tables}")
        logger.debug(f"  新表列表: {new_tables}")

        if old_tables is None and new_tables is None:
            logger.debug("  → 两者都为None，返回True")
            return True

        if old_tables is None or new_tables is None:
            logger.debug("  → 其中一个为None，返回False")
            return False

        # 比较表列表（忽略顺序）
        result = set(old_tables) == set(new_tables)
        logger.debug(f"  → 集合比较结果: {result}")
        return result


@dataclass
class FieldAnalysisResult:
    """字段分析结果"""
    strategy: UpdateStrategy
    pipeline_required: bool
    affected_fields: List[str] = field(default_factory=list)
    pipeline_params: Optional[PipelineExecutionParams] = None
    reason: str = ""
