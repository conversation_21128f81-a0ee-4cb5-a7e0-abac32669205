"""
Knowledge V2 API - 搜索功能路由

基于向量和文本的混合搜索API
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query

from ..models.request_models import MetadataSearchRequest
from ..models.response_models import (
    MetadataSearchResponse,
    MetadataSearchResultItem,
    ErrorResponse
)
from ..dependencies import (
    get_metadata_crud,
    handle_api_errors,
    PerformanceMonitor
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/search", tags=["搜索功能V2"])


@router.post("/", response_model=MetadataSearchResponse, summary="元数据混合搜索")
@handle_api_errors
async def search_metadata(
    request: MetadataSearchRequest,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    元数据混合搜索
    
    - **query**: 搜索查询字符串
    - **knowledge_id**: 知识库ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    - **search_type**: 搜索类型 (vector/text/hybrid)
    - **limit**: 返回结果数量限制
    - **min_score**: 最小相似度分数
    - **vector_weight**: 向量搜索权重
    - **text_weight**: 文本搜索权重
    
    支持向量搜索、文本搜索和混合搜索模式
    """
    with PerformanceMonitor("元数据搜索") as monitor:
        try:
            # 根据搜索类型执行不同的搜索策略
            if request.search_type == "vector":
                # 纯向量搜索
                results = await _vector_search(metadata_crud, request)
            elif request.search_type == "text":
                # 纯文本搜索
                results = await _text_search(metadata_crud, request)
            else:
                # 混合搜索（默认）
                results = await _hybrid_search(metadata_crud, request)
            
            # 过滤结果
            filtered_results = []
            for result in results:
                if result.get('score', 0) >= request.min_score:
                    filtered_results.append(MetadataSearchResultItem(
                        score=result.get('score', 0),
                        entity_type=result.get('entity_type', 'unknown'),
                        entity_data=result.get('entity_data', {}),
                        vector_info=result.get('vector_info')
                    ))
            
            # 限制结果数量
            filtered_results = filtered_results[:request.limit]
            
            return MetadataSearchResponse(
                success=True,
                message=f"搜索完成，找到 {len(filtered_results)} 条结果",
                results=filtered_results,
                total_found=len(filtered_results),
                search_time=monitor.execution_time,
                search_params=request.dict()
            )
            
        except Exception as e:
            logger.error(f"元数据搜索失败: {e}")
            return MetadataSearchResponse(
                success=False,
                message=f"搜索失败: {str(e)}",
                results=[],
                total_found=0,
                search_time=monitor.execution_time,
                search_params=request.dict()
            )


@router.get("/databases", response_model=MetadataSearchResponse, summary="搜索数据库")
@handle_api_errors
async def search_databases(
    query: str = Query(..., description="搜索查询"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID"),
    limit: int = Query(10, description="结果数量限制", ge=1, le=100),
    min_score: float = Query(0.5, description="最小相似度", ge=0.0, le=1.0),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    搜索数据库（便捷接口）
    
    专门用于搜索数据库实体的简化接口
    """
    request = MetadataSearchRequest(
        query=query,
        knowledge_id=knowledge_id,
        entity_type="database",
        limit=limit,
        min_score=min_score
    )
    
    return await search_metadata(request, metadata_crud)


@router.get("/tables", response_model=MetadataSearchResponse, summary="搜索表")
@handle_api_errors
async def search_tables(
    query: str = Query(..., description="搜索查询"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID"),
    limit: int = Query(10, description="结果数量限制", ge=1, le=100),
    min_score: float = Query(0.5, description="最小相似度", ge=0.0, le=1.0),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    搜索表（便捷接口）
    
    专门用于搜索表实体的简化接口
    """
    request = MetadataSearchRequest(
        query=query,
        knowledge_id=knowledge_id,
        entity_type="table",
        limit=limit,
        min_score=min_score
    )
    
    return await search_metadata(request, metadata_crud)


@router.get("/columns", response_model=MetadataSearchResponse, summary="搜索字段")
@handle_api_errors
async def search_columns(
    query: str = Query(..., description="搜索查询"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID"),
    limit: int = Query(10, description="结果数量限制", ge=1, le=100),
    min_score: float = Query(0.5, description="最小相似度", ge=0.0, le=1.0),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    搜索字段（便捷接口）
    
    专门用于搜索字段实体的简化接口
    """
    request = MetadataSearchRequest(
        query=query,
        knowledge_id=knowledge_id,
        entity_type="column",
        limit=limit,
        min_score=min_score
    )
    
    return await search_metadata(request, metadata_crud)


# ==================== 内部搜索函数 ====================

async def _vector_search(metadata_crud, request: MetadataSearchRequest) -> List[dict]:
    """执行向量搜索"""
    try:
        # 这里需要调用向量搜索方法
        # 由于当前CRUD中可能没有专门的搜索方法，我们先返回空结果
        logger.info(f"执行向量搜索: {request.query}")
        return []
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        return []


async def _text_search(metadata_crud, request: MetadataSearchRequest) -> List[dict]:
    """执行文本搜索"""
    try:
        # 这里需要调用文本搜索方法
        # 可以使用数据库的LIKE查询或全文搜索
        logger.info(f"执行文本搜索: {request.query}")
        
        results = []
        
        # 搜索数据库
        if not request.entity_type or request.entity_type == "database":
            db_conditions = {"db_name": {"$like": f"%{request.query}%"}}
            if request.knowledge_id:
                db_conditions["knowledge_id"] = request.knowledge_id
            
            db_results = await metadata_crud.batch_query_source_databases([db_conditions])
            for db_list in db_results:
                for db in db_list:
                    results.append({
                        "score": 0.8,  # 文本匹配分数
                        "entity_type": "database",
                        "entity_data": db
                    })
        
        # 搜索表
        if not request.entity_type or request.entity_type == "table":
            table_conditions = {"table_name": {"$like": f"%{request.query}%"}}
            if request.knowledge_id:
                table_conditions["knowledge_id"] = request.knowledge_id
            
            table_results = await metadata_crud.batch_query_source_tables([table_conditions])
            for table_list in table_results:
                for table in table_list:
                    results.append({
                        "score": 0.8,
                        "entity_type": "table",
                        "entity_data": table
                    })
        
        # 搜索字段
        if not request.entity_type or request.entity_type == "column":
            column_conditions = {"column_name": {"$like": f"%{request.query}%"}}
            if request.knowledge_id:
                column_conditions["knowledge_id"] = request.knowledge_id
            
            column_results = await metadata_crud.batch_query_source_columns([column_conditions])
            for column_list in column_results:
                for column in column_list:
                    results.append({
                        "score": 0.8,
                        "entity_type": "column",
                        "entity_data": column
                    })
        
        return results
        
    except Exception as e:
        logger.error(f"文本搜索失败: {e}")
        return []


async def _hybrid_search(metadata_crud, request: MetadataSearchRequest) -> List[dict]:
    """执行混合搜索"""
    try:
        # 执行向量搜索和文本搜索
        vector_results = await _vector_search(metadata_crud, request)
        text_results = await _text_search(metadata_crud, request)
        
        # 合并结果并重新计算分数
        combined_results = []
        
        # 向量搜索结果
        for result in vector_results:
            result["score"] = result.get("score", 0) * request.vector_weight
            combined_results.append(result)
        
        # 文本搜索结果
        for result in text_results:
            result["score"] = result.get("score", 0) * request.text_weight
            combined_results.append(result)
        
        # 按分数排序
        combined_results.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        return combined_results
        
    except Exception as e:
        logger.error(f"混合搜索失败: {e}")
        return []
