"""
DD-C核心处理器

简化的DD-C数据处理功能：
1. 输入：report_code + dept_id
2. 查询：post表获取所有记录
3. 处理：调用指标平台工具获取index信息
4. 输出：包含SDR01-SDR15字段和index的结构化数据

核心流程：
查询post表 → 遍历记录 → 调用指标平台工具 → 格式化输出
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class DDCProcessResult:
    """DD-C处理结果"""
    success: bool = False
    report_code: str = ""
    dept_id: str = ""
    processing_time: float = 0.0
    
    # 处理后的数据
    result_data: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    total_records: int = 0
    processed_records: int = 0
    
    # 调试信息和错误
    debug_info: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)


class DDCCoreProcessor:
    """DD-C核心处理器"""
    
    def __init__(self, rdb_client: Any):
        """
        初始化DD-C核心处理器
        
        Args:
            rdb_client: RDB客户端 (get_client('database.rdbs.mysql'))
        """
        self.rdb_client = rdb_client
        logger.info(f"DD-C核心处理器初始化完成")

    async def process_core_logic(
        self,
        report_code: str,
        dept_id: str
    ) -> DDCProcessResult:
        """
        执行DD-C核心处理逻辑
        
        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID
            
        Returns:
            DDCProcessResult: 处理结果
        """
        start_time = time.time()
        result = DDCProcessResult(report_code=report_code, dept_id=dept_id)

        try:
            logger.info(f"🚀 开始DD-C核心处理: report_code={report_code}, dept_id={dept_id}")

            # 1. 查询post表获取所有记录
            logger.info("1️⃣ 查询post表数据...")
            query_start_time = time.time()
            records = await self._query_post_distribution_data(report_code, dept_id)
            query_end_time = time.time()
            logger.info(f"1️⃣ 查询post表完成: 找到{len(records)}条记录, 耗时={query_end_time-query_start_time:.2f}s")

            result.total_records = len(records)
            result.debug_info['records_found'] = len(records)
            
            if not records:
                logger.warning("未找到任何记录")
                result.success = False
                return result
            
            # 2. 处理每条记录
            logger.info("2️⃣ 处理记录并调用指标平台...")
            process_start_time = time.time()
            processed_items = await self._process_records(records)
            process_end_time = time.time()
            logger.info(f"2️⃣ 记录处理完成: 处理{len(processed_items)}条记录, 耗时={process_end_time-process_start_time:.2f}s")
            
            result.processed_records = len(processed_items)
            
            # 3. 格式化最终输出
            logger.info("3️⃣ 格式化输出...")
            result.result_data = {
                "report_code": report_code,
                "dept_id": dept_id,
                "item": processed_items
            }
            
            result.success = True
            logger.info(f"✅ DD-C核心处理完成: 总记录{result.total_records}, 处理{result.processed_records}")
            
        except Exception as e:
            error_msg = f"DD-C核心处理失败: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            result.success = False
        
        result.processing_time = time.time() - start_time
        return result
    
    async def _query_post_distribution_data(
        self,
        report_code: str,
        dept_id: str
    ) -> List[Dict[str, Any]]:
        """查询post_distribution表数据"""
        try:
            # 使用DD CRUD查询
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)
            
            # 构建查询条件（参考dd_b_core_processor的方法）
            from modules.dd_submission.dd_b.infrastructure.constants import DDBUtils
            conditions = DDBUtils.build_query_conditions(report_code, dept_id)
            logger.info(f"查询条件: {conditions}")
            
            # 批量查询post_distribution表
            conditions_list = [conditions]
            records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions_list,
                batch_size=1000,  # 一次查询较多记录
                max_concurrency=1,
                timeout_per_batch=60.0
            )
            
            logger.info(f"post_distribution查询完成: 找到{len(records)}条记录")
            return records
            
        except Exception as e:
            logger.error(f"post_distribution数据查询失败: {e}")
            return []
    
    async def _process_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理记录列表，调用指标平台工具"""
        processed_items = []

        try:
            # 导入指标平台工具
            from modules.dd_submission.dd_c.utils.get_indicator_platform import get_indicator_platform
            # 导入DD CRUD用于查询pre表
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            for i, record in enumerate(records):
                try:
                    logger.debug(f"处理第{i+1}/{len(records)}条记录: submission_id={record.get('submission_id')}")

                    # 1. 获取pre_distribution_id并查询pre表获取dr09和dr17
                    pre_distribution_id = record.get('pre_distribution_id')
                    dr09, dr17 = '', ''

                    if pre_distribution_id:
                        logger.debug(f"查询pre表: pre_distribution_id={pre_distribution_id}")
                        pre_conditions = [{"id": pre_distribution_id}]
                        pre_records = await dd_crud.batch_query_pre_distributions(
                            conditions_list=pre_conditions,
                            batch_size=1,
                            max_concurrency=1
                        )

                        if pre_records:
                            pre_record = pre_records[0]
                            dr09 = pre_record.get('dr09', '')
                            dr17 = pre_record.get('dr17', '')
                            logger.debug(f"获取到dr字段: dr09='{dr09}', dr17='{dr17}'")
                        else:
                            logger.warning(f"未找到pre_distribution_id={pre_distribution_id}的记录")
                    else:
                        logger.warning(f"记录{i+1}缺少pre_distribution_id")

                    # 2. 准备传给指标平台的item数据（添加必要字段）
                    item_for_platform = {
                        **record,  # 复制所有原始字段
                        'type': record.get('submission_type', 'SUBMISSION'),  # 添加type字段
                        'dr09': dr09,  # 添加dr09字段
                        'dr17': dr17   # 添加dr17字段
                    }

                    # 3. 调用指标平台工具获取index信息
                    index_info = get_indicator_platform(item_for_platform)

                    # 4. 转换entry_type
                    entry_type = self._convert_entry_type(record.get('submission_type', ''))

                    # 5. 构建输出项
                    item_data = {
                        "entry_id": record.get('submission_id', ''),
                        "entry_type": entry_type
                    }

                    # 6. 添加SDR01-SDR15字段
                    for j in range(1, 16):  # SDR01 到 SDR15
                        sdr_field = f"sdr{j:02d}"
                        item_data[sdr_field] = record.get(sdr_field, "")

                    # 7. 添加index信息
                    item_data["index"] = index_info

                    processed_items.append(item_data)

                except Exception as e:
                    logger.error(f"处理第{i+1}条记录失败: {e}")
                    # 继续处理下一条记录
                    continue

            logger.info(f"记录处理完成: 成功处理{len(processed_items)}/{len(records)}条记录")
            return processed_items

        except Exception as e:
            logger.error(f"批量处理记录失败: {e}")
            return []
    
    def _convert_entry_type(self, submission_type: str) -> str:
        """
        转换entry_type（与DD-B保持一致的规则）
        
        Args:
            submission_type: 原始submission_type
            
        Returns:
            str: 转换后的entry_type
        """
        try:
            if submission_type == 'RANGE':
                entry_type = 'TABLE'
            elif submission_type == 'SUBMISSION':
                entry_type = 'ITEM'
            else:
                # 默认值
                entry_type = 'ITEM'
                logger.warning(f"未知的submission_type: {submission_type}, 使用默认值ITEM")
            
            logger.debug(f"Entry类型转换: submission_type={submission_type} → entry_type={entry_type}")
            return entry_type
            
        except Exception as e:
            logger.error(f"转换entry_type失败: {e}")
            return 'ITEM'


async def process_dd_c(
    rdb_client: Any,
    report_code: str,
    dept_id: str
) -> DDCProcessResult:
    """
    DD-C处理便捷函数
    
    Args:
        rdb_client: RDB客户端
        report_code: 报告代码（version）
        dept_id: 部门ID
        
    Returns:
        DDCProcessResult: 处理结果
    """
    processor = DDCCoreProcessor(rdb_client=rdb_client)
    return await processor.process_core_logic(report_code, dept_id)
