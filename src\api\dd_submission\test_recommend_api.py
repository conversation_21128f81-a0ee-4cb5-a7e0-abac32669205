#!/usr/bin/env python3
"""
DD-B推荐API简单测试
"""

import asyncio
import json
import httpx


async def test_recommend_api():
    """测试DD-B推荐API"""

    BASE_URL = "http://localhost:30337"

    # 请求参数
    request_data = {
        "report_code": "S71_ADS_RELEASE_V0",
        "entry_id": "1004"
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            print("🚀 测试DD-B推荐API")
            print(f"请求: {json.dumps(request_data, ensure_ascii=False)}")

            response = await client.post(
                f"{BASE_URL}/api/dd/dd-b/recommend",
                json=request_data
            )

            if response.status_code == 200:
                result = response.json()
                print("✅ 成功!")
                print(f"entry_id: {result.get('entry_id')}")
                print(f"entry_type: {result.get('entry_type')}")

                # 显示BDR字段
                for i in range(1, 18):
                    bdr_field = f"bdr{i:02d}"
                    value = result.get(bdr_field, '')
                    if value:
                        print(f"{bdr_field}: {str(value)[:50]}")
            else:
                print(f"❌ 失败: {response.status_code}")
                print(response.text)

        except Exception as e:
            print(f"❌ 异常: {e}")


async def main():
    """主函数"""
    await test_recommend_api()


if __name__ == "__main__":
    asyncio.run(main())
