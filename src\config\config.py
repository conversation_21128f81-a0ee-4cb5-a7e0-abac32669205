qwen_llm_config = {
    "model": "qwen_vllm",
    "credentials": {"provider": "opentrack", "host": "**************", "port": "30167",
                    "base_url": "http://**************:30167/v1/chat/completions", "api_key": "empty"},
    "model_parameters": {
        "max_tokens": 10000,
        "temperature": 0.9
    },
    "stream": False
}
style_json_path = r"/data/ideal/code/hr_code/test_dd_file/excel_data_with_style.json"


_project_prompt = '''
# 任务要求

我需要你根据我提供的逻辑规则信息，将其转换为一个符合特定格式的 JSON 列表。每个 JSON 对象包含五个属性：
`type`：表示适用的列名（如 "A"、"B"、"C"）或 `"all"` 表示适用于所有列。
`val` : 表示完整的逻辑表达式
`project_id`: 仅保留数字部分（如 `13.C` 的 `project_id` 是 `"13"`），如果支持全部项目，这个为空
`is_all_project`: 布尔值，是否支持全部的项目 true或者false
`remove_project_ids`: 不被支持的一些项目id，只在我们is_all_project为true时启用，用于标识部分不使用的project_id

规则说明：
请严格按照输入内容进行解析，不要自行添加、修改或推测逻辑。
如果指定列（如“仅适用于C列”），则"type": "C"或者用户指定了[id.C] op [....]也是C。
如果规则中定义了多个行（如“适用于[1.]、[2.]行”），则需分别生成对应的 JSON 对象，如果对比我们所有的project_id发现只缺失某几个项目，则启用is_all_project跟remove_project_ids来标识。
如果project_id人家指定了多级序号，你也要展示多级序号，不要只保留一级
如果用户有些描述指定了除了xx不在的，我们要使用is_all_project跟remove_project_ids来标识。
注意project_id只有前面的序号，如果用户指定了12.A这种，那project_id只是12，A体现在type中
当我们is_all_project为true时，则代表这个规则是针对全部项目的，那project_id的内容就应该为空，通过remove_project_ids来标识弃用哪些项目的id
如果用户标识了适用于全部列，那我们的type是all，且is_all_project为false。
如果解析数据没有任何可以提取的规则，那就直接返回一个空列表即可
如果用户的要求是适用于xx-xx行，需要去对比用户给出的所有的project_id来给出结果,当出现remove_project_ids时，一定要跟用户要求仔细对比
注意识别type，当用户已经明确指定对应的类型，不要直接给all
项目前的 G01_I_2_这种格式的标签不要移除，这个标签不参与实际的project_id,这行实际参与的project_id是标签后面[]中的。
当项目超过8个时，我们在使用is_all_project标签，当小于8个时，我们将所有列展开出来即可

## 输入示例 1
```
[61.1]≥[26.1]+[27.1]；
[62.2.3]≥[6.1]；
[63.]≤[25.]+[24.]，仅适用于C列
[64.1.1]≤[49.1]（适用于A、B列）
[C]=[A]+[B]；本校验关系适用于[1.]、[2.]行
[13.C]=[13.A]-[13.B]
[1.]=[2.]-[13.]（本关系不覆盖[C]列）
G0421_I_1_[13.A]=G06_I[2.3E]
所有project_id[61.1,62,63,64,1,2,13]
所有的列A,B,C
```

## 输出示例 1
```json
[
  {"type":"all", "val":"[61.1]≥[26.1]+[27.1]", "project_id":"61.1", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"all", "val":"[62.2.3]≥[6.1]", "project_id":"62.3", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"C", "val":"[63.C]≤[25.C]+[24.C]", "project_id":"63", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"A", "val":"[64.1.1A]≤[49.1A]", "project_id":"64.1.1", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"B", "val":"[64.1.1B]≤[49.B]", "project_id":"64.1.1", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"C", "val":"[1.C]=[1.A]+[1.B]", "project_id":"1", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"C", "val":"[2.C]=[2.A]+[2.B]", "project_id":"2", "is_all_project":"false", "remove_project_ids":[]},
  {"type":"C", "val":"[13.C]=[13.A]-[13.B]", "project_id":"13", "is_all_project":"false", "remove_project_ids":[]}
  {"type":"A", "val":"[1.A]=[2.A]-[13.A]", "project_id":"1", "is_all_project":"false", "remove_project_ids":[]}
  {"type":"B", "val":"[1.A]=[2.A]-[13.A]", "project_id":"1", "is_all_project":"false", "remove_project_ids":[]}
  {"type":"A", "val":"G0421_I_1_[13.A]=G06_I[2.3E]", "project_id":"13", "is_all_project":"false", "remove_project_ids":[]}
]
```

## 输入示例 2
```
A≥B，适用于除[1.1][2.2]以外的各行
E=C+A,（本关系不覆盖[1.]、[1.1.]、[1.2]、[2.3]）
所有project_id[1,1.1,1.2,1.3,2,2.1,2.2,2.3]
所有的列A,B,C,E
```

## 输出示例 2 ```json [ {"type":"A", "val":"[A]≥[B]", "project_id":"", "is_all_project":"true", "remove_project_ids":[
"1.1","2.2"]} {"type":"E", "val":"[E]≥[C]+[A]", "project_id":"", "is_all_project":"true", "remove_project_ids":["1",
"1.1","1.2","2.3"]} ] ```


当前任务
请对以下输入进行解析，并输出符合上述格式的 JSON 列表：
<解析数据>
{project_data}
<解析数据>
这是最终输出可以参考的所有project_id[{project_ids}]
这是最终输出可以参考的所有的列[{valid_column}]

注意：只需输出最终的 JSON 结果，不需要额外解释。
'''


hsbc_data_path = "前后端存储数据的地址（前面那部分）"

TMP_PATH = r"/data/ideal/code/hr_code/hsbc_1_data/tmp"
BASE_PATH = r"/data/ideal/code/hr_code/hsbc_1_data/test_file"  # 请替换为实际的固定路径