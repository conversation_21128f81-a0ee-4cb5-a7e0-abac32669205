"""
DD-B模块核心业务逻辑

提供DD-B模块的核心处理功能：
- 数据处理引擎
- 字段填充引擎
"""

# 数据处理引擎
from .data_processor import (
    DataProcessor,
    create_data_processor,
    process_dd_b_request
)

# 字段填充引擎
from .field_filler import (
    FieldFiller,
    create_field_filler,
    fill_single_record,
    fill_multiple_records
)

# 历史信息连接器
from .history_connector import (
    HistoryInfoExtractor,
    OptimizedVectorSearchEngine,
    GenerationDecisionMaker,
    HistoryConnector,
    create_history_connector,
    extract_and_decide_single
)

# 大模型生成器
from .llm_generator import (
    LLMGenerator,
    FieldGenerationIntegrator,
    ConcurrencyManager,
    create_llm_generator,
    create_field_generation_integrator,
    generate_fields_for_record
)

# 并发处理器
from .concurrent_processor import (
    ConcurrentProcessor,
    TaskQueue,
    ResourcePool,
    create_concurrent_processor,
    create_and_start_processor
)

# 全局并发控制管理器
from .global_concurrency_manager import (
    GlobalConcurrencyManager,
    ConcurrencyConfig,
    ConcurrencyScope,
    get_global_concurrency_manager,
    create_concurrency_config
)

# Pipeline集成器
from .pipeline_integrator import (
    PipelineIntegrator,
    PipelineRequest,
    PipelineResult,
    FieldMappingResult,
    create_pipeline_integrator,
    execute_pipeline_for_record
)

# 增强数据处理器
from .enhanced_data_processor import (
    EnhancedDataProcessor,
    create_enhanced_data_processor,
    process_dd_b_request_enhanced
)

__all__ = [
    # 数据处理引擎
    "DataProcessor",
    "create_data_processor",
    "process_dd_b_request",

    # 字段填充引擎
    "FieldFiller",
    "create_field_filler",
    "fill_single_record",
    "fill_multiple_records",

    # 历史信息连接器
    "HistoryInfoExtractor",
    "OptimizedVectorSearchEngine",
    "GenerationDecisionMaker",
    "HistoryConnector",
    "create_history_connector",
    "extract_and_decide_single",

    # 大模型生成器
    "LLMGenerator",
    "FieldGenerationIntegrator",
    "ConcurrencyManager",
    "create_llm_generator",
    "create_field_generation_integrator",
    "generate_fields_for_record",

    # 并发处理器
    "ConcurrentProcessor",
    "TaskQueue",
    "ResourcePool",
    "create_concurrent_processor",
    "create_and_start_processor",

    # 全局并发控制管理器
    "GlobalConcurrencyManager",
    "ConcurrencyConfig",
    "ConcurrencyScope",
    "get_global_concurrency_manager",
    "create_concurrency_config",

    # Pipeline集成器
    "PipelineIntegrator",
    "PipelineRequest",
    "PipelineResult",
    "FieldMappingResult",
    "create_pipeline_integrator",
    "execute_pipeline_for_record",

    # 增强数据处理器
    "EnhancedDataProcessor",
    "create_enhanced_data_processor",
    "process_dd_b_request_enhanced"
]
