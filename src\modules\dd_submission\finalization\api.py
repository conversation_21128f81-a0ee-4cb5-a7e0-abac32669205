"""
DD提交数据最终确定API接口

提供统一的API接口处理前端最终确定的数据。
"""

from typing import Any, Dict, List, Literal
import logging
from .service import FinalizationService

logger = logging.getLogger(__name__)


class FinalizationAPI:
    """DD提交数据最终确定API"""
    
    def __init__(self, dd_crud):
        """
        初始化API
        
        Args:
            dd_crud: DD CRUD操作实例
        """
        self.service = FinalizationService(dd_crud)
    
    async def finalize_data(
        self,
        report_code: str,
        step: Literal["EXTRACTION", "DUTY", "BIZ", "TECH"],
        data: List[Dict[str, Any]],
        strategy: Literal["delete_insert", "batch_update"] = "delete_insert"
    ) -> Dict[str, Any]:
        """
        统一的数据最终确定接口
        
        Args:
            report_code: 报表代码（如 "G0107_beta_<version>"）
            step: 解读阶段（EXTRACTION/DUTY/BIZ/TECH）
            data: 前端提供的数据列表
            strategy: 处理策略（delete_insert/batch_update）
            
        Returns:
            Dict[str, Any]: 操作结果
            {
                "success": bool,
                "step": str,
                "strategy": str,
                "affected_records": int,
                "message": str
            }
        """
        try:
            logger.info(f"开始处理数据最终确定: report_code={report_code}, "
                       f"step={step}, 数据量={len(data)}, 策略={strategy}")
            
            # 根据不同阶段调用对应的处理方法
            if step == "EXTRACTION":
                result = await self.service.finalize_extraction_data(
                    report_code, data, strategy
                )
            elif step == "DUTY":
                result = await self.service.finalize_duty_data(
                    report_code, data, strategy
                )
            elif step == "BIZ":
                result = await self.service.finalize_biz_data(
                    report_code, data, strategy
                )
            elif step == "TECH":
                result = await self.service.finalize_tech_data(
                    report_code, data, strategy
                )
            else:
                raise ValueError(f"不支持的解读阶段: {step}")
            
            # 添加步骤信息到结果中
            result["step"] = step
            
            logger.info(f"数据最终确定完成: step={step}, success={result['success']}, "
                       f"affected_records={result['affected_records']}")
            
            return result
            
        except Exception as e:
            logger.error(f"数据最终确定失败: step={step}, error={e}")
            return {
                "success": False,
                "step": step,
                "strategy": strategy,
                "affected_records": 0,
                "message": f"操作失败: {e}"
            }


# 便捷函数，用于快速创建API实例
def create_finalization_api(dd_crud) -> FinalizationAPI:
    """
    创建数据最终确定API实例
    
    Args:
        dd_crud: DD CRUD操作实例
        
    Returns:
        FinalizationAPI: API实例
    """
    return FinalizationAPI(dd_crud)
