#!/usr/bin/env python3
"""
检查数据库中的实际数据

快速检查biz_dd_post_distribution表中的数据，
找到有效的version和dept_id值用于测试。
"""

import asyncio
import os
import sys

# 设置项目根目录
project_root = os.getcwd()
sys.path.insert(0, project_root)

from service import get_client
from modules.knowledge.dd.crud import DDCrud


async def check_database_data():
    """检查数据库中的实际数据"""
    print("🔍 检查数据库中的实际数据...")
    
    try:
        # 获取数据库客户端
        rdb_client = await get_client('database.rdbs.mysql')
        dd_crud = DDCrud(rdb_client)
        
        # 1. 检查表是否存在且有数据
        print("\n1️⃣ 检查表数据...")
        try:
            empty_conditions = [{}]
            all_records = await dd_crud.batch_query_post_distributions(
                conditions_list=empty_conditions,
                batch_size=10,
                max_concurrency=1
            )
            print(f"📊 biz_dd_post_distribution表总记录数（前10条）: {len(all_records)}")
            
            if not all_records:
                print("❌ 表中没有数据")
                return
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return
        
        # 2. 显示前几条记录的关键字段
        print("\n2️⃣ 前5条记录的关键字段:")
        for i, record in enumerate(all_records[:5], 1):
            print(f"  记录{i}:")
            print(f"    id: {record.get('id')}")
            print(f"    submission_id: {record.get('submission_id')}")  # 检查submission_id
            print(f"    version: {record.get('version')}")
            print(f"    dept_id: {record.get('dept_id')}")
            print(f"    submission_type: {record.get('submission_type')}")
            dr09 = record.get('dr09', '')
            dr17 = record.get('dr17', '')
            print(f"    dr09: {dr09[:50]}{'...' if len(dr09) > 50 else ''}")
            print(f"    dr17: {dr17[:50]}{'...' if len(dr17) > 50 else ''}")
            print(f"    dr09为空: {'是' if not dr09.strip() else '否'}")
            print(f"    submission_id存在: {'是' if record.get('submission_id') else '否'}")
            print()
        
        # 3. 统计不同的version值
        print("3️⃣ 统计不同的version值:")
        version_counts = {}
        for record in all_records:
            version = record.get('version')
            if version:
                version_counts[version] = version_counts.get(version, 0) + 1
        
        print(f"  发现的version值:")
        for version, count in list(version_counts.items())[:10]:
            print(f"    {version}: {count}条记录")
        
        # 4. 统计不同的dept_id值
        print("\n4️⃣ 统计不同的dept_id值:")
        dept_id_counts = {}
        for record in all_records:
            dept_id = record.get('dept_id')
            if dept_id:
                dept_id_counts[dept_id] = dept_id_counts.get(dept_id, 0) + 1
        
        print(f"  发现的dept_id值:")
        for dept_id, count in list(dept_id_counts.items())[:10]:
            print(f"    {dept_id}: {count}条记录")
        
        # 5. 找到有效的测试数据（dr09不为空）
        print("\n5️⃣ 查找有效的测试数据（dr09不为空）:")
        valid_records = []
        for record in all_records:
            dr09 = record.get('dr09', '').strip()
            if dr09:  # dr09不为空
                valid_records.append(record)
        
        print(f"  有效记录数: {len(valid_records)}")
        
        if valid_records:
            print("  推荐的测试参数:")
            for i, record in enumerate(valid_records[:3], 1):
                print(f"    测试用例{i}:")
                print(f"      version: '{record.get('version')}'")
                print(f"      dept_id: '{record.get('dept_id')}'")
                print(f"      dr09: '{record.get('dr09', '')[:50]}...'")
                print()
        
        # 6. 使用第一个有效记录进行测试查询
        if valid_records:
            print("6️⃣ 使用有效数据进行测试查询:")
            test_record = valid_records[0]
            test_version = test_record.get('version')
            test_dept_id = test_record.get('dept_id')
            
            print(f"  测试参数: version='{test_version}', dept_id='{test_dept_id}'")
            
            try:
                test_conditions = [{'version': test_version, 'dept_id': test_dept_id}]
                test_results = await dd_crud.batch_query_post_distributions(
                    conditions_list=test_conditions,
                    batch_size=5,
                    max_concurrency=1
                )
                print(f"  ✅ 测试查询成功: 找到 {len(test_results)} 条记录")
                
                # 统计有效记录数
                valid_test_records = [r for r in test_results if r.get('dr09', '').strip()]
                print(f"  ✅ 其中有效记录（dr09不为空）: {len(valid_test_records)} 条")
                
            except Exception as e:
                print(f"  ❌ 测试查询失败: {e}")
        
        print("\n🎯 总结:")
        print(f"  总记录数: {len(all_records)}")
        print(f"  有效记录数（dr09不为空）: {len(valid_records)}")
        print(f"  不同version数: {len(version_counts)}")
        print(f"  不同dept_id数: {len(dept_id_counts)}")
        
        if valid_records:
            recommended = valid_records[0]
            print(f"\n🚀 推荐的测试参数:")
            print(f"  report_code = '{recommended.get('version')}'")
            print(f"  dept_id = '{recommended.get('dept_id')}'")
        else:
            print(f"\n❌ 没有找到有效的测试数据（所有记录的dr09都为空）")
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")


async def main():
    """主函数"""
    print("DD-B数据库数据检查")
    print("=" * 50)
    
    try:
        await check_database_data()
        print("\n" + "=" * 50)
        print("数据检查完成!")
        
    except Exception as e:
        print(f"\n❌ 数据检查失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
